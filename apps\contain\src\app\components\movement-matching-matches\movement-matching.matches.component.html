<p-card class="mt-20" [ngClass]="{'p-20': !isRunMovementMatchingPage}">
    <ng-template pTemplate="header">
        <h4>Matched Hires / Movements</h4>
    </ng-template>
    <contain-hire-request-filters
                                  *ngIf="!isRunMovementMatchingPage"
                                  [isMovementMatchingLists]="true">
    </contain-hire-request-filters>
    <p-table
             [columns]="listColumns()"
             [value]="rows!"
             [scrollable]="true"
             [paginator]="true"
             [rows]="20"
             [rowsPerPageOptions]="[5, 10, 20]">
        <ng-template pTemplate="header" let-columns>
            <tr>
                <th *ngFor="let column of columns" scope="col" [style.min-width.px]="column.width"
                    [style.width.%]="(column.width / tableWidth) * 100">
                    <span>{{ column.name }}</span>
                </th>
            </tr>
        </ng-template>
        <ng-template pTemplate="body" let-item>
            <tr (click)="navigateToFlowVoyage(item.voyageId)">
                <td>{{item.hireUnit }}</td>
                <td>{{item.movementUnit }}</td>
                <td>{{item.hireClientName }}</td>
                <td>{{item.movementClientName }}</td>
                <td>{{item.hireBillingAssetName }}</td>
                <td>{{item.movementAssetName }}</td>
                <td>{{voyageDirection[item.direction] }}</td>
                <td>{{item.completedDate | date:'dd/MM/yyyy HH:mm' }}</td>
                <td>{{item.sailingDate | date:'dd/MM/yyyy HH:mm' }}</td>
                <td>{{item.manifestNo }}</td>
                <td>{{item.movementVendorName }}</td>
                <td>{{item.matchedAt | date: 'dd/MM/yyyy HH:mm'}}</td>
                <td>
                    <i
                       title="View Match Details"
                       class="pi pi-info-circle"
                       (click)="openDetailsDialog(item, $event)">
                    </i>
                    <span (click)="$event.stopPropagation()" class="fs-16 ml-5"
                          *ngIf="item.isApproved && item.hasException" title="Manually Approved">(M)</span>
                </td>
            </tr>
        </ng-template>
    </p-table>
    <contain-movement-matching-details-dialog
    *ngIf="selectedMovementMatching && detailsDialogVisible"
    [movementMatching]="selectedMovementMatching"
    [dialogVisible]="detailsDialogVisible"
    (dialogToggle)="detailsDialogVisible = !detailsDialogVisible">
    </contain-movement-matching-details-dialog>
</p-card>