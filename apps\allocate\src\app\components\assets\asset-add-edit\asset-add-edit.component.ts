import {
  ChangeDetectionStrategy,
  Component,
  computed,
  DestroyRef,
  effect,
  inject,
  OnInit,
} from '@angular/core';
import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { DatePipe, NgIf } from '@angular/common';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

import { skip } from 'rxjs';
import { Store } from '@ngrx/store';

import { DialogModule } from 'primeng/dialog';
import { InputTextModule } from 'primeng/inputtext';
import { DropdownModule } from 'primeng/dropdown';
import { CalendarModule } from 'primeng/calendar';
import { MultiSelectModule } from 'primeng/multiselect';
import { InputSwitchModule } from 'primeng/inputswitch';
import { TooltipModule } from 'primeng/tooltip';

import { AssetsAllocateActions } from '../../../store/actions/assets.actions';
import { assetsAllocateFeature } from '../../../store/features/assets.feature';
import {
  assetsFeature,
  locationsFeature,
  operatorsFeature,
} from 'libs/services/src/lib/services/maintenance/store/features';
import { ArrayIncludesPipe } from 'libs/components/src/lib/pipes';
import { AssetActions } from 'libs/services/src/lib/services/maintenance/store/actions/assets.actions';
import { Asset } from 'libs/services/src/lib/services/maintenance/interfaces/asset.interface';
import { ConfirmService } from 'libs/components/src/lib/services/confirm.service';
import { stripTimezoneOffset } from 'libs/services/src/lib/services/functions/convert-date.utils';

@Component({
  standalone: true,
  selector: 'allocate-assets-add-edit',
  templateUrl: './asset-add-edit.component.html',
  imports: [
    DialogModule,
    ReactiveFormsModule,
    InputTextModule,
    MultiSelectModule,
    DropdownModule,
    CalendarModule,
    InputSwitchModule,
    ArrayIncludesPipe,
    NgIf,
    TooltipModule,
    DatePipe,
  ],
  providers: [DatePipe],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AssetsAddEditComponent implements OnInit {
  private readonly store = inject(Store);
  private readonly destroyRef = inject(DestroyRef);
  private readonly confirmService = inject(ConfirmService);
  private readonly datePipe = inject(DatePipe);

  isVisible = this.store.selectSignal(
    assetsAllocateFeature.selectIsVisibleAddEdit
  );
  asset = this.store.selectSignal(assetsAllocateFeature.selectAsset);
  assets = this.store.selectSignal(assetsFeature.selectAssets);
  assetTypesList = this.store.selectSignal(assetsFeature.selectAssetTypesList);
  locations = this.store.selectSignal(locationsFeature.selectLocations);
  operators = this.store.selectSignal(operatorsFeature.selectOperators);
  assetsHeads = computed(() => {
    return this.store
      .selectSignal(assetsFeature.selectAssetsHead)()
      .filter((item) => item.assetId !== this.asset()?.assetId);
  });

  form = new FormGroup({
    name: new FormControl('', [Validators.required]),
    assetType: new FormControl('', [Validators.required]),
    clientId: new FormControl('', Validators.required),
    startDateTime: new FormControl<Date | null>(null, [Validators.required]),
    endDateTime: new FormControl<Date | null>(null),
    clusterHeadId: new FormControl({
      value: '',
      disabled: true,
    }),
    addNewHistory: new FormControl<boolean>(false),
    locationIds: new FormControl<string[]>([], [Validators.required]),
  });
  initialValue = this.form.getRawValue();

  controls = {
    name: this.form.get('name'),
    assetType: this.form.get('assetType'),
    locationIds: this.form.get('locationIds'),
    endDateTime: this.form.get('endDateTime'),
    startDateTime: this.form.get('startDateTime'),
    clientId: this.form.get('clientId'),
    clusterHeadId: this.form.get('clusterHeadId'),
    addNewHistory: this.form.get('addNewHistory'),
  };

  locDependencies = {
    locType: ['POR', 'MOB'],
    locTypeOff: ['OFF', 'WEL'],
  };

  constructor() {
    effect(
      () => {
        if (this.isVisible()) {
          if (this.asset()) {
            this.form.patchValue({
              ...this.asset(),
              startDateTime: this.asset()?.startDateTime
                ? new Date(this.asset()!.startDateTime!)
                : null,
              endDateTime: this.asset()?.endDateTime
                ? new Date(this.asset()!.endDateTime!)
                : null,
            });
            this.controls.assetType?.disable();
            if (this.asset()?.clientId!) {
              this.store.dispatch(
                AssetActions.load_Cluster_Heads({ id: this.asset()?.clientId! })
              );
            }
          } else {
            this.controls.assetType?.enable();
          }
        }
      },
      {
        allowSignalWrites: true,
      }
    );
  }

  ngOnInit() {
    this.controls.clientId?.valueChanges
      .pipe(skip(1), takeUntilDestroyed(this.destroyRef))
      .subscribe((value) => {
        this.controls.clusterHeadId?.setValue('');
        if (value) {
          this.controls.clusterHeadId?.enable();
          this.store.dispatch(AssetActions.load_Cluster_Heads({ id: value }));
        } else {
          this.controls.clusterHeadId?.disable();
        }
      });

    this.controls.assetType?.valueChanges
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((value) => {
        if (this.locDependencies.locType.includes(value ?? '')) {
          this.controls.clientId?.disable();
          this.controls.clientId?.setValue(null);
          this.controls.startDateTime?.disable();
          this.controls.startDateTime?.setValue(null);
          this.controls.endDateTime?.disable();
          this.controls.endDateTime?.setValue(null);
        } else {
          this.controls.clientId?.enable();
          this.controls.startDateTime?.enable();
          this.controls.endDateTime?.enable();
        }
        if (!value || this.locDependencies.locType.includes(value ?? '')) {
          this.controls.clusterHeadId?.disable();
        }
      });
  }

  hideDialog() {
    this.store.dispatch(
      AssetsAllocateActions.change_visibility_add_edit({
        visible: false,
        asset: null,
      })
    );
    this.resetForm();
  }

  resetForm() {
    this.form.reset(this.initialValue);
    this.form.updateValueAndValidity();
  }

  onSubmit() {
    this.form.markAllAsTouched();

    if (this.form.valid) {
      const model = { ...(this.form.getRawValue() as unknown as Asset) };

      const endDate = model.endDateTime
        ? `${this.datePipe.transform(model.endDateTime, 'dd/MM/yyyy HH:mm')}`
        : '';
      const startDate = model.startDateTime
        ? `${this.datePipe.transform(model.startDateTime, 'dd/MM/yyyy HH:mm')}`
        : '';

      const oldEndDate = this.asset()?.endDateTime
        ? `${this.datePipe.transform(
            this.asset()!.endDateTime,
            'dd/MM/yyyy HH:mm'
          )}`
        : '';

      const oldStartDate = this.asset()?.startDateTime
        ? `${this.datePipe.transform(
            this.asset()!.startDateTime,
            'dd/MM/yyyy HH:mm'
          )}`
        : '';

      model.startDateTime = this.controls.startDateTime?.getRawValue();
      model.endDateTime = this.controls.endDateTime?.getRawValue();
      if (!this.asset()) {
        if (this.locDependencies.locType.includes(model.assetType ?? '')) {
          (model.startDateTime = model.startDateTime
            ? stripTimezoneOffset(model.startDateTime)
            : null),
            (model.endDateTime = model.endDateTime
              ? stripTimezoneOffset(model.endDateTime)
              : null),
            this.store.dispatch(AssetActions.add_Asset({ asset: model }));
        } else {
          let message = '';
          if (model.endDateTime) {
            message = `
            ${this.getOperatorNameById(
              model.clientId
            )} will own this asset from ${startDate} to ${endDate}.
            <br>
            You will not be able to edit the ownership history for this asset before this period.
            <br>
            Do you want to continue?
            `;
          } else {
            message = `
            ${this.getOperatorNameById(
              model.clientId
            )} will own this asset from ${startDate}.
            <br>
            You cannot add any history that precedes this date.
            <br>
            Do you want to continue?
            `;
          }

          this.confirmService
            .confirm({
              header: 'Confirmation',
              message,
              acceptLabel: 'Yes',
              rejectLabel: 'No',
            })
            .then((value) => {
              if (value) {
                (model.startDateTime = model.startDateTime
                  ? stripTimezoneOffset(model.startDateTime)
                  : null),
                  (model.endDateTime = model.endDateTime
                    ? stripTimezoneOffset(model.endDateTime)
                    : null),
                  this.store.dispatch(AssetActions.add_Asset({ asset: model }));
              }
            });
        }
      } else {
        model.assetType = this.asset()!.assetType;

        if (this.locDependencies.locType.includes(model.assetType ?? '')) {
          this.dispatchEditAsset(model);
          return;
        }

        if (
          !this.controls.addNewHistory?.value &&
          (model.clientId !== this.asset()!.clientId ||
            model.startDateTime !== this.asset()!.startDateTime ||
            model.endDateTime !== this.asset()!.endDateTime)
        ) {
          const message = `
          You are editing the asset ownership history of this asset from:
          <br>
          ${this.getOperatorNameById(
            this.asset()!.clientId
          )} owning it from ${oldStartDate} to ${oldEndDate}.
          <br>
          to ${this.getOperatorNameById(
            model.clientId
          )} owning it from ${startDate} to ${endDate}.
          <br>
          Do you want to continue?
          `;
          this.confirmService
            .confirm({
              header: 'Confirmation',
              message,
              acceptLabel: 'Yes',
              rejectLabel: 'No',
            })
            .then((value) => {
              if (value) {
                this.dispatchEditAsset(model);
              }
            });

          return;
        }

        if (this.controls.addNewHistory?.value) {
          let message = '';
          if (this.asset()?.endDateTime) {
            message = `
            ${this.getOperatorNameById(model.clientId)} will own this asset from
            <br>
            ${startDate} to ${endDate}.
            <br>
            You will not be able to edit the ownership history for this asset before this period.
            `;
          } else {
            message = `
            ${this.getOperatorNameById(model.clientId)} will own this asset from
            <br>
            ${startDate} to ${endDate}.
            <br>
            ${
              this.asset()!.clientName
            } will be recorded as owning this asset from
            <br>
            ${startDate} to ${endDate}.
            <br>
            You will not be able to edit ${
              this.asset()!.clientName
            } ownership history for this asset during or before this period.
            <br>
            Do you want to continue?
            `;
          }
          this.confirmService
            .confirm({
              header: 'Confirmation',
              message,
              acceptLabel: 'Yes',
              rejectLabel: 'No',
            })
            .then((value) => {
              if (value) {
                if (this.asset()!.isClusterHead) {
                  const message = `
                  Do you want to change all assets in this cluster to have ${this.getOperatorNameById(
                    model.clientId
                  )} assigned as Operator?.
                  <br>
                  If you select no, all the child assets of this asset will no longer be in a cluster.
                  <br>
                  Do you wish to proceed?
                  `;
                  this.confirmService
                    .confirm({
                      header: 'Confirmation',
                      message,
                      acceptLabel: 'Yes',
                      rejectLabel: 'No',
                    })
                    .then((value) => {
                      this.dispatchEditAsset({
                        ...model,
                        updateChildsOperators: value,
                      });
                    });
                } else if (model.clusterHeadId) {
                  const message = `
                  You are moving ${
                    this.asset.name
                  } from ${this.getAssetNameById(
                    this.asset()!.clusterHeadId!
                  )}'s cluster to ${this.getAssetNameById(
                    model.clusterHeadId
                  )}'s cluster
                  `;
                  this.confirmService
                    .confirm({
                      header: 'Confirmation',
                      message,
                      acceptLabel: 'Yes',
                      rejectLabel: 'No',
                    })
                    .then((value) => {
                      if (value) {
                        this.dispatchEditAsset(model);
                      }
                    });
                } else if (this.asset()!.clusterHeadId) {
                  const message = `
                  Changing the operator of this ${
                    this.asset()!.name
                  } will remove it from ${this.getAssetNameById(
                    this.asset()!.clusterHeadId!
                  )}'s cluster. Do you wish to proceed?
                  `;
                  this.confirmService
                    .confirm({
                      header: 'Confirmation',
                      message,
                      acceptLabel: 'Yes',
                      rejectLabel: 'No',
                    })
                    .then((value) => {
                      if (value) {
                        this.dispatchEditAsset(model);
                      }
                    });
                } else {
                  this.dispatchEditAsset(model);
                }
              }
            });

          return;
        }

        this.dispatchEditAsset(model);
      }
    }
  }

  dispatchEditAsset(asset: Asset) {
    (asset.startDateTime = asset.startDateTime
      ? stripTimezoneOffset(asset.startDateTime)
      : null),
      (asset.endDateTime = asset.endDateTime
        ? stripTimezoneOffset(asset.endDateTime)
        : null),
      this.store.dispatch(
        AssetActions.edit_Asset({
          asset,
          assetId: this.asset()!.assetId,
          addNewHistory: this.controls.addNewHistory?.value ?? false,
        })
      );
  }

  private getOperatorNameById(id: string): string {
    return this.operators().find((item) => item.clientId === id)?.name ?? '';
  }

  private getAssetNameById(id: string): string {
    return this.assets().find((item) => item.assetId === id)?.name ?? '';
  }
}
