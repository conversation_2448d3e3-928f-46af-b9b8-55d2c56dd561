import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from '@angular/common';
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  DestroyRef,
  inject,
  OnInit,
  ViewChild,
} from '@angular/core';
import { FormsModule } from '@angular/forms';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

import { Actions, ofType } from '@ngrx/effects';
import { Store } from '@ngrx/store';

import { ConfirmationService } from 'primeng/api';
import { InputTextModule } from 'primeng/inputtext';
import { Table, TableModule } from 'primeng/table';

import { ActivityCategoriesTableFields } from './helper/activity-categories-table-fields.enum';
import { InitializeActivityCategoriesTable } from './helper/activity-categories-table';
import { ActivityCategoryActions } from 'libs/services/src/lib/services/maintenance/store/actions/activity-category.actions';
import { CustomChipComponent } from 'libs/components/src/lib/components/custom-chip/custom-chip.component';
import { ActivityCategory } from 'libs/services/src/lib/services/maintenance/interfaces/activity-category.interface';
import { ActivityCategoryAddEditComponent } from './components/activity-category-add-edit/activity-category-add-edit.component';
import { CategoryTypeAddEditComponent } from './components/category-type-add-edit/category-type-add-edit.component';

@Component({
  selector: 'md-activity-categories',
  standalone: true,
  templateUrl: './activity-categories.page.html',
  imports: [
    InputTextModule,
    TableModule,
    NgFor,
    NgSwitch,
    NgSwitchCase,
    NgSwitchDefault,
    FormsModule,
    NgIf,
    DatePipe,
    CustomChipComponent,
    ActivityCategoryAddEditComponent,
    CategoryTypeAddEditComponent
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ActivityCategoriesPage implements OnInit {
  @ViewChild('table') table!: Table;
  private readonly store = inject(Store);
  private readonly actions = inject(Actions);
  private readonly confirmationService = inject(ConfirmationService);
  private readonly destroyRef = inject(DestroyRef);
  private readonly cdr = inject(ChangeDetectorRef);

  searchValue = '';
  listColumns = InitializeActivityCategoriesTable();
  activityCategories: ActivityCategory[] = [];
  tableFields = ActivityCategoriesTableFields;
  tableWidth = 1300;

  ngOnInit() {
    this.store.dispatch(
      ActivityCategoryActions.init_activity_categories()
    );

    this.actions
      .pipe(
        ofType(ActivityCategoryActions.load_Activity_Category_Success),
        takeUntilDestroyed(this.destroyRef)
      )
      .subscribe(({ activityCategory }) => {
        this.searchValue = '';
        this.activityCategories = [...activityCategory];
        this.table.reset();
        this.cdr.markForCheck();
      });
  }

  create() {
    this.store.dispatch(
      ActivityCategoryActions.change_visibility_add_edit({
        visible: true,
        activityCategory: null,
      })
    );
  }

  edit(activityCategory: ActivityCategory) {
    this.store.dispatch(
      ActivityCategoryActions.change_visibility_add_edit({
        visible: true,
        activityCategory,
      })
    );
  }

  openAddCategoryType(activityCategory: ActivityCategory) {
    this.store.dispatch(
      ActivityCategoryActions.change_visibility_add_category_type({
        visible: true,
        activityCategory,
      })
    );
  }

  remove(activityCategory: ActivityCategory) {
    this.confirmationService.confirm({
      header: 'Delete',
      message: `
        Deleting this Activity Category may affect other parts of Master Data.
        <br>
        Do you want to remove this Activity Category?
      `,
      acceptLabel: 'Confirm',
      rejectLabel: 'Cancel',
      acceptButtonStyleClass: 'btn-negative-primary',
      rejectButtonStyleClass: 'btn-tertiary',
      acceptIcon: 'none',
      rejectIcon: 'none',
      accept: () => {
        this.store.dispatch(
          ActivityCategoryActions.remove_Activity_Category({
            id: activityCategory.activityCategoryId,
          })
        );
      },
    });
  }
}
