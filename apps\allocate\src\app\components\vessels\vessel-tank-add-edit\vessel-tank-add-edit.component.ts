import { Component, inject, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Async<PERSON><PERSON><PERSON>, NgForOf, NgIf } from '@angular/common';
import {
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { LoadingDirective } from 'libs/components/src/lib/directives/loading.directive';
import { MatButtonModule } from '@angular/material/button';
import {
  MAT_DIALOG_DATA,
  MatDialogModule,
  MatDialogRef,
} from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatOptionModule } from '@angular/material/core';
import { MatSelectModule } from '@angular/material/select';
import { VesselTank } from 'libs/services/src/lib/services/vessels/interfaces/vessel-tank.interface';
import { Store } from '@ngrx/store';
import { Actions, ofType } from '@ngrx/effects';
import { vesselTanksFeature } from 'libs/services/src/lib/services/vessels/store/features';
import { Subject, takeUntil } from 'rxjs';
import { VesselTankActions } from 'libs/services/src/lib/services/vessels/store/actions/vessel-tank.actions';
import { ActivatedRoute } from '@angular/router';
import { UtilityService } from 'libs/services/src/lib/services/utility.service';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { NgxMatDatetimePickerModule } from '@angular-material-components/datetime-picker';
import { MatMomentDateModule } from '@angular/material-moment-adapter';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { greaterThan } from 'libs/components/src/lib/validators/greaterThan';
import { decimalPoint } from 'libs/components/src/lib/validators/decimal-point';
import { OnlyDigitsDirective } from 'libs/components/src/lib/directives/only-digits.directive';
import { SingleSelectComponent } from 'libs/components/src/lib/components/single-select/single-select.component';
import { CdkDrag, CdkDragHandle } from '@angular/cdk/drag-drop';
@Component({
  selector: 'lha-vessel-tank-add-edit',
  standalone: true,
  imports: [
    FormsModule,
    LoadingDirective,
    MatButtonModule,
    MatDialogModule,
    MatFormFieldModule,
    MatInputModule,
    MatOptionModule,
    MatSelectModule,
    NgForOf,
    ReactiveFormsModule,
    AsyncPipe,
    NgIf,
    MatDatepickerModule,
    MatMomentDateModule,
    NgxMatDatetimePickerModule,
    MatSlideToggleModule,
    OnlyDigitsDirective,
    SingleSelectComponent,
    CdkDrag,
    CdkDragHandle,
  ],
  templateUrl: './vessel-tank-add-edit.component.html',
  styleUrls: ['./vessel-tank-add-edit.component.scss'],
})
export class VesselTankAddEditComponent implements OnInit, OnDestroy {
  dialogRef = inject(MatDialogRef<VesselTankAddEditComponent>);
  data: { vesselTank: VesselTank } = inject(MAT_DIALOG_DATA);
  activatedRoute = inject(ActivatedRoute);
  utilityService = inject(UtilityService);
  store = inject(Store);
  action = inject(Actions);
  vm$ = this.store.select(vesselTanksFeature.selectVesselTanksState);
  vesselId = this.utilityService.getParamsFromRoute(
    this.activatedRoute.snapshot.root
  )['id'];
  isAdd = true;
  unsubscribe: Subject<boolean> = new Subject();
  vesselTank!: VesselTank;
  form = new FormGroup({
    tankTypeId: new FormControl<string>('', [Validators.required]),
    bulkTypeId: new FormControl<string>('', [Validators.required]),
    name: new FormControl<string>('', [Validators.required]),
    quantity: new FormControl<number | null>(null, [
      greaterThan(-1),
      decimalPoint(3),
    ]),
    tankStatusChangeDate: new FormControl<Date | null>(null),
    cleaned: new FormControl<boolean>(false, [Validators.required]),
    dayRatePrice: new FormControl<number | null>(null, [
      greaterThan(-1),
      decimalPoint(2),
    ]),
  });
  ngOnInit(): void {
    this.subCloseDialog();
    this.initAddEdit();
    this.subToCleanedChanges();
  }
  ngOnDestroy(): void {
    this.unsubscribe.next(true);
    this.unsubscribe.complete();
  }
  subToCleanedChanges(): void {
    this.form
      .get('cleaned')!
      .valueChanges.pipe(takeUntil(this.unsubscribe))
      .subscribe((cleaned) => {
        const tankStatusChangeDateControl = this.form.get(
          'tankStatusChangeDate'
        )!;
        if (this.isAdd) {
          if (cleaned) {
            tankStatusChangeDateControl.setValidators(Validators.required);
          } else {
            tankStatusChangeDateControl.setValidators(null);
          }
        } else if (this.data.vesselTank) {
          if (cleaned !== this.data.vesselTank.cleaned) {
            tankStatusChangeDateControl.setValidators(Validators.required);
          } else {
            tankStatusChangeDateControl.setValidators(null);
          }
        }
        tankStatusChangeDateControl.updateValueAndValidity();
      });
  }
  private initAddEdit(): void {
    this.isAdd = !this.data.vesselTank;
    if (!this.isAdd) {
      this.vesselTank = this.data.vesselTank;
      this.pathForm(this.vesselTank);
    }
  }
  private subCloseDialog(): void {
    this.action
      .pipe(
        ofType(
          VesselTankActions.add_Vessel_Tank_Success,
          VesselTankActions.edit_Vessel_Tank_Success
        )
      )
      .pipe(takeUntil(this.unsubscribe))
      .subscribe(() => {
        this.dialogRef.close();
      });
  }
  private pathForm(vesselTank: VesselTank): void {
    this.form.patchValue({
      name: vesselTank.name,
      tankTypeId: vesselTank.tankTypeId,
      bulkTypeId: vesselTank.bulkTypeId,
      cleaned: vesselTank.cleaned,
      dayRatePrice: vesselTank.dayRatePrice,
      tankStatusChangeDate: vesselTank.tankStatusChangeDate
        ? vesselTank.tankStatusChangeDate
        : null,
      quantity: vesselTank.quantity,
    });
  }
  saveVesselTank(): void {
    if (this.form.invalid) {
      this.form.markAllAsTouched();
      return;
    }
    if (this.form.value.dayRatePrice === null) {
      this.form.value.dayRatePrice = 0;
    }
    if (this.form.value.quantity === null) {
      this.form.value.quantity = 0;
    }
    const model = {
      ...this.form.value,
      vesselId: this.vesselId,
    } as VesselTank;

    if (this.isAdd) {
      this.store.dispatch(
        VesselTankActions.add_Vessel_Tank({ vesselTank: model })
      );
    } else {
      this.store.dispatch(
        VesselTankActions.edit_Vessel_Tank({
          vesselTankId: this.vesselTank.vesselTankId,
          vesselTank: model,
        })
      );
    }
  }
}
