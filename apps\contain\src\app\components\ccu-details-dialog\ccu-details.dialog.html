<p-dialog
          [visible]="ccuDetailsDialogVisible()"
          [modal]="true"
          [draggable]="false"
          [closable]="false"
          [style]="{ width: '900px', 'height': '1000px' }">
  <ng-template pTemplate="header">
    <span class="p-dialog-title">CCU Details</span>
  </ng-template>

  <ng-template pTemplate="content">
    <div class="mb-20">
      <span class="section-title pb-10">Master Data</span>
      <table class="section-table full-width">
        <tr>
          <td class="field_label f-bold">CCU</td>
          <td><span class="pl-20"></span>{{ ccuData()?.ccuId }}</td>
          <td class="field_label f-bold">Length (mm)</td>
          <td><span class="pl-20"></span>{{ ccuData()?.length }}</td>
        </tr>
        <tr>
          <td class="field_label f-bold">Active</td>
          <td>
            <span class="pl-20"></span>
            <i 
            [ngClass]="!ccuData()?.disabled ? 'pi pi-check' : 'pi pi-times'" 
            [ngStyle]="{'color': !ccuData()?.disabled ? 'green' : 'red'}"
            style="font-size: 1.5rem;"></i>
          </td>
          <td class="field_label f-bold">Container Pool</td>
          <td><span class="pl-20"></span>{{ ccuData()?.poolName || '' }}</td>
        </tr>
        <tr>
          <td class="field_label f-bold">Type Name</td>
          <td><span class="pl-20">{{ ccuData()?.groupType }}</span></td>
          <td class="field_label f-bold">Width (mm)</td>
          <td><span class="pl-20">{{ ccuData()?.width }}</span></td>
        </tr>
        <tr>
          <td class="field_label f-bold">Family Name</td>
          <td><span class="pl-20">{{ ccuData()?.cargoFamilyName }}</span></td>
          <td class="field_label f-bold">Height (mm)</td>
          <td><span class="pl-20">{{ ccuData()?.height }}</span></td>
        </tr>
        <tr>
          <td class="field_label f-bold">Size</td>
          <td><span class="pl-20">{{ ccuData()?.cargoSizeName }}</span></td>
          <td class="field_label f-bold">Mgw (kg)</td>
          <td><span class="pl-20">{{ ccuData()?.maxGrossWeight }}</span></td>
        </tr>
        <tr>
          <td class="field_label f-bold">Type</td>
          <td><span class="pl-20">{{ ccuData()?.cargoTypeName }}</span></td>
          <td class="field_label f-bold">Tare (kg)</td>
          <td><span class="pl-20">{{ ccuData()?.tareMass }}</span></td>
        </tr>
        <tr>
          <td class="field_label f-bold">Status</td>
          <td>
            <span class="pl-20">{{ ccuData()?.isApproved ? 'Approved' : 'Pending' }}</span>
          </td>
          <td class="field_label f-bold">Cargo Category</td>
          <td>
            <span class="pl-20">{{ ccuData()?.category !== null && ccuData()?.category !== undefined ? (ccuData()!.category! |
            propertyName : cargoCategories : 'value' : 'name') : null }}</span>
          </td>
        </tr>
        <tr>
          <td class="field_label f-bold">CCU Owner</td>
          <td><span class="pl-20">{{ ccuData()?.vendorVendorName }}</span></td>
          <td class="field_label f-bold">Is Deck Cargo</td>
          <td>
            <span class="pl-20"><i 
              [ngClass]="ccuData()?.isDeckCargo ? 'pi pi-check' : 'pi pi-times'" 
              [ngStyle]="{'color': ccuData()?.isDeckCargo ? 'green' : 'red'}"
              style="font-size: 1.5rem;"></i></span>
            
          </td>
        </tr>
        <tr>
          <td class="field_label f-bold">Description</td>
          <td><span class="pl-20">{{ ccuData()?.cargoDescriptionName }}</span></td>
          <td class="field_label f-bold">Last Updated</td>
          <td>
            <span class="pl-20">{{ ccuData()?.updatedDate | date:'dd/MM/yyyy HH:mm' }}</span>
          </td>
        </tr>
      </table>
    </div>

    <div class="mb-20">
      <div class="d-flex sectionAlignment">
        <span class="section-title">CCU Status Info</span>
        <button
                class="btn-primary"
                type="button"
                (click)="setStatusDialog(ccuData()!)">
          Set current CCU status
        </button>
      </div>
      <table class="section-table full-width">
        <tr>
          <td class="field_label f-bold">CCU Status</td>
          <td>
            <span class="pl-20">{{ ccuData()?.cargoStatus !== null && ccuData()?.cargoStatus !== undefined ?
            (ccuData()!.cargoStatus! | propertyName : cargoStatuses : 'value' : 'name') : null }}</span>
          </td>
          <td></td>
          <td></td>
        </tr>
      </table>
    </div>

    <div class="mb-20">
      <span class="section-title pb-10">Configured Test Date</span>
      <table class="section-table full-width">
        <tr>
          <td class="field_label f-bold">Certificate Test Date</td>
          <td>
            <span class="pl-20">{{ ccuData()?.certificateTestDate | date:'dd/MM/yyyy' }}</span>
          </td>
          <td></td>
          <td></td>
        </tr>
        <tr>
          <td class="field_label f-bold">Certificate Test Expire Date</td>
          <td>
            <span class="pl-20">{{ ccuData()?.certificateExpireDate | date:'dd/MM/yyyy' }}</span>
          </td>
          <td></td>
          <td></td>
        </tr>
      </table>
    </div>

    <div class="mb-20">
      <div class="d-flex sectionAlignment">
        <span class="section-title pb-10">CCU Events</span>
        <button class="btn-primary" type="button" (click)="createNoteDialog()">
          Create Note
        </button>
      </div>
      <p-table
               [columns]="listColumns()"
               [value]="cargoEvents()"
               [scrollable]="true"
               class="pt-10">
        <ng-template pTemplate="header" let-columns>
          <tr>
            <th *ngFor="let column of columns" scope="col" [style.min-width.px]="column.width"
                [style.width.%]="(column.width / tableWidth) * 100">
              <span>{{ column.name }}</span>
            </th>
          </tr>
        </ng-template>
        <ng-template pTemplate="body" let-rowData>
          <tr>
            <td>{{ rowData.eventType !== null ? eventTypeDescriptions[rowData.eventType] :
              cargoHireEventTypeDescriptions[rowData.hireRequestCargoEventType] }}</td>
            <td>{{ rowData.eventDate | date:'dd/MM/yyyy HH:mm' }}</td>
            <td>
              <span *ngIf="rowData.eventType === 0 || rowData.hireRequestCargoEventType === 6">
                {{ rowData.createdUserEmail }}:
              </span>
              {{ rowData.details }}
            </td>
            <td>
              <i
                 *ngIf="rowData.eventType === 0"
                 class="pi pi-trash"
                 title="Remove"
                 (click)="onDelete($event, rowData)">
              </i>
            </td>
          </tr>
        </ng-template>
      </p-table>
    </div>
  </ng-template>

  <ng-template pTemplate="footer">
    <button
            class="btn-tertiary"
            type="button"
            (click)="hideDialog()">
      Close
    </button>
  </ng-template>
</p-dialog>

<contain-set-status-ccu *ngIf="ccuData()" [data]="currentCcu"></contain-set-status-ccu>

<contain-cargo-note-dialog
  *ngIf="ccuData()"
  [cargoId]="ccuData()!.cargoId">
</contain-cargo-note-dialog>