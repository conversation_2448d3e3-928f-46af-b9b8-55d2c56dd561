import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  DestroyRef,
  effect,
  inject,
  Input,
  OnInit,
  signal,
  ViewChild,
} from '@angular/core';
import {
  NgStyle,
  NgIf,
  DatePipe,
  DecimalPipe,
  NgFor,
  NgSwitch,
  NgSwitchCase,
  NgSwitchDefault,
} from '@angular/common';
import { RouterLink } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

import { Store } from '@ngrx/store';
import { Actions, ofType } from '@ngrx/effects';

import { Table, TableModule } from 'primeng/table';
import { CardModule } from 'primeng/card';
import { InputTextModule } from 'primeng/inputtext';

import { settingsFeature } from 'libs/services/src/lib/services/settings/shared/store/features';
import { Voyage } from 'libs/services/src/lib/services/voyages/interfaces/voyage.interface';

import { CustomChipComponent } from 'libs/components/src/lib/components/custom-chip/custom-chip.component';
import { BillingVoyageCardComponent } from '../billing-voyage-card/billing-voyage-card.component';
import { ColumnModel } from 'libs/components/src/lib/models/column.model';
import { BillingDetailTableFields } from '../../../shared/enums/billing-detail-table-fields.enum';
import { billingsFeature } from '../../../store/features/billing.features';
import { BillingVesselsActions } from '../../../store/actions/billing-vessel.actions';
import { billingVesselsFeature } from '../../../store/features/billing-vessel.features';
import { InitializeBillingsDetailTable } from '../../../shared/tables/billing-detail.table';
import { BillingPeriodComponent } from '../billing-period/billing-period.component';

@Component({
  standalone: true,
  selector: 'lha-billing-vessel-details',
  templateUrl: './billing-vessel-details.component.html',
  imports: [
    NgStyle,
    NgIf,
    BillingVoyageCardComponent,
    DatePipe,
    DecimalPipe,
    CustomChipComponent,
    FormsModule,
    CardModule,
    NgSwitch,
    NgSwitchCase,
    NgSwitchDefault,
    TableModule,
    NgFor,
    InputTextModule,
    RouterLink,
    BillingPeriodComponent
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BillingVesselDetailsComponent implements OnInit {
  @ViewChild('table') table!: Table;
  @Input() set vesselId(value: string) {
    this.#vesselId.set(value);
  }
  private readonly store = inject(Store);
  private readonly actions = inject(Actions);
  private readonly destroyRef = inject(DestroyRef);
  private readonly cdr = inject(ChangeDetectorRef);
  #vesselId = signal('');
  billingVesselView = this.store.selectSignal(
    billingVesselsFeature.selectBillingVesselView
  );
  billingPeriodId = this.store.selectSignal(
    billingsFeature.selectBillingPeriodId
  );
  vessel = this.store.selectSignal(billingVesselsFeature.selectVesselById);
  appSettings = this.store.selectSignal(settingsFeature.selectAppSettings);
  listColumns = [
    ...InitializeBillingsDetailTable(),
    new ColumnModel(
      BillingDetailTableFields.actions,
      'Change Billing Date',
      150
    ),
  ];
  tableFields = BillingDetailTableFields;
  searchValue = '';
  loading = this.store.selectSignal(billingVesselsFeature.selectLoading);
  voyages: Voyage[] = [];
  tableWidth = 1150;

  constructor() {
    effect(
      () => {
        if (this.billingPeriodId() && this.#vesselId()) {
          this.store.dispatch(
            BillingVesselsActions.load_Billing_Vessel_Voyages({
              billingPeriodId: this.billingPeriodId(),
              vesselId: this.#vesselId(),
            })
          );
        }
      },
      {
        allowSignalWrites: true,
      }
    );
  }

  ngOnInit() {
    this.actions
      .pipe(
        ofType(BillingVesselsActions.load_Billing_Vessel_Voyages_Success),
        takeUntilDestroyed(this.destroyRef)
      )
      .subscribe(({ billingVesselVoyages }) => {
        this.searchValue = '';
        this.voyages = [...billingVesselVoyages?.voyages!];
        this.table.reset();
        this.cdr.markForCheck();
      });
  }

  export(): void {
    this.store.dispatch(BillingVesselsActions.export_Billing_Vessel({
      billingPeriodId: this.billingPeriodId(),
      vesselId: this.#vesselId()
    }));
  }

  openChangePeriodDialog(voyage: Voyage): void {
    this.store.dispatch(
      BillingVesselsActions.change_visibility_billing_period({
        visible: true,
        voyage,
        
      })
    );
  }
}
