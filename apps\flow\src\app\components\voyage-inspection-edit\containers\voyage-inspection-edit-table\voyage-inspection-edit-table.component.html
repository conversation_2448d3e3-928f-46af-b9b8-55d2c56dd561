<p-table
  [columns]="listColumns()"
  [value]="voyageCargoes"
  [scrollable]="true"
  scrollHeight="400px"
>
  <ng-template pTemplate="header" let-columns>
    <tr>
      <th
        *ngFor="let column of columns"
        scope="col"
        [style.min-width.px]="column.width"
        [style.width.%]="(column.width / tableWidth) * 100"
      >
        <span>{{ column.name }}</span>
      </th>
    </tr>
  </ng-template>
  <ng-template
    pTemplate="body"
    let-item
    let-rowData
    let-index="rowIndex"
    let-columns="columns"
  >
    <tr
      [ngClass]="{
              'bumped-row': rowData.voyageCargo.isBumped && !rowData.voyageCargo.isCancelled,
            }"
    >
      <td>
        <i
          *ngIf="canManage"
          class="pi pi-ellipsis-v"
          (click)="openOverlayPanel($event, item)"
        ></i>
      </td>
      <td>
        <img
          *ngIf="item.status == VoyageCargoInspectionStatus.Uninspected"
          src="./assets/uninspected.svg"
        />
        <img
          *ngIf="item.status == VoyageCargoInspectionStatus.Passed"
          src="./assets/passed.svg"
        />
        <img
          *ngIf="item.status == VoyageCargoInspectionStatus.Failed"
          src="./assets/failed.svg"
        />
      </td>
      <td>
        {{
          item.voyageCargo.assetName
            ? item.voyageCargo.assetName
            : 'No Location'
        }}
      </td>
      <td>{{ item.voyageCargo.ccuId ? item.voyageCargo.ccuId : '' }}</td>
      <td>{{ item.voyageCargo.quantity ? item.voyageCargo.quantity : '' }}</td>
      <td>
        {{ item.voyageCargo.cargoWeight ? item.voyageCargo.cargoWeight : '' }}
      </td>
      <td>
        <span
          *ngIf="item.voyageCargo.voyageCargoDangerousGoods?.length"
          class="d-flex"
        >
          <img width="25" src="assets/danger.svg" />
          <sub> {{ item.voyageCargo.voyageCargoDangerousGoods?.length }}</sub>
        </span>
      </td>
      <td>
        {{ item.voyageCargo.voyageCargoDangerousGoods?.length ? subClass : '' }}
      </td>
      <td>
        {{ item.voyageCargo.voyageCargoDangerousGoods?.length ? unNo : '' }}
      </td>
      <td>
        {{ item.voyageCargo.vendorId ? item.voyageCargo.vendorName : '' }}
      </td>
      <td>
        {{
          item.voyageCargo.cargoUnitType ? item.voyageCargo.cargoUnitType : ''
        }}
      </td>
      <td>{{ item.trailerNumber ? item.trailerNumber : '' }}</td>
      <td>{{ item.recordedTime | date : 'dd/MM/yyyy HH:mm' }}</td>
      <td>{{ item.failReason ? item.failReason : '' }}</td>
      <td>
        <span>
          {{
            item.voyageCargo.collectDate
              ? (item.voyageCargo.collectDate | date : 'dd/MM/yyyy') + ' '
              : ''
          }}
        </span>
        <span>
          {{
            item.voyageCargo.collectTime
              ? (item.voyageCargo.collectTime | slice : 0 : -3)
              : ''
          }}
        </span>
      </td>
      <td>
        {{
          item.voyageCargo.pickupAddress ? item.voyageCargo.pickupAddress : ''
        }}
      </td>
    </tr>
  </ng-template>
</p-table>

<div class="card flex justify-center gap-2">
  <p-overlayPanel #op>
    <div
      *ngIf="selectedInspection?.status !== VoyageCargoInspectionStatus.Passed"
      class="menu-item"
      (click)="handleMenuOption('Passed')"
    >
      Passed
    </div>
    <div
      *ngIf="selectedInspection?.status !== VoyageCargoInspectionStatus.Failed"
      class="menu-item"
      (click)="handleMenuOption('Failed')"
    >
      Failed
    </div>
    <div
      class="menu-item"
      *ngIf="
        selectedInspection?.status !== VoyageCargoInspectionStatus.Uninspected
      "
      (click)="handleMenuOption('Uninspect')"
    >
      Uninspect
    </div>
    <div class="menu-item" (click)="cancel()">Cancel</div>
  </p-overlayPanel>
</div>

<inspection-table-dialog
  *ngIf="selectedInspection && inspectionTableDialog"
  [inspectionTableDialog]="inspectionTableDialog"
  [voyageInspectionCargo]="selectedInspection"
  [header]="dialogHeading"
  [status]="status"
  (closedDialog)="closeDialog()"
  [saveText]="saveText"
  [width]="width"
  [height]="height"
>
</inspection-table-dialog>
