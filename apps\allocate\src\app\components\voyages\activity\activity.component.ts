import { Component, computed, inject, OnInit } from '@angular/core';
import {
  DatePipe,
  NgStyle,
  NgClass,
  NgForOf,
  NgIf,
  DecimalPipe,
} from '@angular/common';
import { CustomChipComponent } from 'libs/components/src/lib/components/custom-chip/custom-chip.component';
import { Store } from '@ngrx/store';
import {
  activitiesFeature,
  voyagesFeature,
} from 'libs/services/src/lib/services/voyages/store/features';
import {
  Activity,
  GroupedInvalidErrors,
} from 'libs/services/src/lib/services/voyages/interfaces/activity.interface';
import { ActivityActions } from 'libs/services/src/lib/services/voyages/store/actions/activity.actions';
import { VoyageStatus } from 'libs/components/src/lib/enums/voyage-status.enum';
import { TableModule } from 'primeng/table';
import { ColumnModel } from 'libs/components/src/lib/models/column.model';
import { FormControl, FormGroup } from '@angular/forms';
import { ConfirmationService } from 'primeng/api';
import { ActivityAddEditComponent } from '../../../components/voyages/activity-add-edit/activity-add-edit.component';
import { InputTextModule } from 'primeng/inputtext';
import { OverlayPanelModule } from 'primeng/overlaypanel';

@Component({
  selector: 'lha-activity',
  standalone: true,
  imports: [
    NgIf,
    DatePipe,
    NgStyle,
    NgClass,
    CustomChipComponent,
    NgForOf,
    DecimalPipe,
    TableModule,
    ActivityAddEditComponent,
    InputTextModule,
    OverlayPanelModule,
  ],
  templateUrl: './activity.component.html',
  styleUrls: ['./activity.component.scss'],
})
export class ActivityComponent implements OnInit {
  private readonly confirmationService = inject(ConfirmationService);
  store = inject(Store);
  activityState = this.store.selectSignal(
    activitiesFeature.selectActivitiesState
  );
  voyage = this.store.selectSignal(voyagesFeature.selectActiveVoyage);
  voyageStatus = VoyageStatus;
  searchControl = new FormGroup({
    search: new FormControl<string>(''),
  });
  controls = {
    search: this.searchControl.get('search'),
  };
  columns: ColumnModel[] = [
    new ColumnModel('index', 'Index', 100, {}),
    new ColumnModel('parallelActivityNotOk', '', 120, {}),
    new ColumnModel('activityCode', 'Activity', 120, {}),
    new ColumnModel('parallelActivitiesList', 'Parallel Activities', 120, {}),
    new ColumnModel('assetName', 'Asset', 100, {}),
    new ColumnModel('clientName', 'Operator', 100, {}),
    new ColumnModel('billedAssetName', 'Billed Asset', 100, {}),
    new ColumnModel('startDateTime', 'Time From', 120),
    new ColumnModel('endDateTime', 'Time To', 100),
    new ColumnModel('duration', 'Duration (days)', 100),
    new ColumnModel('createdDate', 'Date Created', 100),
    new ColumnModel('comments', 'Comments', 150),
  ];
  tableWidth = 3235;
  invalidErrors: { Error: string }[] = [];

  dynamicColumns = computed(() => {
    if (this.voyage()?.voyageStatus !== this.voyageStatus.Completed) {
      return [
        ...this.columns,
        new ColumnModel('vesselActivityId', 'Actions', 120),
      ];
    }
    return this.columns;
  });

  ngOnInit(): void {
    this.loadActivitiesLists();
  }

  loadActivitiesLists(): void {
    this.store.dispatch(ActivityActions.load_Activities_Lists());
  }

  exportVesselActivities(): void {
    this.store.dispatch(ActivityActions.export_Activities());
  }

  addEditActivity(activity: Activity | null, isEdit: boolean): void {
    this.store.dispatch(
      ActivityActions.open_Activity_Add_Edit_Dialog({
        isVisible: true,
        isEdit,
        activity,
      })
    );
  }

  removeActivity(activity: Activity, activityList: Activity[]): void {
    const actIndex = activityList.findIndex(
      (item) => item.vesselActivityId === activity.vesselActivityId
    );
    const isFirstLast = actIndex === 0 || actIndex + 1 === activityList.length;

    this.confirmationService.confirm({
      header: 'Delete',
      message: isFirstLast
        ? 'Are you sure you want to delete this activity?'
        : 'Proceeding with this operation can cause time gaps between activities, the voyage will be marked as invalid, and you will not be able to calculate until there are no gaps in the Voyage. Do you want to continue?',
      acceptLabel: 'Confirm',
      rejectLabel: 'Cancel',
      acceptButtonStyleClass: 'btn-negative-primary',
      rejectButtonStyleClass: 'btn-tertiary',
      acceptIcon: 'none',
      rejectIcon: 'none',
      accept: () => {
        this.store.dispatch(
          ActivityActions.remove_Activity({ id: activity.vesselActivityId })
        );
      },
    });
  }

  applyGlobalFilter(event: any, table: any, onInit: boolean): void {
    if (!onInit) {
      const inputElement = event.target as HTMLInputElement;
      if (inputElement) {
        table.filterGlobal(inputElement.value, 'contains');
      }
    } else {
      table.filterGlobal(this.controls.search!.value, 'contains');
    }
  }

  showInvalidErrors(event: MouseEvent, activity: Activity, overlayPanel: any) {
    const errors: GroupedInvalidErrors = activity.invalidErrors
      ? JSON.parse(activity.invalidErrors)
      : {};
    this.invalidErrors = [];

    for (const key in errors) {
      if (errors.hasOwnProperty(key)) {
        this.invalidErrors.push(...errors[key]);
      }
    }

    if (activity.timeGapNotOk) {
      this.invalidErrors.push({ Error: 'Time gaps' });
    }
    if (activity.parallelActivityNotOk) {
      this.invalidErrors.push({ Error: 'Parallel activities are not allowed' });
    }

    overlayPanel.show(event);
  }

  hideInvalidErrors(overlayPanel: any) {
    overlayPanel.hide();
  }
}
