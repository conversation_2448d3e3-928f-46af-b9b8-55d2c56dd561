<p-dialog [draggable]="false" [closable]="false" [modal]="true"
          [visible]="dialogVisible()"
          [style]="{ width: '800px', height: '200px' }">
    <ng-template pTemplate="header">
        <span class="p-dialog-title">
            Set Test Date
        </span>
    </ng-template>
    <ng-template pTemplate="content">
        <form class="w-50" [formGroup]="ccuForm">
            <span class="field_label f-bold">CCU Test Date *</span>
            <p-calendar
                        [inputId]="'certificateTestDate'"
                        [tabindex]="0"
                        [showIcon]="true"
                        [showTime]="false"
                        [showSeconds]="false"
                        [readonlyInput]="false"
                        dateFormat="dd/mm/yy"
                        formControlName="certificateTestDate"
                        (keydown)="$event.stopPropagation()"
                        appendTo="body">
            </p-calendar>
        </form>
    </ng-template>
    <ng-template pTemplate="footer">
        <button class="btn-tertiary" (click)="hideDialog()">Cancel</button>
        <button
                class="btn-primary"
                type="button"
                (click)="submit()"
                [disabled]="!ccuForm.valid || ccuForm.pristine">
            Save
        </button>
    </ng-template>
</p-dialog>