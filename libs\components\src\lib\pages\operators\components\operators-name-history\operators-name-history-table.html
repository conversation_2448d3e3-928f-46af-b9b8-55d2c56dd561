<div class="d-flex mb-20 justify-content-end">
  <span class="p-input-icon-left">
    <em class="pi pi-search"></em>
    <input
      type="text"
      pInputText
      [(ngModel)]="searchValue"
      placeholder="Search..."
      (input)="assetsHistoryTable.filterGlobal(searchValue, 'contains')"
    />
  </span>
</div>

<p-table
  #assetsHistoryTable
  [columns]="listAssetClientsColumns"
  [value]="operator()?.clientNameHistory || []"
  [scrollable]="true"
  scrollHeight="500px"
  [rowsPerPageOptions]="[10, 25, 50]"
  [paginator]="true"
  [rows]="10"
  [filterDelay]="0"
  [globalFilterFields]="[tableFields.name]"
>
  <ng-template pTemplate="header" let-columns>
    <tr>
      <th
        *ngFor="let column of columns"
        [style.min-width.px]="column.width"
        [style.width.%]="(column.width / tableWidth) * 100"
        [pSortableColumn]="column.field"
        [pSortableColumnDisabled]="!column.sortable"
      >
        <span>{{ column.name }}</span>
        <p-sortIcon *ngIf="column.sortable" [field]="column.field" />
      </th>
    </tr>
  </ng-template>
  <ng-template
    let-index="rowIndex"
    pTemplate="body"
    let-row
    let-columns="columns"
  >
    <tr>
      <ng-container *ngFor="let column of columns">
        <ng-container [ngSwitch]="column.field">
          <td *ngSwitchCase="tableFields.startDateTime">
            {{ row.startDateTime | date : 'dd/MM/yyyy HH:mm' }}
          </td>
          <td *ngSwitchCase="tableFields.endDateTime">
            <span *ngIf="tableFields.endDateTime; else current">
              {{ row.endDateTime | date : 'dd/MM/yyyy HH:mm' }}
            </span>
            <ng-template #current>
              <lha-custom-chip cssClass="draft" [size]="null" text="current" />
            </ng-template>
          </td>
          <td *ngSwitchDefault>{{ row[column.field] }}</td>
        </ng-container>
      </ng-container>
    </tr>
  </ng-template>
  <ng-template pTemplate="emptymessage">
    <tr>
      <td colspan="3" style="text-align: center">No results found</td>
    </tr>
  </ng-template>
</p-table>
