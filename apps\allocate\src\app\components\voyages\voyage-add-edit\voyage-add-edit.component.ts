import { Component, DestroyRef, inject, OnInit } from '@angular/core';
import { Voyage } from 'libs/services/src/lib/services/voyages/interfaces/voyage.interface';
import { Store } from '@ngrx/store';
import { Actions, ofType } from '@ngrx/effects';
import { voyagesFeature } from 'libs/services/src/lib/services/voyages/store/features';
import {
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { VoyagesActions } from 'libs/services/src/lib/services/voyages/store/actions/voyages.actions';
import { VoyageAddRequest } from 'libs/services/src/lib/services/voyages/interfaces/voyage-add-request.interface';
import { AsyncPipe, NgForOf, NgIf } from '@angular/common';
import { InitialCapitalizeDirective } from 'libs/components/src/lib/directives/initial-capitalize.directive';
import { settingsFeature } from 'libs/services/src/lib/services/settings/shared/store/features';
import { confirmActions } from 'libs/components/src/lib/store/confirm.actions';
import { voyageTypes } from 'libs/services/src/lib/services/voyages/voyage-types/voyage-types';
import { decimalPoint } from 'libs/components/src/lib/validators/decimal-point';
import { UserRole } from 'libs/auth/src/lib/constants/userRole.enum';
import { HasPermissionDirective } from '../../../shared/directives/hasPermissions.directive';
import { VoyageStatus } from 'libs/components/src/lib/enums/voyage-status.enum';
import { DialogModule } from 'primeng/dialog';
import { DropdownModule } from 'primeng/dropdown';
import { InputTextModule } from 'primeng/inputtext';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { voyageDirections } from 'libs/services/src/lib/services/voyages/voyage-types/voyage-direction';
import { RadioButtonModule } from 'primeng/radiobutton';
import { ProgressSpinnerModule } from 'primeng/progressspinner';

@Component({
  selector: 'lha-voyage-add-edit',
  standalone: true,
  imports: [
    FormsModule,
    NgForOf,
    ReactiveFormsModule,
    AsyncPipe,
    NgIf,
    InitialCapitalizeDirective,
    HasPermissionDirective,
    DialogModule,
    DropdownModule,
    InputTextModule,
    RadioButtonModule,
    ProgressSpinnerModule,
  ],
  templateUrl: './voyage-add-edit.component.html',
  styleUrls: ['./voyage-add-edit.component.scss'],
})
export class VoyageAddEditComponent implements OnInit {
  private readonly destroyRef = inject(DestroyRef);
  store = inject(Store);
  actions = inject(Actions);
  isVisibleDialog = this.store.selectSignal(
    voyagesFeature.selectIsVoyageAddEditDialogVisible
  );
  isEdit = this.store.selectSignal(voyagesFeature.selectIsEdit);
  voyageState = this.store.selectSignal(voyagesFeature.selectVoyagesState);
  voyage = this.store.selectSignal(voyagesFeature.selectVoyage);
  appSettings = this.store.selectSignal(settingsFeature.selectAppSettings);
  loading = this.store.selectSignal(voyagesFeature.selectLoading);
  userRole = UserRole;
  form = new FormGroup({
    voyageNumber: new FormControl<string>('', [Validators.required]),
    vesselId: new FormControl<string>('', [Validators.required]),
    initialAssetId: new FormControl<string>('', [Validators.required]),
    voyageType: new FormControl<number | null>(null, [Validators.required]),
    deckPercentageUsedIn: new FormControl<number | null>(null, [
      Validators.required,
      Validators.min(0),
      decimalPoint(2),
    ]),
    deckPercentageUsedOut: new FormControl<number | null>(null, [
      Validators.required,
      Validators.min(0),
      decimalPoint(2),
    ]),
    comments: new FormControl<string | null>(null, []),
    totalMileage: new FormControl<number | null>(0, [
      Validators.min(1),
      decimalPoint(2),
    ]),
    voyageDirection: new FormControl<number | null>(null, Validators.required),
  });
  voyageTypes = voyageTypes;
  voyageStatus = VoyageStatus;
  voyageDirection = voyageDirections;

  controls = {
    voyageNumber: this.form.get('voyageNumber'),
    vesselId: this.form.get('vesselId'),
    initialAssetId: this.form.get('initialAssetId'),
    voyageType: this.form.get('voyageType'),
    deckPercentageUsedIn: this.form.get('deckPercentageUsedIn'),
    deckPercentageUsedOut: this.form.get('deckPercentageUsedOut'),
    comments: this.form.get('comments'),
    totalMileage: this.form.get('totalMileage'),
  };

  ngOnInit() {
    this.actions
      .pipe(ofType(VoyagesActions.open_Voyage_Add_Edit_Dialog))
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((x) => {
        this.pathForm(x.voyage!);
      });

    this.actions
      .pipe(
        ofType(
          VoyagesActions.add_Voyage_Success,
          VoyagesActions.edit_Voyage_Success
        )
      )
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(() => {
        this.hideDialog();
      });
  }

  pathForm(voyage: Voyage): void {
    this.form.patchValue(voyage);
  }

  hideDialog() {
    this.form.reset();
    this.form.markAsUntouched();
    this.store.dispatch(
      VoyagesActions.open_Voyage_Add_Edit_Dialog({
        isVisible: false,
        isEdit: false,
        voyage: null,
      })
    );
  }

  saveVoyage(model?: VoyageAddRequest): void {
    if (this.form.invalid) {
      this.form.markAllAsTouched();
      return;
    }
    if (!model) {
      model = {
        ...this.form.value,
      } as VoyageAddRequest;
    }

    if (
      (model.deckPercentageUsedIn ?? 0) > 100 ||
      (model.deckPercentageUsedOut ?? 0) > 100
    ) {
      this.store.dispatch(
        confirmActions.request_Confirmation({
          titles: {
            title:
              'The percentage you have input exceeds 100%, would you like to continue?',
            btnConfirm: 'Yes',
            btnReject: 'No',
          },
          confirm: !this.isEdit()
            ? VoyagesActions.add_Voyage({ voyage: model })
            : VoyagesActions.edit_Voyage({
                voyage: model,
                voyageId: this.voyage()!.voyageId,
              }),
        })
      );
      return;
    }
    if (!this.isEdit()) {
      this.store.dispatch(VoyagesActions.add_Voyage({ voyage: model }));
    } else {
      this.store.dispatch(
        VoyagesActions.edit_Voyage({
          voyage: model,
          voyageId: this.voyage()!.voyageId,
        })
      );
    }
  }
}
