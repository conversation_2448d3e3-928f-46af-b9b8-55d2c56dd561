{"extends": "./tsconfig.json", "compilerOptions": {"outDir": "../../dist/out-tsc", "declaration": true, "declarationMap": true, "inlineSources": true, "types": []}, "exclude": ["src/**/*.spec.ts", "src/test-setup.ts", "jest.config.ts", "src/**/*.test.ts"], "include": ["src/**/*.ts", "../services/src/lib/services/voyages/enums/features.enum.ts", "../services/src/lib/services/voyages/interfaces/departure-email.interface.ts", "../services/src/lib/services/maintenance/interfaces/operator-state.interface.ts"]}