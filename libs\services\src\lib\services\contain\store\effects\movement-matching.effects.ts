import { Actions, createEffect, ofType } from '@ngrx/effects';
import { inject } from '@angular/core';
import { catchError, map, mergeMap, of, switchMap, tap } from 'rxjs';
import { HttpErrorResponse } from '@angular/common/http';
import { HireRequestFilter } from '../../interfaces/hire-request-filter.interface';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MovementMatchingActions } from '../actions/movement-matching.actions';
import { MovementMatchingService } from '../../movement-matching.service';
import { MovementMatching } from '../../interfaces/movement-matching/movement-matching.interface';
import { MovementMatchingResponse } from '../../interfaces/movement-matching/movement-matching-response.interface';

export const reloadMovementMatchingExceptionsList = createEffect(
  (actions = inject(Actions)) => {
    return actions.pipe(
      ofType(
        MovementMatchingActions.delete_Movement_Matching_Success,
        MovementMatchingActions.update_Movement_Matching_Filter_Success,
        MovementMatchingActions.approve_Movement_Matching_Success
      ),
      switchMap((action) => {
        return of(
          MovementMatchingActions.load_Movement_Matching_Exceptions_List({
            filterModel: action.filterModel,
          })
        );
      })
    );
  },
  {
    functional: true,
  }
);

export const reloadMovementMatchingMatchesList = createEffect(
  (actions = inject(Actions)) => {
    return actions.pipe(
      ofType(
        MovementMatchingActions.delete_Movement_Matching_Success,
        MovementMatchingActions.update_Movement_Matching_Filter_Success,
        MovementMatchingActions.approve_Movement_Matching_Success
      ),
      switchMap((action) => {
        return of(
          MovementMatchingActions.load_Movement_Matching_Matches_List({
            filterModel: action.filterModel,
          })
        );
      })
    );
  },
  {
    functional: true,
  }
);

export const loadMovementMatchingExceptionsList = createEffect(
  (
    actions = inject(Actions),
    movementMatchingService = inject(MovementMatchingService)
  ) => {
    return actions.pipe(
      ofType(MovementMatchingActions.load_Movement_Matching_Exceptions_List),
      mergeMap((action) =>
        movementMatchingService
          .loadMovementMatchingExceptions(action.filterModel)
          .pipe(
            map((res: MovementMatching[]) =>
              MovementMatchingActions.load_Movement_Matching_Exceptions_List_Success(
                {
                  MovementMatchings: res,
                }
              )
            ),
            catchError((error: HttpErrorResponse) =>
              of(
                MovementMatchingActions.load_Movement_Matching_Exceptions_List_Failure(
                  {
                    error,
                  }
                )
              )
            )
          )
      )
    );
  },
  {
    functional: true,
  }
);

export const loadMovementMatchingMatchesList = createEffect(
  (
    actions = inject(Actions),
    movementMatchingService = inject(MovementMatchingService)
  ) => {
    return actions.pipe(
      ofType(MovementMatchingActions.load_Movement_Matching_Matches_List),
      mergeMap((action) =>
        movementMatchingService
          .loadMovementMatchingMatches(action.filterModel)
          .pipe(
            map((res: MovementMatching[]) =>
              MovementMatchingActions.load_Movement_Matching_Matches_List_Success(
                {
                  MovementMatchings: res,
                }
              )
            ),
            catchError((error: HttpErrorResponse) =>
              of(
                MovementMatchingActions.load_Movement_Matching_Matches_List_Failure(
                  {
                    error,
                  }
                )
              )
            )
          )
      )
    );
  },
  {
    functional: true,
  }
);

export const runMovementMatching = createEffect(
  (
    actions = inject(Actions),
    movementMatchingService = inject(MovementMatchingService)
  ) => {
    return actions.pipe(
      ofType(MovementMatchingActions.run_Movement_Matching),
      mergeMap((action) =>
        movementMatchingService.runMovementMatching(action.model).pipe(
          map((res: MovementMatchingResponse) => {
            return MovementMatchingActions.run_Movement_Matching_Success({
              response: res,
              filterModel: action.filterModel,
              successMessage: 'Movement Matching successfully ran',
            });
          }),
          catchError((error: HttpErrorResponse) =>
            of(
              MovementMatchingActions.run_Movement_Matching_Failure({
                error: error,
              })
            )
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const removeMovementMatching = createEffect(
  (
    actions = inject(Actions),
    movementMatchingService = inject(MovementMatchingService)
  ) => {
    return actions.pipe(
      ofType(MovementMatchingActions.delete_Movement_Matching),
      mergeMap((action) =>
        movementMatchingService
          .deleteMovementMatching(action.movementMatchingId)
          .pipe(
            map(() => {
              return MovementMatchingActions.delete_Movement_Matching_Success({
                filterModel: action.filterModel,
                successMessage: 'Movement Matching successfully deleted',
              });
            }),
            catchError((error: HttpErrorResponse) =>
              of(
                MovementMatchingActions.delete_Movement_Matching_Failure({
                  error: error,
                })
              )
            )
          )
      )
    );
  },
  {
    functional: true,
  }
);

export const approveMovementMatching = createEffect(
  (
    actions = inject(Actions),
    movementMatchingService = inject(MovementMatchingService)
  ) => {
    return actions.pipe(
      ofType(MovementMatchingActions.approve_Movement_Matching),
      mergeMap((action) =>
        movementMatchingService
          .approveMovementMatching(action.movementMatching)
          .pipe(
            map(() => {
              return MovementMatchingActions.approve_Movement_Matching_Success({
                filterModel: action.filterModel,
                successMessage:
                  'Hire Request Cargo successfully updated with Flow Voyage Data',
              });
            }),
            catchError((error: HttpErrorResponse) =>
              of(
                MovementMatchingActions.approve_Movement_Matching_Failure({
                  error: error,
                })
              )
            )
          )
      )
    );
  },
  {
    functional: true,
  }
);

export const updateMovementMatchingFilter = createEffect(
  (actions = inject(Actions)) => {
    return actions.pipe(
      ofType(MovementMatchingActions.update_Movement_Matching_Filter),
      map((action) =>
        MovementMatchingActions.update_Movement_Matching_Filter_Success({
          filterModel: action.filter,
        })
      )
    );
  },
  {
    functional: true,
  }
);
