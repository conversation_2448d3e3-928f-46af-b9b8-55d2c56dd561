trigger:
  - develop

pool:
  vmImage: 'ubuntu-latest'

steps:
  - task: NodeTool@0
    inputs:
      versionSpec: '16.x'
    displayName: 'Install Node.js'

  - script: |
      npm ci --verbose
    displayName: 'npm install'

  # Build and deploy Lighthouse app
  - script: |
      if [ "$(buildLighthouse)" == "true" ]; then
        echo "Setting up environment configuration for Lighthouse..."
        if [ "$(BuildConfiguration)" == "production" ]; then
          cp libs/env/src/environments/assets/environment.production.json libs/env/src/environments/assets/environment.json
          cp libs/env/src/environments/assets/auth_config_production.json libs/env/src/environments/assets/auth_config.json
        elif [ "$(BuildConfiguration)" == "development" ]; then
          cp libs/env/src/environments/assets/environment.development.json libs/env/src/environments/assets/environment.json
          cp libs/env/src/environments/assets/auth_config_development.json libs/env/src/environments/assets/auth_config.json
        elif [ "$(BuildConfiguration)" == "uat" ]; then
          cp libs/env/src/environments/assets/environment.uat.json libs/env/src/environments/assets/environment.json
          cp libs/env/src/environments/assets/auth_config_uat.json libs/env/src/environments/assets/auth_config.json
        else
          cp libs/env/src/environments/assets/environment.local.json libs/env/src/environments/assets/environment.json
          cp libs/env/src/environments/assets/auth_config_local.json libs/env/src/environments/assets/auth_config.json
        fi

        echo "Building Lighthouse app..."
        export NODE_OPTIONS="--max_old_space_size=$(maxOldSpaceSize)"
        npx nx build lighthouse --configuration=$(BuildConfiguration)
      fi
    displayName: 'Build Lighthouse app'

  - task: AzureCLI@2
    inputs:
      azureSubscription: '$(AzureSubscription)'
      scriptType: 'bash'
      scriptLocation: 'inlineScript'
      inlineScript: |
        if [ "$(buildLighthouse)" == "true" ]; then
          echo "Deploying Lighthouse app..."
          az storage blob delete-batch --account-name $(LighthouseDevStorage) -s $(ContainerName) --pattern "**"
          az storage blob upload-batch --account-name $(LighthouseDevStorage) -s dist --destination $(ContainerName) --pattern "**" --overwrite
          echo "Clearing dist directory for Lighthouse..."
          rm -rf dist
        fi
    displayName: 'Deploy Lighthouse app and clear dist'

  # Build and deploy Plan app
  - script: |
      echo "Clearing dist directory before Plan build..."
      rm -rf dist/*

      if [ "$(buildPlan)" == "true" ]; then
        echo "Setting up environment configuration for Plan..."
        if [ "$(BuildConfiguration)" == "production" ]; then
          cp libs/env/src/environments/assets/environment.production.json libs/env/src/environments/assets/environment.json
          cp libs/env/src/environments/assets/auth_config_production.json libs/env/src/environments/assets/auth_config.json
        elif [ "$(BuildConfiguration)" == "development" ]; then
          cp libs/env/src/environments/assets/environment.development.json libs/env/src/environments/assets/environment.json
          cp libs/env/src/environments/assets/auth_config_development.json libs/env/src/environments/assets/auth_config.json
        elif [ "$(BuildConfiguration)" == "uat" ]; then
          cp libs/env/src/environments/assets/environment.uat.json libs/env/src/environments/assets/environment.json
          cp libs/env/src/environments/assets/auth_config_uat.json libs/env/src/environments/assets/auth_config.json
        else
          cp libs/env/src/environments/assets/environment.local.json libs/env/src/environments/assets/environment.json
          cp libs/env/src/environments/assets/auth_config_local.json libs/env/src/environments/assets/auth_config.json
        fi

        echo "Building Plan app..."
        export NODE_OPTIONS="--max_old_space_size=$(maxOldSpaceSize)"
        npx nx build plan --outputPath=dist/plan --configuration=$(BuildConfiguration)
      fi
    displayName: 'Build Plan app'

  - task: AzureCLI@2
    inputs:
      azureSubscription: '$(AzureSubscription)'
      scriptType: 'bash'
      scriptLocation: 'inlineScript'
      inlineScript: |
        if [ "$(buildPlan)" == "true" ]; then
          echo "Deploying Plan app..."
          az storage blob delete-batch --account-name $(PlanDevStorage) -s $(ContainerName) --pattern "**"
          az storage blob upload-batch --account-name $(PlanDevStorage) -s dist --destination $(ContainerName) --pattern "**" --overwrite
          echo "Clearing dist directory for Plan..."
          rm -rf dist/*
        fi
    displayName: 'Deploy Plan app and clear dist'

  # Build and deploy Allocate app
  - script: |
      echo "Clearing dist directory before Allocate build..."
      rm -rf dist

      if [ "$(buildAllocate)" == "true" ]; then
        echo "Setting up environment configuration for Allocate..."
        if [ "$(BuildConfiguration)" == "production" ]; then
          cp libs/env/src/environments/assets/environment.production.json libs/env/src/environments/assets/environment.json
          cp libs/env/src/environments/assets/auth_config_production.json libs/env/src/environments/assets/auth_config.json
        elif [ "$(BuildConfiguration)" == "development" ]; then
          cp libs/env/src/environments/assets/environment.development.json libs/env/src/environments/assets/environment.json
          cp libs/env/src/environments/assets/auth_config_development.json libs/env/src/environments/assets/auth_config.json
        elif [ "$(BuildConfiguration)" == "uat" ]; then
          cp libs/env/src/environments/assets/environment.uat.json libs/env/src/environments/assets/environment.json
          cp libs/env/src/environments/assets/auth_config_uat.json libs/env/src/environments/assets/auth_config.json
        else
          cp libs/env/src/environments/assets/environment.local.json libs/env/src/environments/assets/environment.json
          cp libs/env/src/environments/assets/auth_config_local.json libs/env/src/environments/assets/auth_config.json
        fi

        echo "Building Allocate app..."
        export NODE_OPTIONS="--max_old_space_size=$(maxOldSpaceSize)"
        npx nx build allocate --outputPath=dist/allocate --configuration=$(BuildConfiguration)
      fi
    displayName: 'Build Allocate app'

  - task: AzureCLI@2
    inputs:
      azureSubscription: '$(AzureSubscription)'
      scriptType: 'bash'
      scriptLocation: 'inlineScript'
      inlineScript: |
        if [ "$(buildAllocate)" == "true" ]; then
          echo "Deploying Allocate app..."
          az storage blob delete-batch --account-name $(AllocateDevStorage) -s $(ContainerName) --pattern "**"
          az storage blob upload-batch --account-name $(AllocateDevStorage) -s dist --destination $(ContainerName) --pattern "**" --overwrite
          echo "Clearing dist directory for Allocate..."
          rm -rf dist/*
        fi
    displayName: 'Deploy Allocate app and clear dist'

  # Build and deploy Flow app
  - script: |
      echo "Clearing dist directory before Flow build..."
      rm -rf dist

      if [ "$(buildFlow)" == "true" ]; then
        echo "Setting up environment configuration for Flow..."
        if [ "$(BuildConfiguration)" == "production" ]; then
          cp libs/env/src/environments/assets/environment.production.json libs/env/src/environments/assets/environment.json
          cp libs/env/src/environments/assets/auth_config_production.json libs/env/src/environments/assets/auth_config.json
        elif [ "$(BuildConfiguration)" == "development" ]; then
          cp libs/env/src/environments/assets/environment.development.json libs/env/src/environments/assets/environment.json
          cp libs/env/src/environments/assets/auth_config_development.json libs/env/src/environments/assets/auth_config.json
        elif [ "$(BuildConfiguration)" == "uat" ]; then
          cp libs/env/src/environments/assets/environment.uat.json libs/env/src/environments/assets/environment.json
          cp libs/env/src/environments/assets/auth_config_uat.json libs/env/src/environments/assets/auth_config.json
        else
          cp libs/env/src/environments/assets/environment.local.json libs/env/src/environments/assets/environment.json
          cp libs/env/src/environments/assets/auth_config_local.json libs/env/src/environments/assets/auth_config.json
        fi

        echo "Building Flow app..."
        export NODE_OPTIONS="--max_old_space_size=$(maxOldSpaceSize)"
        npx nx build flow --outputPath=dist/flow --configuration=$(BuildConfiguration)
      fi
    displayName: 'Build Flow app'

  - task: AzureCLI@2
    inputs:
      azureSubscription: '$(AzureSubscription)'
      scriptType: 'bash'
      scriptLocation: 'inlineScript'
      inlineScript: |
        if [ "$(buildFlow)" == "true" ]; then
          echo "Deploying Flow app..."
          az storage blob delete-batch --account-name $(FlowDevStorage) -s $(ContainerName) --pattern "**"
          az storage blob upload-batch --account-name $(FlowDevStorage) -s dist --destination $(ContainerName) --pattern "**" --overwrite
          echo "Clearing dist directory for Flow..."
          rm -rf dist/*
        fi
    displayName: 'Deploy Flow app and clear dist'

    # Build and deploy Request app
  - script: |
      echo "Clearing dist directory before Request build..."
      rm -rf dist

      if [ "$(buildRequest)" == "true" ]; then
        echo "Setting up environment configuration for Request..."
        if [ "$(BuildConfiguration)" == "production" ]; then
          cp libs/env/src/environments/assets/environment.production.json libs/env/src/environments/assets/environment.json
          cp libs/env/src/environments/assets/auth_config_production.json libs/env/src/environments/assets/auth_config.json
        elif [ "$(BuildConfiguration)" == "development" ]; then
          cp libs/env/src/environments/assets/environment.development.json libs/env/src/environments/assets/environment.json
          cp libs/env/src/environments/assets/auth_config_development.json libs/env/src/environments/assets/auth_config.json
        elif [ "$(BuildConfiguration)" == "uat" ]; then
          cp libs/env/src/environments/assets/environment.uat.json libs/env/src/environments/assets/environment.json
          cp libs/env/src/environments/assets/auth_config_uat.json libs/env/src/environments/assets/auth_config.json
        else
          cp libs/env/src/environments/assets/environment.local.json libs/env/src/environments/assets/environment.json
          cp libs/env/src/environments/assets/auth_config_local.json libs/env/src/environments/assets/auth_config.json
        fi

        echo "Building Request app..."
        export NODE_OPTIONS="--max_old_space_size=$(maxOldSpaceSize)"
        npx nx build request --outputPath=dist/request --configuration=$(BuildConfiguration)
      fi
    displayName: 'Build Request app'

  - task: AzureCLI@2
    inputs:
      azureSubscription: '$(AzureSubscription)'
      scriptType: 'bash'
      scriptLocation: 'inlineScript'
      inlineScript: |
        if [ "$(buildRequest)" == "true" ]; then
          echo "Deploying Request app..."
          az storage blob delete-batch --account-name $(RequestDevStorage) -s $(ContainerName) --pattern "**"
          az storage blob upload-batch --account-name $(RequestDevStorage) -s dist --destination $(ContainerName) --pattern "**" --overwrite
          echo "Clearing dist directory for Request..."
          rm -rf dist/*
        fi
    displayName: 'Deploy Request app and clear dist'

  # Build and deploy Contain app
  - script: |
      echo "Clearing dist directory before Contain build..."
      rm -rf dist
      if [ "$(buildContain)" == "true" ]; then
        echo "Setting up environment configuration for Contain..."
        if [ "$(BuildConfiguration)" == "production" ]; then
          cp libs/env/src/environments/assets/environment.production.json libs/env/src/environments/assets/environment.json
          cp libs/env/src/environments/assets/auth_config_production.json libs/env/src/environments/assets/auth_config.json
        elif [ "$(BuildConfiguration)" == "development" ]; then
          cp libs/env/src/environments/assets/environment.development.json libs/env/src/environments/assets/environment.json
          cp libs/env/src/environments/assets/auth_config_development.json libs/env/src/environments/assets/auth_config.json
        elif [ "$(BuildConfiguration)" == "uat" ]; then
          cp libs/env/src/environments/assets/environment.uat.json libs/env/src/environments/assets/environment.json
          cp libs/env/src/environments/assets/auth_config_uat.json libs/env/src/environments/assets/auth_config.json
        else
          cp libs/env/src/environments/assets/environment.local.json libs/env/src/environments/assets/environment.json
          cp libs/env/src/environments/assets/auth_config_local.json libs/env/src/environments/assets/auth_config.json
        fi
        echo "Building Contain app..."
        npx nx build contain --outputPath=dist/contain --configuration=$(BuildConfiguration)
      fi
    displayName: 'Build Contain app'
  - task: AzureCLI@2
    inputs:
      azureSubscription: '$(AzureSubscription)'
      scriptType: 'bash'
      scriptLocation: 'inlineScript'
      inlineScript: |
        if [ "$(buildContain)" == "true" ]; then
          echo "Deploying Contain app..."
          az storage blob delete-batch --account-name $(ContainDevStorage) -s $(ContainerName) --pattern "**"
          az storage blob upload-batch --account-name $(ContainDevStorage) -s dist --destination $(ContainerName) --pattern "**" --overwrite
          echo "Clearing dist directory for Contain..."
          rm -rf dist/*
        fi
    displayName: 'Deploy Contain app and clear dist'

  # Build and deploy Master Data app
  - script: |
      echo "Clearing dist directory before Master Data build..."
      rm -rf dist

      if [ "$(buildMasterData)" == "true" ]; then
        echo "Setting up environment configuration for Master Data..."
        if [ "$(BuildConfiguration)" == "production" ]; then
          cp libs/env/src/environments/assets/environment.production.json libs/env/src/environments/assets/environment.json
          cp libs/env/src/environments/assets/auth_config_production.json libs/env/src/environments/assets/auth_config.json
        elif [ "$(BuildConfiguration)" == "development" ]; then
          cp libs/env/src/environments/assets/environment.development.json libs/env/src/environments/assets/environment.json
          cp libs/env/src/environments/assets/auth_config_development.json libs/env/src/environments/assets/auth_config.json
        elif [ "$(BuildConfiguration)" == "uat" ]; then
          cp libs/env/src/environments/assets/environment.uat.json libs/env/src/environments/assets/environment.json
          cp libs/env/src/environments/assets/auth_config_uat.json libs/env/src/environments/assets/auth_config.json
        else
          cp libs/env/src/environments/assets/environment.local.json libs/env/src/environments/assets/environment.json
          cp libs/env/src/environments/assets/auth_config_local.json libs/env/src/environments/assets/auth_config.json
        fi

        echo "Building Master Data app..."
        export NODE_OPTIONS="--max_old_space_size=$(maxOldSpaceSize)"
        npx nx build masterdata --outputPath=dist/masterdata --configuration=$(BuildConfiguration)
      fi
    displayName: 'Build Master Data app'

  - task: AzureCLI@2
    inputs:
      azureSubscription: '$(AzureSubscription)'
      scriptType: 'bash'
      scriptLocation: 'inlineScript'
      inlineScript: |
        if [ "$(buildMasterData)" == "true" ]; then
          echo "Deploying Master Data app..."
          az storage blob delete-batch --account-name $(MasterDataDevStorage) -s $(ContainerName) --pattern "**"
          az storage blob upload-batch --account-name $(MasterDataDevStorage) -s dist --destination $(ContainerName) --pattern "**" --overwrite
          echo "Clearing dist directory for Master Data..."
          rm -rf dist/*
        fi
    displayName: 'Deploy Master Data app and clear dist'
