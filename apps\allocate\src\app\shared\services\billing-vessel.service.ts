import { inject, Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { Vessel } from 'libs/services/src/lib/services/vessels/interfaces/vessel.interface';
import { BillingVesselView } from '../../shared/interfaces/billing-vessel-view.interface';

@Injectable({
  providedIn: 'root',
})
export class BillingVesselService {
  private readonly http = inject(HttpClient);

  loadBillingVesselList(id: string): Observable<Vessel[]> {
    return this.http.get<Vessel[]>(
      `/api/vesselbillingperiod/getbybillingperiod/${id}`
    );
  }

  loadBillingVesselVoyageList(
    billingPeriodId: string,
    billingVesselId: string
  ): Observable<BillingVesselView> {
    return this.http.get<BillingVesselView>(
      `/api/vesselbillingperiod/getbybillingvessel/${billingPeriodId}/${billingVesselId}`
    );
  }

  exportBillingVessel(id: string, vesselId: string): Observable<ArrayBuffer> {
    return this.http.get(
      `/api/vesselbillingperiod/exportbillingvessel/${id}/${vesselId}`,
      {
        responseType: 'arraybuffer',
      }
    );
  }

  changeVoyageBillingPeriod(
    billingPeriodDateYear: Date | null,
    voyageId: string
  ): Observable<void> {
    return this.http.put<void>(`/api/voyage/updatebillingperiod/${voyageId}`, {
      billingPeriodDateYear,
    });
  }
}
