import { createFeature, createReducer, createSelector } from '@ngrx/store';
import { immerOn } from 'ngrx-immer/store';
import { BillingVesselsActions } from '../actions/billing-vessel.actions';
import { routerNavigationAction } from '@ngrx/router-store';
import { billingVesselState } from '../states/billing-vessel.state';
import { Vessel } from 'libs/services/src/lib/services/vessels/interfaces/vessel.interface';

export const billingVesselReducer = createReducer(
  billingVesselState,
  immerOn(
    BillingVesselsActions.load_Billing_Vessels_Success,
    (state, { billingVessels }) => {
      state.billingVessels = billingVessels;
    }
  ),

  immerOn(
    BillingVesselsActions.load_Billing_Vessel_Voyages_Success,
    (state, { billingVesselVoyages }) => {
      state.billingVesselView = billingVesselVoyages;
    }
  ),
  immerOn(
    BillingVesselsActions.load_Billing_Vessels,
    routerNavigationAction,
    (state) => {
      state.loading.list = true;
      state.loading.voyageList = true;
    }
  ),
  immerOn(
    BillingVesselsActions.load_Billing_Vessels_Failure,
    BillingVesselsActions.load_Billing_Vessels_Success,
    (state) => {
      state.loading.list = false;
    }
  ),
  immerOn(
    BillingVesselsActions.load_Billing_Vessel_Voyages_Failure,
    BillingVesselsActions.load_Billing_Vessel_Voyages_Success,
    (state) => {
      state.loading.voyageList = false;
    }
  ),
  immerOn(BillingVesselsActions.export_Billing_Vessel, (state) => {
    state.loading.export = true;
  }),
  immerOn(
    BillingVesselsActions.export_Billing_Vessel_Success,
    BillingVesselsActions.export_Billing_Vessel_Failure,
    (state) => {
      state.loading.export = false;
    }
  ),
  immerOn(BillingVesselsActions.change_Billing_Period, (state) => {
    state.loading.changeBillingPeriod = true;
  }),
  immerOn(
    BillingVesselsActions.change_Billing_Period_Success,
    BillingVesselsActions.change_Billing_Period_Failure,
    (state) => {
      state.loading.changeBillingPeriod = false;
    }
  ),
  immerOn(
    BillingVesselsActions.change_visibility_billing_period,
    (state, payload) => {
      state.isVisibleBillingPeriod = payload.visible;
    }
  ),
  immerOn(BillingVesselsActions.change_Billing_Period_Success, (state) => {
    state.isVisibleBillingPeriod = false;
  })
);

export const billingVesselsFeature = createFeature({
  name: 'billingVessels',
  reducer: billingVesselReducer,
  extraSelectors: ({
    selectLoading,
    selectBillingVessels,
    selectBillingVesselId,
  }) => ({
    selectListLoader: createSelector(
      selectLoading,
      (selectLoading) => selectLoading.list
    ),
    selectVesselById:
      createSelector(
        selectBillingVessels,
        selectBillingVesselId,
        (selectBillingVessels, selectBillingVesselId) =>
          selectBillingVessels.find(
            (item: Vessel) => item.vesselId === selectBillingVesselId
          )
      ) || ({} as Vessel),
  }),
});
