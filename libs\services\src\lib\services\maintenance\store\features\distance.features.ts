import { createFeature, createReducer, createSelector } from '@ngrx/store';
import { immerOn } from 'ngrx-immer/store';
import { distanceInitialState } from '../states/distance.state';
import { DistanceActions } from '../actions/distance.actions';

export const distancesReducer = createReducer(
  distanceInitialState,
  immerOn(
    DistanceActions.change_visibility_add_edit,
    (state, { visible, distance }) => {
      state.isVisibleAddEdit = visible;
      state.distance = distance;
    }
  ),
  immerOn(
    DistanceActions.add_Distance_Success,
    DistanceActions.edit_Distance_Success,
    (state) => {
      state.isVisibleAddEdit = false;
      state.distance = null;
    }
  ),
  immerOn(
    DistanceActions.load_All_Distances,
    DistanceActions.remove_Distance,
    (state) => {
      state.loading.list = true;
    }
  ),
  immerOn(
    DistanceActions.load_All_Distances_Success,
    (state, { distances }) => {
      state.allDistances = distances;
      state.loading.list = false;
    }
  ),
  immerOn(
    DistanceActions.load_All_Distances_Failure,
    DistanceActions.remove_Distance_Success,
    DistanceActions.remove_Distance_Failure,
    (state) => {
      state.loading.list = false;
    }
  ),
  immerOn(
    DistanceActions.add_Distance,
    DistanceActions.edit_Distance,
    (state) => {
      state.loading.createEdit = true;
    }
  ),
  immerOn(
    DistanceActions.add_Distance_Success,
    DistanceActions.add_Distance_Failure,
    DistanceActions.edit_Distance_Success,
    DistanceActions.edit_Distance_Failure,
    (state) => {
      state.loading.createEdit = false;
    }
  )
);

export const distancesFeature = createFeature({
  name: 'distances',
  reducer: distancesReducer,
  extraSelectors: ({ selectLoading }) => ({
    selectListLoader: createSelector(
      selectLoading,
      (selectLoading) => selectLoading.list
    ),
    selectCreateEditLoader: createSelector(
      selectLoading,
      (selectLoading) => selectLoading.createEdit
    ),
  }),
});
