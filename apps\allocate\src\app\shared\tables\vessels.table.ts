import { ColumnModel } from 'libs/components/src/lib/models/column.model';
import { VesselsTableFields } from '../enums/vessels-table-fields.enum';

export function InitializeVesselsTable(): ColumnModel[] {
  const columns = [
    new ColumnModel(VesselsTableFields.name, 'Vessel Name', 150, {
      sortable: true,
    }),
    new ColumnModel(VesselsTableFields.imo, 'IMO', 150, {
      sortable: true,
    }),
    new ColumnModel(VesselsTableFields.vesselOwner, 'Owner', 150, {
      sortable: true,
    }),
    new ColumnModel(VesselsTableFields.createdByName, 'Created By', 150, {
      sortable: true,
    }),
    new ColumnModel(VesselsTableFields.updatedByName, 'Last Updated By', 150, {
      sortable: true,
    }),
    new ColumnModel(VesselsTableFields.actions, '', 100),
  ];
  return columns;
}
