import { Component, inject, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild } from '@angular/core';
import { <PERSON><PERSON><PERSON><PERSON>, MatSidenavModule } from '@angular/material/sidenav';
import { RouterOutlet } from '@angular/router';
import { PageContainerComponent } from '../../../../../libs/components/src/lib/components/page-container/page-container.component';
import { MatIconModule } from '@angular/material/icon';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatButtonModule } from '@angular/material/button';
import { MatListModule } from '@angular/material/list';
import { NgFor, NgIf, CommonModule, DatePipe } from '@angular/common';
import { MatTabsModule } from '@angular/material/tabs';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatTooltipModule } from '@angular/material/tooltip';
import { Store } from '@ngrx/store';
import { InboundOutboundSelectionComponent } from './request-sailing/inbound-outbound-selection/inbound-outbound-selection.component';
import { LookaheadActions } from 'libs/services/src/lib/services/lookahead/store/actions/lookahead.action';
import { lookaheadFeature } from 'libs/services/src/lib/services/lookahead/store/features';
import {
  map,
  Observable,
  startWith,
  Subject,
  Subscription,
  takeUntil,
  tap,
} from 'rxjs';
import { Vessel } from 'libs/services/src/lib/services/vessels/interfaces/vessel.interface';
import { currentUserFeature } from 'libs/auth/src/lib/store/current-user/current-user.features';
import { Voyage } from 'libs/services/src/lib/services/voyages/interfaces/voyage.interface';
import { ClientLocation } from 'libs/services/src/lib/services/client-locations/interfaces/client-locations.interface';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { RequestSailingComponent } from './request-sailing/request-sailing.component';
import { ActivityCategory } from '../../../../../libs/services/src/lib/services/maintenance/interfaces/activity-category.interface';
import {
  EventSettingsModel,
  TimeScaleModel,
} from '@syncfusion/ej2-angular-schedule';
import { SailingRequest } from '../../../../../libs/services/src/lib/services/lookahead/interfaces/sailing-request.interface';
import { LookaheadSharedService } from 'libs/services/src/lib/services/shared/lookahead-subject.service';
import { SingleSelectComponent } from 'libs/components/src/lib/components/single-select/single-select.component';
import {
  NgxMatSelectSearchModule,
  MatSelectSearchComponent,
} from 'ngx-mat-select-search';
import { FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { SchedulerComponent } from './scheduler/scheduler.component';
import { SearchQuery } from '../../../../../libs/components/src/lib/interfaces/search-query.interface';
import { maintenanceInitialState } from '../../../../../libs/services/src/lib/services/maintenance/store/states/maintenance.state';
import { User } from 'libs/auth/src/lib/interfaces/user.interface';
import { RoleCheckService } from 'libs/services/src/lib/services/shared/role-check.service';
import {
  getStartOfWeek,
  getEndOfWeek,
  formatDate,
} from 'libs/components/src/lib/functions/utility.functions';
import { AssetActions } from 'libs/services/src/lib/services/maintenance/store/actions/assets.actions';
import { stripTimezoneOffset } from '../../../../../libs/services/src/lib/services/functions/convert-date.utils';

@Component({
  selector: 'lookahead',
  templateUrl: './lookahead.component.html',
  styleUrls: ['./lookahead.component.scss'],
  standalone: true,
  imports: [
    MatSidenavModule,
    MatIconModule,
    MatButtonModule,
    MatToolbarModule,
    MatListModule,
    NgIf,
    NgFor,
    MatTabsModule,
    MatInputModule,
    MatSelectModule,
    MatDatepickerModule,
    MatTooltipModule,
    InboundOutboundSelectionComponent,
    CommonModule,
    MatProgressSpinnerModule,
    RequestSailingComponent,
    NgxMatSelectSearchModule,
    ReactiveFormsModule,
    SchedulerComponent,
  ],
  providers: [DatePipe, MatSelectSearchComponent],
})
export class LookaheadComponent implements OnInit, OnDestroy {
  private subscriptions = new Subscription();
  store = inject(Store);
  sharedService = inject(LookaheadSharedService);
  datePipe = inject(DatePipe);
  roleCheckService = inject(RoleCheckService);
  vm$ = this.store.select(lookaheadFeature.selectLookaheadsState);
  currentUser$ = this.store.select(currentUserFeature.selectUser);
  sailingRequsts$ = this.store.select(lookaheadFeature.selectRequestSailings);
  locationClients$ = this.store.select(lookaheadFeature.selectLocationClients);
  title = 'material-responsive-sidenav';
  @ViewChild(MatSidenav) sidenav!: MatSidenav;
  isMobile = true;
  isScheduleSailingOpened = false;
  isRequestSailingOpened = false;
  isInboundOutboundSidenavOpened = false;
  currentUser: User | undefined = undefined;
  vessels: Vessel[] = [];
  startDate: Date = new Date();
  endDate: Date = new Date();
  private destroy$ = new Subject<void>();
  voyageType: string = 'Inbound';
  inboundVoyages: Voyage[] = [];
  outboundVoyages: Voyage[] = [];
  locationClient: ClientLocation | undefined = undefined;
  activityCategories: ActivityCategory[] = [];
  locationClients: ClientLocation[] = [];
  originalLocationClients: ClientLocation[] = [];
  clusterAssets: any[] = [];
  public events: Array<any> = [];
  public timeScaleOptions: TimeScaleModel = { enable: false };
  public scheduledEventSettings: EventSettingsModel = {
    dataSource: this.events,
  };
  public requestedEventSettings: EventSettingsModel = {
    dataSource: this.events,
  };
  searchClientControl = new FormControl('');
  filteredClients$!: Observable<ClientLocation[]>;
  sailingRequests: SailingRequest[] = [];
  query: SearchQuery = maintenanceInitialState.query;
  units: any[] = [];
  selectedTabIndex = 0;
  form = new FormGroup({
    clientId: new FormControl<string>(''),
  });

  ngOnInit(): void {
    const selectedDate = new Date();
    const startDate = getStartOfWeek(selectedDate);
    const endDate = getEndOfWeek(selectedDate);

    localStorage.setItem('weekStartDate', formatDate(startDate));
    localStorage.setItem('weekEndDate', formatDate(endDate));
    this.subToActions();
    this.subscriptions.add(
      this.sharedService.openRequestedInboundOutboundVoyageSubject.subscribe(
        (data) => {
          this.handleInboundOutboundSidenav(data);
        }
      )
    );
    this.subscriptions.add(
      this.sharedService.closeInboundOutboundSidenavSubject.subscribe(() => {
        this.closeInboundOutboundSidenav();
      })
    );
  }

  subToActions() {
    this.currentUser$.pipe(takeUntil(this.destroy$)).subscribe((user) => {
      this.store.dispatch(AssetActions.load_Offshore_Assets_By_Location());
      this.currentUser = user;
      if (user?.locationId) {
        this.store.dispatch(
          LookaheadActions.load_Sailing_Request_Lists({
            locationId: user.locationId,
          })
        );
        this.store.dispatch(
          LookaheadActions.load_Location_Clients({
            locationId: user.locationId,
          })
        );
        this.locationClients$
          .pipe(takeUntil(this.destroy$))
          .subscribe((locationClients) => {
            this.locationClients = locationClients;
            this.originalLocationClients = locationClients;
            this.locationClient = locationClients.find(
              (v: any) => v.locationId === user.locationId
            );
            if (this.locationClient) {
              this.sailingRequsts$
                .pipe(takeUntil(this.destroy$))
                .subscribe((sailingRequests) => {
                  if (sailingRequests) {
                    this.sailingRequests = sailingRequests;
                    this.events = this.transformEventData(sailingRequests);
                    this.requestedEventSettings = { dataSource: this.events };
                  }
                });
            }
          });
        this.filters();
        this.store.dispatch(
          LookaheadActions.load_Location_Assets({ locationId: user.locationId })
        );
      }
    });
    this.store.dispatch(LookaheadActions.load_Voyages());
    this.store.dispatch(LookaheadActions.load_Activities());
    this.vm$.pipe(takeUntil(this.destroy$)).subscribe((cat) => {
      this.activityCategories = cat.activityCategories.filter(
        (cat: any) => !cat.isHidden
      );
    });
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
    this.destroy$.next();
    this.destroy$.complete();
  }

  onClientSelectionChange(clientId: string) {
    const filteredSailingRequests = this.sailingRequests.filter(
      (sr) => sr.clientId === clientId
    );
    this.events = this.transformEventData(filteredSailingRequests);
    if (this.locationClient?.locationPlanMode === 0) {
      this.requestedEventSettings = { dataSource: this.events };
      this.scheduledEventSettings = {
        dataSource: this.events.filter(
          (x) =>
            x.VesselId !== null &&
            (x.InboundVoyageId !== null || x.OutboundVoyageId !== null)
        ),
      };
    } else {
      this.scheduledEventSettings = { dataSource: this.events };
    }
  }

  clearSelection(event: Event) {
    event.stopPropagation();
    this.form.get('clientId')?.setValue('');
    this.events = this.transformEventData(this.sailingRequests);
    if (this.locationClient?.locationPlanMode === 0) {
      this.requestedEventSettings = { dataSource: this.events };
      this.scheduledEventSettings = {
        dataSource: this.events.filter(
          (x) =>
            x.VesselId !== null &&
            (x.InboundVoyageId !== null || x.OutboundVoyageId !== null)
        ),
      };
    } else {
      this.scheduledEventSettings = { dataSource: this.events };
    }
  }

  private filters(): void {
    this.searchClientControl.valueChanges
      .pipe(
        startWith(''),
        map((value) => this._filterClients(value!))
      )
      .subscribe((filteredClients) => {
        this.locationClients = filteredClients;
      });
  }

  private _filterClients(value: string): ClientLocation[] {
    if (value === '') {
      return this.originalLocationClients;
    }

    const filterValue = value.toLowerCase();
    const numericFilterValue = value.toString().toLowerCase();

    return this.originalLocationClients!.filter(
      (client) =>
        client.clientName.toLowerCase().includes(filterValue) ||
        client.clientName.toLowerCase().includes(numericFilterValue)
    );
  }

  transformEventData(data: SailingRequest[]) {
    return data.map((event) => {
      //syncfusion scheduler needs to adds 1 minute to the end time for it to display correctly

      const startTime =
        event.startEta !== null
          ? new Date(new Date(event.startEta).toDateString())
          : event.startTime !== null
          ? stripTimezoneOffset(new Date(event.startTime))
          : stripTimezoneOffset(new Date(event.arrivalTime!));

      let endTime = null;

      endTime =
        event.endEtd !== null
          ? new Date(new Date(event.endEtd).toDateString())
          : event.endTime !== null
          ? stripTimezoneOffset(new Date(event.endTime))
          : stripTimezoneOffset(new Date(event.arrivalTime!));

      if (event.endTime !== null) {
        endTime = new Date(event.endTime!);
        endTime.setMinutes(endTime.getMinutes() + 1);
      }

      const isSingleDay =
        startTime &&
        endTime &&
        new Date(startTime).toDateString() === new Date(endTime).toDateString();

      let displayClusterName = event.clusterName;
      if (
        !displayClusterName &&
        event.sailingRequestAssets &&
        event.sailingRequestAssets.length > 0
      ) {
        const assetNames = event.sailingRequestAssets
          .map((asset) => asset.name || asset.name)
          .filter((name) => name);

        if (assetNames.length > 1) {
          // For single day, just show the first asset (saves space on the timeline)
          if (isSingleDay) {
            displayClusterName = assetNames[0];
          } else {
            displayClusterName = `${assetNames[0]} (+${
              assetNames.length - 1
            } more)`;
          }
        } else if (assetNames.length === 1) {
          displayClusterName = assetNames[0];
        }
      }

      const eventData = {
        Id: event.sailingRequestId,
        StartTime: event.startTime,
        EndTime: endTime,
        ClientName: event.clientName,
        ClientId: event.clientId,
        VesselName: event.vesselName,
        VesselId: event.vesselId,
        Eta:
          event.startEta !== null
            ? new Date(event.startEta).toTimeString().split(' ')[0]
            : event.eta,
        Etd:
          event.endEtd !== null
            ? new Date(event.endEtd).toTimeString().split(' ')[0]
            : event.etd,
        InboundVoyageId: event.inboundVoyageId,
        OutboundVoyageId: event.outboundVoyageId,
        SailingRequestAssets: event.sailingRequestAssets,
        SailingRequestActivities: event.sailingRequestActivities,
        SeriesStartTime:
          event.seriesStartTime !== null
            ? new Date(event.seriesStartTime!)
            : null,
        SeriesEndTime:
          event.seriesEndTime !== null ? new Date(event.seriesEndTime!) : null,
        WeeklyPattern: event.weeklyPattern,
        Type: event.type,
        SailingRequestUserComments: event.sailingRequestUserComments,
        Status: event.status,
        ClusterId: event.clusterID,
        ClusterTime: event.clusterTime,
        TimeUnit: event.timeUnit,
        IsMailbag: event.isMailbag,
        IsBulkReq: event.isBulkReq,
        ClientReference: event.clientReference,
        Remarks: event.remarks,
        Comment: event.comment,
        IsFlexableTiming: event.isFlexableTiming,
        ArrivalTime: event.arrivalTime,
        FirstInstallationTime: event.firstInstallationTime,
        LatestArrivalTime: event.latestArrivalTime,
        IsInbound: event.isInbound,
        IsOutbound: event.isOutbound,
        IsInterfield: event.isInterfield,
        TransportRequests: event.transportRequests,
        ClusterName: displayClusterName,
      };

      console.log('eventData', eventData);

      return eventData;
    });
  }

  hasOnlyCliRoleInPlan(): boolean {
    return this.roleCheckService.hasOnlyCliRoleInPlan(this.currentUser);
  }

  toggleRequest(sailingRequest?: SailingRequest) {
    const isPoolMode = this.locationClient?.locationPlanMode === 0;
    if (isPoolMode) {
      if (sailingRequest && this.isRequestSailingOpened) {
        return;
      } else if (!sailingRequest && !this.isRequestSailingOpened) {
        this.isRequestSailingOpened = true;
      } else {
        this.sharedService.editPoolSailingRequest(sailingRequest);
        this.isRequestSailingOpened = true;
      }
    }
  }

  sidenavOpenedChange(opened: boolean) {
    !opened ? (this.isInboundOutboundSidenavOpened = false) : true;
    this.isScheduleSailingOpened = opened;
  }

  inboundOutboundSidenavOpenedChange(opened: boolean) {
    this.isInboundOutboundSidenavOpened = opened;
  }

  updateDateRange(dateRange: { startDate: Date; endDate: Date }) {
    this.startDate = dateRange.startDate;
    this.endDate = dateRange.endDate;
  }

  handleInboundOutboundSidenav(event: {
    isOpen: boolean;
    voyageType: string;
    vesselId: string;
  }) {
    this.isInboundOutboundSidenavOpened = event.isOpen;
    this.voyageType = event.voyageType;
    if (this.voyageType === 'Inbound') {
      this.vm$.pipe(takeUntil(this.destroy$)).subscribe((vm) => {
        this.inboundVoyages = vm.voyages.filter(
          (v: any) => v.vesselId === event.vesselId && v.voyageDirection === 0
        );
      });
    } else {
      this.vm$.pipe(takeUntil(this.destroy$)).subscribe((vm) => {
        this.outboundVoyages = vm.voyages.filter(
          (v: any) => v.vesselId === event.vesselId && v.voyageDirection === 1
        );
      });
    }
  }

  closeInboundOutboundSidenav() {
    this.isInboundOutboundSidenavOpened = false;
  }
}
