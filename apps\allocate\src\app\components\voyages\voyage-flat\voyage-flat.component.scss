.voyage_flat {
  &__container {
    display: grid;
    flex-wrap: wrap;
    row-gap: 30px;
    column-gap: 2%;
    grid-template-columns: repeat(5, 1fr);
  }
  padding: 20px 0;
  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    &_left {
    }
    &_right {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      justify-content: flex-end;
    }
  }
}

@media (max-width: 1400px) {
  .voyage_flat {
    &__container {
      grid-template-columns: repeat(4, 1fr);
    }
  }
}

@media (max-width: 1100px) {
  .voyage_flat {
    &__container {
      grid-template-columns: repeat(3, 1fr);
    }
  }
}

@media (max-width: 900px) {
  .voyage_flat {
    &__container {
      grid-template-columns: repeat(2, 1fr);
    }
  }
}

@media (max-width: 767px) {
  .voyage_flat {
    &__header {
      display: block;
    }
  }
}

@media (max-width: 650px) {
  .voyage_flat {
    &__container {
      grid-template-columns: repeat(1, 1fr);
    }
  }
}
