<p-dialog
  [draggable]="true"
  [closable]="true"
  [modal]="true"
  [visible]="isVisible()"
  (visibleChange)="hideDialog()"
  [style]="{ width: '700px' }"
>
  <ng-template pTemplate="header">
    <div class="header">{{ operator() ? 'Edit' : 'Add' }} Operator</div>
  </ng-template>
  <ng-template pTemplate="content">
    <form [formGroup]="form" class="d-flex flex-wrap gap-16">
      <div class="d-flex w-100 justify-content-between gap-8">
        <div class="d-flex w-50 align-items-center gap-4">
          <img
            *ngIf="photoUrl; else emptyImage"
            [src]="photoUrl"
            width="80"
            height="80"
          />
          <ng-template #emptyImage>
            <img
              src="assets/images/no-profile-picture.svg"
              width="80"
              height="80"
            />
          </ng-template>

          <div
            class="d-flex flex-direction-column gap-8 justify-content-center"
          >
            <button (click)="selectFile()" type="button" class="btn-tertiary">
              Upload Photo
            </button>
            <button
              *ngIf="logCtrl.value || photoUrl"
              (click)="removePhoto()"
              type="button"
              class="btn-tertiary"
            >
              Remove Photo
            </button>
          </div>
        </div>
      </div>
      <div class="d-flex w-100 justify-content-between gap-8">
        <div class="d-flex w-50 flex-direction-column gap-4">
          <label class="fs-14">Operator name</label>
          <input formControlName="name" type="text" pInputText />
          <small
            class="validation-control-error"
            *ngIf="controls.name?.invalid && controls.name?.touched"
          >
            Operator name is required.
          </small>
        </div>
        <div class="d-flex w-50 flex-direction-column gap-4">
          <label class="fs-14">Locations</label>
          <p-multiSelect
            #multiSelectLocation
            [options]="locations()"
            formControlName="locationIds"
            optionLabel="name"
            optionValue="locationId"
            placeholder="Select"
            appendTo="body"
            [showToggleAll]="!multiSelectLocation.isEmpty()"
            display="comma"
            [showClear]="!!controls.locationIds?.value?.length"
            styleClass="new-version-multiselect"
            panelStyleClass="new-version-panel"
          ></p-multiSelect>
          <small
            class="validation-control-error"
            *ngIf="
              controls.locationIds?.invalid && controls.locationIds?.touched
            "
          >
            Locations is required.
          </small>
        </div>
      </div>
      <div class="d-flex w-100 justify-content-between gap-8">
        <div class="d-flex w-50 flex-direction-column gap-4">
          <label class="fs-14">VAT Number</label>
          <input formControlName="vatNumber" type="text" pInputText />
        </div>
        <div class="d-flex w-50 flex-direction-column gap-4">
          <label class="fs-14">EU Number</label>
          <input formControlName="euNumber" type="text" pInputText />
        </div>
      </div>
      <div class="d-flex w-100 justify-content-between gap-8">
        <div class="d-flex w-50 align-items-center gap-4">
          <p-inputSwitch formControlName="customsCompliant" />
          <label class="fs-14">Custom Compliant</label>
        </div>
        <div class="d-flex w-50 align-items-center gap-4">
          <p-inputSwitch formControlName="isActive" />
          <label class="fs-14">Is Active</label>
        </div>
      </div>
    </form>
  </ng-template>
  <ng-template pTemplate="footer">
    <div class="actions">
      <button class="btn-tertiary" type="button" (click)="hideDialog()">
        Cancel
      </button>
      <button class="btn-primary" type="button" (click)="onSubmit()">
        {{ operator() ? 'Save' : 'Add' }}
      </button>
    </div>
  </ng-template>
</p-dialog>
<lib-cropping-image-dialog
  [visible]="visibleCropDialog"
  [imageChangedEvent]="imageChangedEvent"
  (hideDialog)="visibleCropDialog = false"
  (saveImage)="saveImage($event)"
/>
