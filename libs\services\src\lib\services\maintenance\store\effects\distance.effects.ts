import { Actions, createEffect, ofType } from '@ngrx/effects';
import { inject } from '@angular/core';
import { catchError, map, mergeMap, of } from 'rxjs';
import { HttpErrorResponse } from '@angular/common/http';
import { switchMap } from 'rxjs/operators';

import { DistanceService } from '../../distance.service';
import { DistanceActions } from '../actions/distance.actions';
import { Distance } from '../../interfaces/distance.interface';
import { AssetActions } from '../actions/assets.actions';
import { FileService } from '../../../file.service';

export const distancesInit = createEffect(
  (actions = inject(Actions)) => {
    return actions.pipe(
      ofType(DistanceActions.init_distances),
      switchMap(() =>
        of(AssetActions.load_Assets(), DistanceActions.load_All_Distances())
      )
    );
  },
  { functional: true }
);

export const loadAllDistances = createEffect(
  (actions = inject(Actions), distanceService = inject(DistanceService)) => {
    return actions.pipe(
      ofType(
        DistanceActions.load_All_Distances,
        DistanceActions.remove_Distance_Success,
        DistanceActions.add_Distance_Success,
        DistanceActions.edit_Distance_Success
      ),
      mergeMap(() =>
        distanceService.loadAllDistances().pipe(
          map((distances) => {
            return DistanceActions.load_All_Distances_Success({ distances });
          }),
          catchError((error: HttpErrorResponse) =>
            of(DistanceActions.load_All_Distances_Failure({ error }))
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const removeDistance = createEffect(
  (actions = inject(Actions), distanceService = inject(DistanceService)) => {
    return actions.pipe(
      ofType(DistanceActions.remove_Distance),
      mergeMap((action) =>
        distanceService.removeDistance(action.id).pipe(
          map((res: Distance) =>
            DistanceActions.remove_Distance_Success({
              distance: res,
              successMessage: 'Distanced remove successfully!',
            })
          ),
          catchError((error: HttpErrorResponse) =>
            of(DistanceActions.remove_Distance_Failure({ error }))
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const addDistance = createEffect(
  (actions = inject(Actions), distanceService = inject(DistanceService)) => {
    return actions.pipe(
      ofType(DistanceActions.add_Distance),
      mergeMap((action) =>
        distanceService.addDistance(action.distance).pipe(
          map((res: Distance) =>
            DistanceActions.add_Distance_Success({
              distance: res,
              successMessage: 'Distance added successfully!',
            })
          ),
          catchError((error: HttpErrorResponse) =>
            of(DistanceActions.add_Distance_Failure({ error: error }))
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const editDistance = createEffect(
  (actions = inject(Actions), distanceService = inject(DistanceService)) => {
    return actions.pipe(
      ofType(DistanceActions.edit_Distance),
      mergeMap((action) =>
        distanceService.editDistance(action.distanceId, action.distance).pipe(
          map((res: Distance) =>
            DistanceActions.edit_Distance_Success({
              distance: res,
              successMessage: 'Distance edited successfully!',
            })
          ),
          catchError((error: HttpErrorResponse) =>
            of(DistanceActions.edit_Distance_Failure({ error: error }))
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const exportDistances = createEffect(
  (
    actions = inject(Actions),
    distanceService = inject(DistanceService),
    fileService = inject(FileService)
  ) => {
    return actions.pipe(
      ofType(DistanceActions.export_Distances),
      mergeMap(() =>
        distanceService.exportDistances().pipe(
          map((res: ArrayBuffer) => {
            fileService.downloadFile(res, 'Distances');
            return DistanceActions.export_Distances_Success({
              successMessage: 'Exported Distances Successfully!',
            });
          }),
          catchError((error: HttpErrorResponse) =>
            of(DistanceActions.export_Distances_Failure({ error }))
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);
