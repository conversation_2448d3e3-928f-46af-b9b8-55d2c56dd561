<p-dialog
  [draggable]="false"
  [closable]="true"
  [modal]="true"
  [visible]="isVisible()"
  (visibleChange)="hideDialog()"
  [style]="{ width: '600px' }"
>
  <ng-template pTemplate="header">
    <div class="header">
      {{ billing()?.billingPeriodMonth }}
    </div>
  </ng-template>
  <ng-template pTemplate="content">
    <form [formGroup]="form" class="d-flex gap-16">
      <div class="flex-1 d-flex flex-direction-column gap-8">
        <div class="d-flex flex-direction-column gap-4">
          <label class="fs-14">
            Please highlight the most important changes for this calculation run
          </label>
          <textarea
            rows="5"
            cols="30"
            pInputTextarea
            [autoResize]="true"
            formControlName="comments"
          ></textarea>
          <small
            class="validation-control-error"
            *ngIf="commentsCtrl?.invalid && commentsCtrl?.touched"
          >
            Message is required
          </small>
        </div>
      </div>
    </form>
  </ng-template>
  <ng-template pTemplate="footer">
    <div class="actions">
      <button class="btn-tertiary" type="button" (click)="hideDialog()">
        Cancel
      </button>
      <button class="btn-primary" type="button" (click)="onSubmit()">
        Calculate
      </button>
    </div>
  </ng-template>
</p-dialog>
