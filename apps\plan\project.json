{"name": "plan", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "generators": {"@schematics/angular:component": {"style": "scss", "skipTests": true}}, "prefix": "app", "sourceRoot": "apps/plan/src", "tags": [], "targets": {"build": {"executor": "@angular-devkit/build-angular:browser", "options": {"baseHref": "/plan/", "outputPath": "dist/plan/", "index": "apps/plan/src/index.html", "main": "apps/plan/src/main.ts", "polyfills": ["zone.js"], "tsConfig": "apps/plan/tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "apps/plan/src/assets/", "output": "./assets/"}, {"glob": "**/*", "input": "apps/plan/src/assets", "output": "./assets/"}, {"glob": "favicon.ico", "input": "apps/plan/src", "output": "."}, {"input": "libs/env/src/environments/assets", "glob": "**/*", "output": "/assets/environments"}, {"input": "libs/components/src/components/header/assets", "glob": "**/*", "output": "/assets/components"}], "styles": ["apps/plan/src/styles.scss"], "scripts": []}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "500mb"}, {"type": "anyComponentStyle", "maximumWarning": "2kb", "maximumError": "7kb"}], "outputHashing": "all"}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}, "local": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"executor": "@angular-devkit/build-angular:dev-server", "options": {"host": "stcinsiso.localhost", "port": 4202, "sslKey": "localhost-key.pem", "sslCert": "localhost.pem", "ssl": true}, "configurations": {"production": {"browserTarget": "plan:build:production"}, "development": {"browserTarget": "plan:build:development"}, "local": {"browserTarget": "plan:build:local"}}, "defaultConfiguration": "development"}, "extract-i18n": {"executor": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "plan:build"}}, "test": {"executor": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing", "hammer.js"], "tsConfig": "apps/plan/tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": ["apps/plan/src/favicon.ico", "apps/plan/src/assets"], "styles": ["apps/plan/src/styles.scss"], "scripts": []}}, "lint": {"executor": "@nx/eslint:lint", "options": {"lintFilePatterns": ["apps/plan/**/*.ts", "apps/plan/**/*.html"]}}}}