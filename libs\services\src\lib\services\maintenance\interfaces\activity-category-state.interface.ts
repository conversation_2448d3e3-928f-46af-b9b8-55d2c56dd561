import { MaintenanceState } from './maintenance-state.interface';
import { ActivityCategory } from './activity-category.interface';
import { Constant } from 'libs/services/src/lib/services/shared/constant.interface';

export interface ActivityCategoryState extends MaintenanceState {
  activityCategories: ActivityCategory[];
  activityCategoryTypeList: Constant[];
  activityCategoryType: string;
  isVisibleAddEdit: boolean;
  activityCategory: ActivityCategory | null;
  isVisibleAddCategoryType: boolean;
}
