import { Vessel } from 'libs/services/src/lib/services/vessels/interfaces/vessel.interface';
import { BillingVesselView } from './billing-vessel-view.interface';

export interface BillingVesselStateInterface {
  billingVessels: Vessel[];
  billingVesselId: string;
  billingVesselView: BillingVesselView | null;
  isVisibleBillingPeriod: boolean;
  loading: {
    list: boolean;
    voyageList: boolean;
    export: boolean;
    changeBillingPeriod: boolean;
  };
}
