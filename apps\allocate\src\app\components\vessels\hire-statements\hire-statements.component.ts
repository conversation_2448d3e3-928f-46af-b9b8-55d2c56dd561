import { Component, effect, inject, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { TableComponent } from 'libs/components/src/lib/components/table/table.component';
import {
  AsyncPipe,
  DatePipe,
  NgIf,
  NgClass,
  DecimalPipe,
} from '@angular/common';
import { CellTemplateDirective } from 'libs/components/src/lib/components/table/cell-template.directive';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { Store } from '@ngrx/store';
import { hireStatementFeature } from 'libs/services/src/lib/services/vessels/store/features';
import { SearchQuery } from 'libs/components/src/lib/interfaces/search-query.interface';
import { Column } from 'libs/components/src/lib/components/table/column.model';
import { HireStatement } from 'libs/services/src/lib/services/vessels/interfaces/hire-statement.interface';
import { HireStatementActions } from 'libs/services/src/lib/services/vessels/store/actions/hire-statement.actions';
import { confirmActions } from 'libs/components/src/lib/store/confirm.actions';
import { hireStatementInitialState } from 'libs/services/src/lib/services/vessels/store/state/hire-statement.state';
import { HireStatementBulk } from 'libs/services/src/lib/services/vessels/interfaces/hire-statement-bulk.interface';
import { HireStatementBulkActions } from 'libs/services/src/lib/services/vessels/store/actions/hire-statement-bulk.actions';
import {
  ActivatedRoute,
  Router,
  RouterLink,
  RouterLinkActive,
} from '@angular/router';
import { Subject } from 'rxjs';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { settingsFeature } from 'libs/services/src/lib/services/settings/shared/store/features';
import { HeaderCellTemplateDirective } from 'libs/components/src/lib/components/table/header-cell-template.directive';

@Component({
  selector: 'lha-hire-statements',
  standalone: true,
  imports: [
    TableComponent,
    AsyncPipe,
    CellTemplateDirective,
    MatButtonModule,
    MatIconModule,
    NgIf,
    DatePipe,
    NgClass,
    RouterLink,
    MatCheckboxModule,
    RouterLinkActive,
    DecimalPipe,
    HeaderCellTemplateDirective,
  ],
  templateUrl: './hire-statements.component.html',
  styleUrls: ['./hire-statements.component.scss'],
})
export class HireStatementsComponent implements OnInit, OnDestroy {
  store = inject(Store);
  activatedRoute = inject(ActivatedRoute);
  router = inject(Router);
  hireStatementsState = this.store.selectSignal(
    hireStatementFeature.selectHireStatementsState
  );
  hireStatements = this.store.selectSignal(
    hireStatementFeature.selectHireStatements
  );
  query: SearchQuery = hireStatementInitialState.query;
  hireStatementId = this.activatedRoute.snapshot.params['itemId'];
  unsubscribe: Subject<boolean> = new Subject();
  appSettings = this.store.selectSignal(settingsFeature.selectAppSettings);
  isOffHire = false;

  columns: Column<HireStatement>[] = [
    new Column('vesselId', ''),
    new Column('type', 'Type', { sortHeader: true }),
    new Column('isOnHire', 'On Hire', { sortHeader: true }),
    new Column('deliveryDate', 'Delivery Date', { sortHeader: true }),
    new Column('deliveryPlace', 'Delivery Location', { sortHeader: true }),
    new Column('redeliveryDate', 'Redelivery Date', { sortHeader: true }),
    new Column('dayRate', 'Day Rate', { sortHeader: true }),
    new Column('redeliveryPlace', 'Redelivery Location', { sortHeader: true }),
    new Column('duration', 'Duration (days)', { sortHeader: true }),
    new Column('hireStatementId', 'Actions'),
  ];

  columnsHSBulks: Column<HireStatementBulk>[] = [
    new Column('bulkTypeId', 'Bulk Type', { sortHeader: true }),
    new Column('startQuantity', 'Start Quantity', { sortHeader: true }),
    new Column('endQuantity', 'End Quantity', { sortHeader: true }),
    new Column('price', 'Price', { sortHeader: true }),
    new Column('dateLoaded', 'Date Loaded', { sortHeader: true }),
    new Column('hireStatementBulkId', 'Actions'),
  ];

  constructor() {
    effect(() => {
      const hireStatements = this.hireStatements();
      const params = this.activatedRoute.snapshot.params;

      if (hireStatements.length && !this.hireStatementId) {
        this.router.navigate([hireStatements[0].hireStatementId], {
          relativeTo: this.activatedRoute,
        });
      }
      this.isOffHire = !hireStatements.find(
        (item) => item.hireStatementId === params['itemId']
      )?.isOnHire;
    });
  }

  ngOnInit(): void {
    this.loadHireStatementLists();
  }

  ngOnDestroy(): void {
    this.unsubscribe.next(true);
    this.unsubscribe.complete();
    this.store.dispatch(
      HireStatementActions.load_Hire_Statements_Success({ hireStatements: [] })
    );
  }

  loadHireStatementLists(): void {
    this.store.dispatch(HireStatementActions.load_Hire_Statements_Lists());
  }

  exportHireStatements(): void {
    this.store.dispatch(HireStatementActions.export_Hire_Statements());
  }

  exportHireStatementBulks(): void {
    this.store.dispatch(HireStatementBulkActions.export_Hire_Statement_Bulks());
  }

  onChangeQuery(query: SearchQuery): void {
    this.query = {
      ...this.query,
      ...query,
    };
    this.store.dispatch(
      HireStatementActions.update_Hire_Statement_Queries({ query: this.query })
    );
  }

  addEditHireStatement(hireStatement?: HireStatement): void {
    this.store.dispatch(
      HireStatementActions.open_Hire_Statement_Dialog({ hireStatement })
    );
  }

  removeHireStatement(item: HireStatement): void {
    this.store.dispatch(
      confirmActions.request_Confirmation({
        titles: {
          title:
            'Warning, Deleting this Hire Statement may affect other parts of Allocate. Do you want to remove this Hire Statement?',
          btnConfirm: 'Yes Delete',
        },
        confirm: HireStatementActions.remove_Hire_Statement({
          hireStatement: item,
        }),
      })
    );
  }

  addEditHireStatementBulk(hireStatementBulk?: HireStatementBulk): void {
    this.store.dispatch(
      HireStatementBulkActions.open_Hire_Statement_Bulk_Dialog({
        hireStatementBulk,
      })
    );
  }

  removeHireStatementBulk(item: HireStatementBulk): void {
    this.store.dispatch(
      confirmActions.request_Confirmation({
        titles: {
          title:
            'Warning, Deleting this Hire Statement Bulk may affect other parts of Allocate. Do you want to remove this Hire Statement?',
          btnConfirm: 'Yes Delete',
        },
        confirm: HireStatementBulkActions.remove_Hire_Statement_Bulk({
          id: item.hireStatementBulkId,
        }),
      })
    );
  }
}
