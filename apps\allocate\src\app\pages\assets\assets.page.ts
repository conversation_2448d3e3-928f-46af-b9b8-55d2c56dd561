import {
  ChangeDetectionStrategy,
  ChangeDetector<PERSON>ef,
  Component,
  DestroyRef,
  inject,
  OnInit,
  ViewChild,
} from '@angular/core';
import { FormsModule } from '@angular/forms';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import {
  Ng<PERSON>lass,
  NgFor,
  NgIf,
  NgSwitch,
  NgSwitchCase,
  NgSwitchDefault,
} from '@angular/common';

import { Store } from '@ngrx/store';
import { Actions, ofType } from '@ngrx/effects';

import { InputTextModule } from 'primeng/inputtext';
import { Table, TableModule } from 'primeng/table';

import { AssetActions } from 'libs/services/src/lib/services/maintenance/store/actions/assets.actions';
import { Asset } from 'libs/services/src/lib/services/maintenance/interfaces/asset.interface';
import { CustomChipComponent } from 'libs/components/src/lib/components/custom-chip/custom-chip.component';
import { AssetHistory } from 'libs/services/src/lib/services/maintenance/interfaces/asset-history.interface';

import { InitializeAssetsTable } from '../../shared/tables/assets.table';
import { AssetsTableFields } from '../../shared/enums/assets-table-fields.enum';
import { AssetsAllocateActions } from '../../store/actions/assets.actions';
import { assetsAllocateFeature } from '../../store/features/assets.feature';
import { AssetsMobileWellComponent } from '../../components/assets/asset-mobile-well/asset-mobile-well.component';
import { AssetsHistoryComponent } from '../../components/assets/asset-history/asset-history.component';
import { AssetsAddEditComponent } from '../../components/assets/asset-add-edit/asset-add-edit.component';

@Component({
  standalone: true,
  selector: 'allocate-assets',
  templateUrl: './assets.page.html',
  imports: [
    InputTextModule,
    TableModule,
    NgFor,
    NgSwitch,
    NgSwitchCase,
    NgSwitchDefault,
    NgClass,
    FormsModule,
    NgIf,
    CustomChipComponent,
    AssetsMobileWellComponent,
    AssetsHistoryComponent,
    AssetsAddEditComponent,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AssetsPage implements OnInit {
  @ViewChild('table') table!: Table;

  private readonly store = inject(Store);
  private readonly actions = inject(Actions);
  private readonly destroyRef = inject(DestroyRef);
  private readonly cdr = inject(ChangeDetectorRef);

  listColumns = InitializeAssetsTable();
  assets: Asset[] = [];
  tableFields = AssetsTableFields;
  tableWidth = 1250;
  searchValue = '';
  isVisibleAddEdit = this.store.selectSignal(
    assetsAllocateFeature.selectIsVisibleAddEdit
  );
  isVisibleMobileWell = this.store.selectSignal(
    assetsAllocateFeature.selectIsVisibleMobileWell
  );

  ngOnInit() {
    this.store.dispatch(AssetsAllocateActions.init_assets_allocate());

    this.actions
      .pipe(
        ofType(AssetActions.load_Assets_Success),
        takeUntilDestroyed(this.destroyRef)
      )
      .subscribe(({ assets }) => {
        this.searchValue = '';
        this.assets = [...assets];
        this.table.reset();
        this.cdr.markForCheck();
      });
  }

  createAssets() {
    this.store.dispatch(
      AssetsAllocateActions.change_visibility_add_edit({
        visible: true,
        asset: null,
      })
    );
  }

  editAsset(asset: Asset) {
    this.store.dispatch(
      AssetsAllocateActions.change_visibility_add_edit({ visible: true, asset })
    );
  }

  openAssetHistory(assetsHistory: AssetHistory[]) {
    this.store.dispatch(
      AssetsAllocateActions.change_visibility_history({
        visible: true,
        assetsHistory,
      })
    );
  }

  export() {
    this.store.dispatch(AssetActions.export_Assets());
  }

  openMobileWell(asset: Asset) {
    this.store.dispatch(
      AssetsAllocateActions.change_visibility_mobile_well({
        visible: true,
        asset,
      })
    );
  }
}
