import {
  ChangeDetectionStrategy,
  Component,
  DestroyRef,
  inject,
  OnInit,
} from '@angular/core';
import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { NgIf } from '@angular/common';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

import { Store } from '@ngrx/store';
import { Actions, ofType } from '@ngrx/effects';

import { CalendarModule } from 'primeng/calendar';
import { DialogModule } from 'primeng/dialog';

import { Voyage } from 'libs/services/src/lib/services/voyages/interfaces/voyage.interface';
import { billingsFeature } from '../../../store/features/billing.features';
import { BillingVesselsActions } from '../../../store/actions/billing-vessel.actions';
import { billingVesselsFeature } from '../../../store/features/billing-vessel.features';
import { BillingsActions } from '../../../store/actions/billing.actions';
import { stripTimezoneOffset } from 'libs/services/src/lib/services/functions/convert-date.utils';

@Component({
  selector: 'lha-billing-period',
  standalone: true,
  imports: [NgIf, ReactiveFormsModule, CalendarModule, DialogModule],
  templateUrl: './billing-period.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BillingPeriodComponent implements OnInit {
  private readonly store = inject(Store);
  private readonly actions = inject(Actions);
  private readonly destroyRef = inject(DestroyRef);

  billing = this.store.selectSignal(billingsFeature.selectBilling);
  billingPeriodId = this.store.selectSignal(
    billingsFeature.selectBillingPeriodId
  );
  voyage: Voyage | null = null;
  isVisible = this.store.selectSignal(
    billingVesselsFeature.selectIsVisibleBillingPeriod
  );
  form = new FormGroup({
    billingPeriod: new FormControl<Date | null>(null, [Validators.required]),
  });

  billingPeriodCtrl = this.form.get('billingPeriod');

  ngOnInit(): void {
    this.actions
      .pipe(
        ofType(BillingVesselsActions.change_visibility_billing_period),
        takeUntilDestroyed(this.destroyRef)
      )
      .subscribe(({ visible, voyage }) => {
        if (voyage && visible) {
          this.voyage = voyage;
          this.form.patchValue({
            billingPeriod: this.billing()?.periodStartDate
              ? new Date(this.billing()?.periodStartDate!)
              : null,
          });
        }
      });

    this.actions
      .pipe(
        ofType(BillingsActions.load_Billing_Success),
        takeUntilDestroyed(this.destroyRef)
      )
      .subscribe(({ billing }) => {
        if (billing) {
          this.form.patchValue({
            billingPeriod: billing.periodStartDate,
          });
        }
      });
  }

  onSubmit(): void {
    if (this.form.invalid) {
      this.form.markAllAsTouched();
      return;
    }

    this.store.dispatch(
      BillingVesselsActions.change_Billing_Period({
        billingPeriod: this.billingPeriodCtrl?.value
          ? stripTimezoneOffset(this.billingPeriodCtrl?.value)
          : null,
        voyageId: this.voyage?.voyageId!,
        vesselId: this.voyage?.vesselId!,
        billingPeriodId: this.billingPeriodId(),
      })
    );
  }

  hideDialog() {
    this.form.reset();
    this.form.markAsUntouched();
    this.store.dispatch(
      BillingVesselsActions.change_visibility_billing_period({
        visible: false,
        voyage: null,
      })
    );
  }
}
