import {
  convertToUtcFromUserTimezone,
  convertFromUtcToUserTimezone,
  formatDateInUserTimezone,
  getCurrentDateInUserTimezone,
  isValidTimezone,
  getTimezoneOffset
} from './timezone-conversion.utils';

describe('Timezone Conversion Utils', () => {
  
  describe('convertToUtcFromUserTimezone', () => {
    it('should convert a date from user timezone to UTC', () => {
      const userDate = new Date('2024-01-15T10:00:00'); // 10 AM local time
      const userTimezone = 'America/New_York'; // EST/EDT
      
      const utcDate = convertToUtcFromUserTimezone(userDate, userTimezone);
      
      // In January, New York is EST (UTC-5), so 10 AM EST = 3 PM UTC
      expect(utcDate.getUTCHours()).toBe(15);
    });

    it('should convert an ISO string from user timezone to UTC', () => {
      const isoString = '2024-01-15T10:00:00';
      const userTimezone = 'Europe/London'; // GMT/BST
      
      const utcDate = convertToUtcFromUserTimezone(isoString, userTimezone);
      
      // In January, London is GMT (UTC+0), so 10 AM GMT = 10 AM UTC
      expect(utcDate.getUTCHours()).toBe(10);
    });

    it('should throw error for invalid date', () => {
      expect(() => {
        convertToUtcFromUserTimezone('invalid-date', 'America/New_York');
      }).toThrow();
    });

    it('should throw error for missing timezone', () => {
      expect(() => {
        convertToUtcFromUserTimezone(new Date(), '');
      }).toThrow('User timezone is required for conversion');
    });
  });

  describe('convertFromUtcToUserTimezone', () => {
    it('should convert a UTC date to user timezone', () => {
      const utcDate = new Date('2024-01-15T15:00:00Z'); // 3 PM UTC
      const userTimezone = 'America/New_York'; // EST/EDT
      
      const localDate = convertFromUtcToUserTimezone(utcDate, userTimezone);
      
      // In January, New York is EST (UTC-5), so 3 PM UTC = 10 AM EST
      // Note: The returned date is still a JS Date object, but represents the local time
      expect(localDate.getHours()).toBe(10);
    });

    it('should throw error for invalid UTC date', () => {
      expect(() => {
        convertFromUtcToUserTimezone('invalid-date', 'America/New_York');
      }).toThrow();
    });
  });

  describe('formatDateInUserTimezone', () => {
    it('should format a UTC date in user timezone', () => {
      const utcDate = new Date('2024-01-15T15:00:00Z'); // 3 PM UTC
      const userTimezone = 'America/New_York';
      
      const formatted = formatDateInUserTimezone(utcDate, userTimezone, 'dd/MM/yyyy HH:mm');
      
      expect(formatted).toBe('15/01/2024 10:00'); // 10 AM EST
    });

    it('should return empty string for null date', () => {
      const formatted = formatDateInUserTimezone(null as any, 'America/New_York');
      expect(formatted).toBe('');
    });

    it('should use default format when not specified', () => {
      const utcDate = new Date('2024-01-15T15:00:00Z');
      const userTimezone = 'America/New_York';
      
      const formatted = formatDateInUserTimezone(utcDate, userTimezone);
      
      expect(formatted).toMatch(/\d{2}\/\d{2}\/\d{4} \d{2}:\d{2}/);
    });
  });

  describe('getCurrentDateInUserTimezone', () => {
    it('should return current date in user timezone', () => {
      const userTimezone = 'America/New_York';
      
      const currentDate = getCurrentDateInUserTimezone(userTimezone);
      
      expect(currentDate).toBeInstanceOf(Date);
      expect(currentDate.getTime()).toBeCloseTo(Date.now(), -2); // Within 100ms
    });

    it('should throw error for missing timezone', () => {
      expect(() => {
        getCurrentDateInUserTimezone('');
      }).toThrow('User timezone is required');
    });
  });

  describe('isValidTimezone', () => {
    it('should return true for valid timezones', () => {
      expect(isValidTimezone('America/New_York')).toBe(true);
      expect(isValidTimezone('Europe/London')).toBe(true);
      expect(isValidTimezone('UTC')).toBe(true);
    });

    it('should return false for invalid timezones', () => {
      expect(isValidTimezone('Invalid/Timezone')).toBe(false);
      expect(isValidTimezone('')).toBe(false);
      expect(isValidTimezone('EST')).toBe(false); // Abbreviations are not valid IANA timezone names
    });
  });

  describe('getTimezoneOffset', () => {
    it('should return timezone offset in minutes', () => {
      const timezone = 'America/New_York';
      const winterDate = new Date('2024-01-15T12:00:00Z'); // Winter (EST)
      
      const offset = getTimezoneOffset(timezone, winterDate);
      
      expect(offset).toBe(-300); // EST is UTC-5 (5 * 60 = 300 minutes)
    });

    it('should handle daylight saving time', () => {
      const timezone = 'America/New_York';
      const summerDate = new Date('2024-07-15T12:00:00Z'); // Summer (EDT)
      
      const offset = getTimezoneOffset(timezone, summerDate);
      
      expect(offset).toBe(-240); // EDT is UTC-4 (4 * 60 = 240 minutes)
    });

    it('should throw error for invalid timezone', () => {
      expect(() => {
        getTimezoneOffset('');
      }).toThrow('Timezone is required');
    });
  });
});
