.voyage {
  &__block {
    display: flex;
    align-items: center;
    /* Vertically center */
    justify-content: center;
    /* Horizontally center */
    flex-wrap: wrap;
    width: 100%;
    /* Ensure the block takes the full width of the header */
    height: 100%;
    /* Ensure the block takes the full height of the header */
  }

  &__box {
    align-items: center;
    display: flex;
    margin-right: 20px;
    font-size: 16px;
  }

  &__title {
    margin-right: 5px;
    color: white;
  }

  &__text {
    color: white;
    font-weight: normal;
  }
}

header {
  display: flex;
  align-items: center;
  /* Vertically center */
  justify-content: center;
  /* Horizontally center */
  height: 50px;
  /* Adjust as needed */
}

@media screen and (max-width: 1024px) {
  .voyage__box {
    font-size: 14px;
  }
}

@media screen and (max-width: 820px) {
  .voyage__box {
    font-size:10px;
  }
}

