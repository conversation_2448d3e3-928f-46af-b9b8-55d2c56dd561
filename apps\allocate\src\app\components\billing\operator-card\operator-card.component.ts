import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { RouterLink } from '@angular/router';
import { DecimalPipe, Ng<PERSON>lass, <PERSON><PERSON><PERSON><PERSON> } from '@angular/common';
import { BillingOperator } from '../../../shared/interfaces/billing-operator.interface';

@Component({
  selector: 'lha-operator-card',
  standalone: true,
  imports: [RouterLink, NgStyle, DecimalPipe, NgClass],
  templateUrl: './operator-card.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class OperatorCardComponent {
  @Input() billingOperator!: BillingOperator;
  @Input() currency = '';
}
