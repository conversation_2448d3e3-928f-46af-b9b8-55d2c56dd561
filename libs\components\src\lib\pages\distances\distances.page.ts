import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  DestroyRef,
  inject,
  OnInit,
  ViewChild,
} from '@angular/core';
import {
  NgF<PERSON>,
  NgSwitch,
  Ng<PERSON>witchCase,
  NgSwitchDefault,
  NgIf,
  DatePipe,
} from '@angular/common';
import { FormsModule } from '@angular/forms';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

import { Store } from '@ngrx/store';
import { Actions, ofType } from '@ngrx/effects';

import { InputTextModule } from 'primeng/inputtext';
import { Table, TableModule } from 'primeng/table';
import { ConfirmationService } from 'primeng/api';

import { Distance } from 'libs/services/src/lib/services/maintenance/interfaces/distance.interface';
import { DistanceActions } from 'libs/services/src/lib/services/maintenance/store/actions/distance.actions';
import { DistancesAddEditComponent } from './components/distances-add-edit/distances-add-edit.component';
import { InitializeDistancesTable } from './helper/distances-table';
import { DistancesTableFields } from './helper/distances-table-fields.enum';

@Component({
  standalone: true,
  selector: 'md-distances',
  templateUrl: './distances.page.html',
  imports: [
    FormsModule,
    InputTextModule,
    TableModule,
    NgFor,
    NgSwitch,
    NgSwitchCase,
    NgSwitchDefault,
    NgIf,
    DatePipe,
    DistancesAddEditComponent,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DistancesPage implements OnInit {
  @ViewChild('table') table!: Table;
  private readonly store = inject(Store);
  private readonly actions = inject(Actions);
  private readonly destroyRef = inject(DestroyRef);
  private readonly cdr = inject(ChangeDetectorRef);
  private readonly confirmationService = inject(ConfirmationService);

  searchValue = '';
  tableWidth = 1150;
  listColumns = InitializeDistancesTable();
  distances: Distance[] = [];
  tableFields = DistancesTableFields;

  ngOnInit() {
    this.store.dispatch(DistanceActions.init_distances());

    this.actions
      .pipe(
        ofType(DistanceActions.load_All_Distances_Success),
        takeUntilDestroyed(this.destroyRef)
      )
      .subscribe(({ distances }) => {
        this.searchValue = '';
        this.distances = [...distances];
        this.table.reset();
        this.cdr.markForCheck();
      });
  }

  export() {
    this.store.dispatch(DistanceActions.export_Distances());
  }

  create() {
    this.store.dispatch(
      DistanceActions.change_visibility_add_edit({
        visible: true,
        distance: null,
      })
    );
  }

  edit(distance: Distance) {
    this.store.dispatch(
      DistanceActions.change_visibility_add_edit({
        visible: true,
        distance,
      })
    );
  }

  remove(item: Distance): void {
    this.confirmationService.confirm({
      header: 'Delete',
      message: `
        Deleting this Distance may affect other parts of Master Data.
        <br>
        Do you want to remove this Distance?`,
      acceptLabel: 'Confirm',
      rejectLabel: 'Cancel',
      acceptButtonStyleClass: 'btn-negative-primary',
      rejectButtonStyleClass: 'btn-tertiary',
      acceptIcon: 'none',
      rejectIcon: 'none',
      accept: () => {
        this.store.dispatch(
          DistanceActions.remove_Distance({
            id: item.distanceId,
          })
        );
      },
    });
  }
}
