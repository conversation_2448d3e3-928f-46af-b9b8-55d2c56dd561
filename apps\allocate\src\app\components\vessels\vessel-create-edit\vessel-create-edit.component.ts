import {
  Component,
  Destroy<PERSON><PERSON>,
  inject,
  <PERSON><PERSON><PERSON><PERSON>,
  OnInit,
} from '@angular/core';
import { PageContainerComponent } from '../../../../../../../libs/components/src/lib/components/page-container/page-container.component';
import {
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { Vessel } from '../../../../../../../libs/services/src/lib/services/vessels/interfaces/vessel.interface';
import { VesselActions } from '../../../../../../../libs/services/src/lib/services/vessels/store/actions/vessels.actions';
import { Store } from '@ngrx/store';
import { Subject, takeUntil } from 'rxjs';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { AsyncPipe, NgForOf, NgIf } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatOptionModule } from '@angular/material/core';
import { Actions, ofType } from '@ngrx/effects';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { LoadingDirective } from '../../../../../../../libs/components/src/lib/directives/loading.directive';
import { Location } from '@angular/common';
import { UtilityService } from '../../../../../../../libs/services/src/lib/services/utility.service';
import { CountryService } from '../../../../../../../libs/services/src/lib/services/country.service';
import { ToggleStateComponent } from '../../../../../../../libs/components/src/lib/components/toggle-state/toggle-state.component';
import { maxLength } from '../../../../../../../libs/components/src/lib/validators/maxLength';
import { MatSelectModule } from '@angular/material/select';
import { greaterThan } from '../../../../../../../libs/components/src/lib/validators/greaterThan';
import { UploadImgComponent } from '../../../../../../../libs/components/src/lib/components/upload-img/upload-img.component';
import { maxImageSize } from '../../../../../../../libs/components/src/lib/validators/maxImageSize';
import { OnlyDigitsDirective } from '../../../../../../../libs/components/src/lib/directives/only-digits.directive';
import { decimalPoint } from '../../../../../../../libs/components/src/lib/validators/decimal-point';
import { SingleSelectComponent } from '../../../../../../../libs/components/src/lib/components/single-select/single-select.component';
import { fileTypes } from '../../../../../../../libs/components/src/lib/validators/fileTypes';
import { Environment } from 'env';
import { PortalDirective } from '../../../../../../../libs/components/src/lib/directives/portal.directive';
import { vesselsFeature } from '../../../../../../../libs/services/src/lib/services/vessels/store/features/vessels.features';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { DomSanitizer } from '@angular/platform-browser';

@Component({
  selector: 'lha-vessel-create-edit',
  standalone: true,
  imports: [
    PortalDirective,
    PageContainerComponent,
    FormsModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    NgIf,
    MatButtonModule,
    MatSlideToggleModule,
    RouterLink,
    MatDatepickerModule,
    LoadingDirective,
    AsyncPipe,
    ToggleStateComponent,
    MatOptionModule,
    MatSelectModule,
    NgForOf,
    UploadImgComponent,
    OnlyDigitsDirective,
    SingleSelectComponent,
  ],
  templateUrl: './vessel-create-edit.component.html',
  styleUrls: ['./vessel-create-edit.component.scss'],
})
export class VesselCreateEditComponent implements OnInit, OnDestroy {
  private readonly destroyRef = inject(DestroyRef);
  private sanitizer = inject(DomSanitizer);
  activatedRoute = inject(ActivatedRoute);
  utilityService = inject(UtilityService);
  countryService = inject(CountryService);
  countryList = this.countryService.getCountryList();
  router = inject(Router);
  vesselId = this.utilityService.getParamsFromRoute(
    this.activatedRoute.snapshot.root
  )['id'];
  store = inject(Store);
  action = inject(Actions);
  isAdd = true;
  vessel$ = this.store.select(vesselsFeature.selectVessel);
  vm$ = this.store.select(vesselsFeature.selectVesselsState);
  loading$ = this.store.select(vesselsFeature.selectCreateEditLoader);
  isView = this.activatedRoute.snapshot.queryParamMap.get('isView') === 'true';
  location = inject(Location);
  vessel!: Vessel;
  isVesselActive = true;
  unsubscribe: Subject<boolean> = new Subject();
  maxImgSize = 1024 * 1024 * 2;
  imgControl = new FormControl<string | Event | null>('', [
    maxImageSize(this.maxImgSize),
    fileTypes(['image/png', 'image/jpeg']),
  ]);
  environment = inject(Environment);
  url = this.environment.apiUrl;
  form = new FormGroup({
    name: new FormControl<string>('', [Validators.required]),
    imo: new FormControl<number | null>(null, [Validators.required]),
    country: new FormControl<string>('', []),
    construction: new FormControl<Date | ''>('', []),
    mmsi: new FormControl<number | null>(null, [
      Validators.required,
      greaterThan(0),
      maxLength(9),
    ]),
    vesselOwner: new FormControl<string>('', [Validators.required]),
    dpClass: new FormControl<number | null>(null, []),
    fireFightClass: new FormControl<number | null>(null, []),
    length: new FormControl<number | null>(null, [
      Validators.required,
      greaterThan(0),
      decimalPoint(2),
    ]),
    width: new FormControl<number | null>(null, [
      Validators.required,
      greaterThan(0),
      decimalPoint(2),
    ]),
    draft: new FormControl<number | null>(null, [
      Validators.required,
      greaterThan(0),
      decimalPoint(2),
    ]),
    grossTonnage: new FormControl<number | null>(null, [
      Validators.required,
      greaterThan(0),
      decimalPoint(2),
    ]),
    deadWeight: new FormControl<number | null>(null, [
      Validators.required,
      greaterThan(0),
      decimalPoint(2),
    ]),
    netTonnage: new FormControl<number | null>(null, [
      Validators.required,
      greaterThan(0),
      decimalPoint(2),
    ]),
    deckLengthValue: new FormControl<number | null>(null, [
      Validators.required,
      greaterThan(0),
      decimalPoint(2),
    ]),
    deckCapacity: new FormControl<number | null>(null, [
      Validators.required,
      greaterThan(0),
      decimalPoint(2),
    ]),
    eori: new FormControl<number | null>(null, [greaterThan(0)]),
    roundedSafeHaven: new FormControl<boolean>(false, []),
    errv: new FormControl<boolean>(false, []),
    inactive: new FormControl<boolean>(false, []),
    lengthUnitName: new FormControl<string>('', [Validators.required]),
    deckLengthUnitName: new FormControl<string>('', [Validators.required]),
    widthUnitName: new FormControl<string>('', [Validators.required]),
    draftUnitName: new FormControl<string>('', [Validators.required]),
    deckCapacityUnitName: new FormControl<string>('', [Validators.required]),
    grossTonnageUnitName: new FormControl<string>('', [Validators.required]),
    netTonnageUnitName: new FormControl<string>('', [Validators.required]),
    deadWeightUnitName: new FormControl<string>('', [Validators.required]),
    deckWidthValue: new FormControl<number | null>(null, [
      Validators.required,
      greaterThan(0),
      decimalPoint(2),
    ]),
    deckWidthUnitName: new FormControl<string>('', [Validators.required]),
    picture: new FormControl<string | null>(null, []),
  });

  dpClassList = [
    {
      name: '0',
      value: 0,
    },
    {
      name: '1',
      value: 1,
    },
    {
      name: '2',
      value: 2,
    },
    {
      name: '3',
      value: 3,
    },
  ];

  firefightClassList = [
    {
      name: '0',
      value: 0,
    },
    {
      name: '1',
      value: 1,
    },
    {
      name: '2',
      value: 2,
    },
  ];

  ngOnInit(): void {
    this.subToVessel();
    this.setModeSettings();
    this.subToViewMode();
    this.subToAction();

    this.action
      .pipe(ofType(VesselActions.load_Vessel_Picture_Success))
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(({ vesselPicture }) => {
        const picture = this.sanitizer.bypassSecurityTrustUrl(
          URL.createObjectURL(vesselPicture)
        );
        this.imgControl.patchValue(picture as string, { emitEvent: false });
      });
  }

  ngOnDestroy(): void {
    this.unsubscribe.next(true);
    this.unsubscribe.complete();
  }

  private subToVessel(): void {
    this.vessel$.pipe(takeUntil(this.unsubscribe)).subscribe((res) => {
      if (!this.vesselId) {
        return;
      }
      this.isAdd = false;
      this.vessel = res;
      this.patchForm(res);
    });
  }

  private subToViewMode(): void {
    this.activatedRoute.queryParams
      .pipe(takeUntil(this.unsubscribe))
      .subscribe((res) => {
        if (this.vesselId && typeof res['isView'] === 'undefined') {
          this.changeMode(true);
          return;
        }
      });
  }

  private subToAction(): void {
    this.action
      .pipe(
        ofType(VesselActions.edit_Vessel_Success),
        takeUntil(this.unsubscribe)
      )
      .subscribe(() => {
        this.changeMode(true);
      });
  }

  changeMode(isView: boolean): void {
    this.isView = isView;
    /**
     * That was implemented in this way in order to routerNavigationAction is not updated
     */
    const url = this.router
      .createUrlTree([], {
        relativeTo: this.activatedRoute,
        queryParams: { isView },
      })
      .toString();
    this.location.replaceState(url);
    this.setModeSettings();
  }

  toggleActiveState(inactive: boolean): void {
    this.form.patchValue({
      inactive: !inactive,
    });
  }

  setModeSettings(): void {
    if (this.isView) {
      this.form.disable();
      this.form.markAsUntouched();
    } else {
      this.form.enable();
    }
  }

  cancelEdit(): void {
    if (this.form.touched || this.imgControl.touched) {
      this.patchForm(this.vessel);
    }
    this.isVesselActive = !this.vessel.inactive;
    this.changeMode(true);
  }

  private patchForm(vessel: Vessel): void {
    this.form.patchValue(vessel);
    this.isVesselActive = !vessel.inactive;
    if (vessel.vesselPictureId) {
      this.store.dispatch(
        VesselActions.load_Vessel_Picture({
          vesselPictureId: vessel.vesselPictureId,
        })
      );
    }
  }

  saveVessel(): void {
    if (this.form.invalid || this.imgControl.invalid) {
      this.form.markAllAsTouched();
      this.imgControl.markAsTouched();
      return;
    }

    const model = {
      ...this.form.value,
      hasImage: !!this.imgControl.value,
    } as Vessel;

    if (this.isAdd) {
      this.store.dispatch(
        VesselActions.add_Vessel({ vessel: model, img: this.imgControl.value })
      );
    } else {
      this.store.dispatch(
        VesselActions.edit_Vessel({
          vessel: model,
          vesselId: this.vessel.vesselId,
          img: this.imgControl.value,
        })
      );
    }
  }

  dateFutureFilter(d: Date | null): boolean {
    const dateNow = new Date(new Date().toDateString()).getTime();
    const calendarDate = new Date(new Date(d || '').toDateString()).getTime();
    return dateNow >= calendarDate;
  }
}
