import { createActionGroup, emptyProps, props } from '@ngrx/store';
import { BillingOperator } from '../../shared/interfaces/billing-operator.interface';
import { errorProps } from 'libs/components/src/lib/functions/utility.functions';
import { BillingOperatorView } from '../../shared/interfaces/billing-operator-view.interface';

export const BillingOperatorsActions = createActionGroup({
  source: 'BillingOperators',
  events: {
    load_Billing_Operators: props<{ billingPeriodId: string }>(),
    load_Billing_Operators_Success: props<{
      billingOperators: BillingOperator[];
    }>(),
    load_Billing_Operators_Failure: errorProps(),

    load_Billing_Operator_Voyages: props<{
      billingPeriodId: string;
      clientId: string;
    }>(),

    load_Billing_Operator_Voyages_Success: props<{
      billingOperatorVoyages: BillingOperatorView;
    }>(),
    load_Billing_Operator_Voyages_Failure: errorProps(),

    export_Billing_Operators: props<{
      billingPeriodId: string;
      clientId: string;
    }>(),
    export_Billing_Operators_Success: emptyProps(),
    export_Billing_Operators_Failure: errorProps(),
  },
});
