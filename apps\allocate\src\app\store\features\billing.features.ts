import { createFeature, createReducer, createSelector } from '@ngrx/store';
import { immerOn } from 'ngrx-immer/store';
import { billingState } from '../states/billing.state';
import { BillingsActions } from '../actions/billing.actions';

export const billingReducer = createReducer(
  billingState,
  immerOn(BillingsActions.init_Billing_Detail, (state, payload) => {
    state.billingPeriodId = payload.billingPeriodId;
  }),
  immerOn(BillingsActions.load_Billings, (state) => {
    state.loading.list = true;
  }),
  immerOn(BillingsActions.update_Billing_Queries, (state, { query }) => {
    state.query = query;
    state.loading.list = true;
  }),
  immerOn(BillingsActions.load_Billing_Success, (state, { billing }) => {
    state.billing = billing;
  }),
  immerOn(
    BillingsActions.lock_Billing_Period_Success,
    BillingsActions.unlock_Billing_Period_Success,
    (state, { billing }) => {
      state.billing = billing;
    }
  ),
  immerOn(BillingsActions.load_Billings_Success, (state, { billings }) => {
    state.billings = billings;
    state.loading.list = false;
  }),
  immerOn(
    BillingsActions.load_Billing_History_Success,
    (state, { billingHistory }) => {
      state.billingHistory = billingHistory;
      state.loading.loadHistory = true;
    }
  ),
  immerOn(BillingsActions.calc_Billing_Period, (state) => {
    state.loading.calculating = true;
  }),
  immerOn(
    BillingsActions.calc_Billing_Period_Success,
    BillingsActions.calc_Billing_Period_Failure,
    (state) => {
      state.loading.calculating = false;
      state.isVisibleCalcBillingPeriod = false;
    }
  ),
  immerOn(
    BillingsActions.load_Billing,
    BillingsActions.lock_Billing_Period,
    BillingsActions.unlock_Billing_Period,
    (state) => {
      state.loading.list = true;
    }
  ),
  immerOn(BillingsActions.export_Billing, (state) => {
    state.loading.export = true;
  }),
  immerOn(
    BillingsActions.export_Billing_Success,
    BillingsActions.export_Billing_Failure,
    (state) => {
      state.loading.export = false;
    }
  ),
  immerOn(BillingsActions.compare_Billing_History, (state) => {
    state.loading.comparing = true;
  }),
  immerOn(
    BillingsActions.compare_Billing_History_Success,
    BillingsActions.compare_Billing_History_Failure,
    (state) => {
      state.loading.comparing = false;
    }
  ),
  immerOn(
    BillingsActions.load_Billing_History_Success,
    BillingsActions.load_Billing_History_Failure,
    (state) => {
      state.loading.loadHistory = false;
    }
  ),
  immerOn(BillingsActions.download_Billing_History, (state) => {
    state.loading.download = true;
  }),
  immerOn(
    BillingsActions.download_Billing_History_Success,
    BillingsActions.download_Billing_History_Failure,
    (state) => {
      state.loading.download = false;
    }
  ),
  immerOn(
    BillingsActions.load_Billings_Failure,
    BillingsActions.load_Billing_Success,
    BillingsActions.load_Billing_Failure,
    BillingsActions.lock_Billing_Period_Success,
    BillingsActions.lock_Billing_Period_Failure,
    BillingsActions.unlock_Billing_Period_Success,
    BillingsActions.unlock_Billing_Period_Failure,
    (state) => {
      state.loading.list = false;
    }
  ),
  immerOn(
    BillingsActions.change_visibility_calc_billing_period,
    (state, payload) => {
      state.isVisibleCalcBillingPeriod = payload.visible;
    }
  ),
  immerOn(
    BillingsActions.change_visibility_billing_history_period,
    (state, payload) => {
      state.isVisibleBillingHistory = payload.visible;
    }
  ),
  
);

export const billingsFeature = createFeature({
  name: 'billings',
  reducer: billingReducer,
  extraSelectors: ({ selectLoading }) => ({
    selectListLoader: createSelector(
      selectLoading,
      (selectLoading) => selectLoading.list
    ),
    selectCreateEditLoader: createSelector(
      selectLoading,
      (selectLoading) => selectLoading.createEdit
    ),
  }),
});
