import {
  ChangeDetectorRef,
  Component,
  DestroyRef,
  inject,
  OnInit,
  ViewChild,
} from '@angular/core';

import { BillingHistory } from '../../../shared/interfaces/billing-history.interface';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  Ng<PERSON>or,
  NgI<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Ng<PERSON><PERSON>,
  Ng<PERSON>witchCase,
  NgSwitchDefault,
} from '@angular/common';
import { billingsFeature } from '../../../store/features/billing.features';
import { Store } from '@ngrx/store';
import { BillingsActions } from '../../../store/actions/billing.actions';
import { Table, TableModule } from 'primeng/table';
import { BillingHistoryTableFields } from '../../../shared/enums/billing-history-table-fields.enum';
import { Actions, ofType } from '@ngrx/effects';
import { DialogModule } from 'primeng/dialog';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormsModule } from '@angular/forms';
import { InputTextModule } from 'primeng/inputtext';
import { InitializeBillingHistoryTable } from '../../../shared/tables/billing-history.table';

@Component({
  selector: 'lha-billing-history',
  standalone: true,
  imports: [
    DatePipe,
    NgIf,
    NgStyle,
    TableModule,
    NgFor,
    DialogModule,
    NgSwitch,
    NgSwitchCase,
    NgSwitchDefault,
    FormsModule,
    InputTextModule,
  ],
  templateUrl: './billing-history.component.html',
})
export class BillingHistoryComponent implements OnInit {
  @ViewChild('table') table!: Table;

  private readonly store = inject(Store);
  private readonly actions = inject(Actions);
  private readonly destroyRef = inject(DestroyRef);
  private readonly cdr = inject(ChangeDetectorRef);

  listColumns = InitializeBillingHistoryTable();
  tableFields = BillingHistoryTableFields;
  searchValue = '';
  loading = this.store.selectSignal(billingsFeature.selectLoading);
  isVisible = this.store.selectSignal(
    billingsFeature.selectIsVisibleBillingHistory
  );
  tableWidth = 530;
  billingHistory: BillingHistory[] = [];
  selectedItems: BillingHistory[] = [];

  ngOnInit(): void {
    this.actions
      .pipe(
        ofType(BillingsActions.change_visibility_billing_history_period),
        takeUntilDestroyed(this.destroyRef)
      )
      .subscribe(({ billing, visible }) => {
        this.selectedItems = [];
        this.billingHistory = [];
        if (billing && visible) {
          this.store.dispatch(
            BillingsActions.load_Billing_History({
              id: billing.billingPeriodId,
            })
          );
        }
      });

    this.actions
      .pipe(
        ofType(BillingsActions.load_Billing_History_Success),
        takeUntilDestroyed(this.destroyRef)
      )
      .subscribe(({ billingHistory }) => {
        this.searchValue = '';
        this.billingHistory = [...billingHistory];
        this.table.reset();
        this.cdr.markForCheck();
      });
  }

  compareHistory(): void {
    this.store.dispatch(
      BillingsActions.compare_Billing_History({
        billingHistory: this.selectedItems,
      })
    );
  }

  downloadHistory(): void {
    if (!this.selectedItems[0]?.documentId) {
      return;
    }
    this.store.dispatch(
      BillingsActions.download_Billing_History({
        billingHistory: this.selectedItems[0],
      })
    );
  }

  hideDialog() {
    this.store.dispatch(
      BillingsActions.change_visibility_billing_history_period({
        visible: false,
        billing: null,
      })
    );
  }
}
