<div class="d-flex justify-content-between mb-40">
  <b class="fs-24">Settings</b>

  <button class="btn-primary" type="button" (click)="saveSettings()">
    Save
  </button>
</div>

<form [formGroup]="form" class="d-flex flex-wrap gap-16">
  <div class="d-flex w-100 justify-content-between gap-8">
    <div class="d-flex w-50 flex-direction-column gap-4">
      <label class="fs-14">Currency</label>
      <p-dropdown
        [options]="currencyList"
        [filter]="true"
        formControlName="currency"
        optionLabel="currency"
        optionValue="currency"
        [showClear]="true"
        appendTo="body"
        panelStyleClass="new-version-panel"
        styleClass="new-version"
      />
      <small
        class="validation-control-error"
        *ngIf="controls.currency?.invalid && controls.currency?.touched"
      >
        Currency is required.
      </small>
    </div>
    <div class="d-flex w-50 flex-direction-column gap-4">
      <label class="fs-14">Initial Port</label>
      <p-dropdown
        [options]="ports()"
        [filter]="true"
        formControlName="settingsDefaultInitialPortId"
        optionLabel="name"
        optionValue="assetId"
        [showClear]="true"
        appendTo="body"
        panelStyleClass="new-version-panel"
        styleClass="new-version"
      />
      <small
        class="validation-control-error"
        *ngIf="
          controls.settingsDefaultInitialPortId?.invalid &&
          controls.settingsDefaultInitialPortId?.touched
        "
      >
        Initial port is required.
      </small>
    </div>
  </div>
  <div class="d-flex w-100 justify-content-between gap-8">
    <div class="d-flex w-50 align-items-center gap-4">
      <p-inputSwitch formControlName="allowPassToPass" />
      <label class="fs-14">Allow Passage To Passage</label>
    </div>
    <div class="d-flex w-50 align-items-center gap-4">
      <p-inputSwitch formControlName="allowPassToInt" />
      <label class="fs-14">Allow Passage To Interfield</label>
    </div>
  </div>
</form>
