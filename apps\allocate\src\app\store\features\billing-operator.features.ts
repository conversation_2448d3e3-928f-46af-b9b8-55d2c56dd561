import { createFeature, createReducer, createSelector } from '@ngrx/store';
import { immerOn } from 'ngrx-immer/store';
import { billingOperatorState } from '../states/billing-operator.state';
import { BillingOperatorsActions } from '../actions/billing-operator.actions';
import { routerNavigationAction } from '@ngrx/router-store';

export const billingOperatorReducer = createReducer(
  billingOperatorState,
  immerOn(
    BillingOperatorsActions.load_Billing_Operator_Voyages,
    (state, { clientId }) => {
      state.billingOperatorId = clientId;
    }
  ),
  immerOn(
    BillingOperatorsActions.load_Billing_Operators_Success,
    (state, { billingOperators }) => {
      state.billingOperators = billingOperators;
    }
  ),
  immerOn(
    BillingOperatorsActions.load_Billing_Operator_Voyages_Success,
    (state, { billingOperatorVoyages }) => {
      state.billingOperatorVoyages = billingOperatorVoyages;
    }
  ),
  immerOn(
    BillingOperatorsActions.load_Billing_Operators,
    routerNavigationAction,
    (state) => {
      state.loading.list = true;
      state.loading.voyageList = true;
    }
  ),
  immerOn(BillingOperatorsActions.export_Billing_Operators, (state) => {
    state.loading.export = true;
  }),
  immerOn(
    BillingOperatorsActions.export_Billing_Operators_Success,
    BillingOperatorsActions.export_Billing_Operators_Failure,
    (state) => {
      state.loading.export = false;
    }
  ),
  immerOn(
    BillingOperatorsActions.load_Billing_Operators_Failure,
    BillingOperatorsActions.load_Billing_Operators_Success,
    (state) => {
      state.loading.list = false;
    }
  ),
  immerOn(
    BillingOperatorsActions.load_Billing_Operator_Voyages_Success,
    BillingOperatorsActions.load_Billing_Operator_Voyages_Success,
    (state) => {
      state.loading.voyageList = false;
    }
  ),
);

export const billingOperatorsFeature = createFeature({
  name: 'billingOperators',
  reducer: billingOperatorReducer,
  extraSelectors: ({
    selectLoading,
    selectBillingOperators,
    selectBillingOperatorId,
  }) => ({
    selectListLoader: createSelector(
      selectLoading,
      (selectLoading) => selectLoading.list
    ),
    selectBillingOperator: createSelector(
      selectBillingOperators,
      selectBillingOperatorId,
      (selectBillingOperators, selectBillingOperatorId) =>
        selectBillingOperators.find(
          (item) => item.clientId === selectBillingOperatorId
        )
    ),
  }),
});
