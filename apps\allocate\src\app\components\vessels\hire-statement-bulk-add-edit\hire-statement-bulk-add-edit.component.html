<div
  *ngIf="vm$ | async as vm"
  cdkDrag
  cdkDragRootElement=".cdk-overlay-pane"
  cdkDragHandle
  cdkDragBoundary=".cdk-overlay-container"
>
  <div mat-dialog-title>
    <h4>{{ isAdd ? 'Add ' : 'Edit ' }} Hire Statement Bulk</h4>
  </div>
  <mat-dialog-content *lhaLoading="vm.loading.createEdit">
    <form [formGroup]="form" (ngSubmit)="saveHireStatementBulk()">
      <div class="form__block">
        <div class="form__box">
          <lha-single-select
            [options]="vm.bulkTypes"
            formControlName="bulkTypeId"
            bindValue="bulkTypeId"
            placeholder="Bulk Type"
          />
          <div
            *ngIf="
              form.controls.bulkTypeId.invalid &&
              form.controls.bulkTypeId.touched
            "
          >
            <mat-error *ngIf="form.controls.bulkTypeId.hasError('required')">
              Bulk Type is required.
            </mat-error>
          </div>
        </div>
        <div class="form__box">
          <mat-form-field appearance="outline" hideRequiredMarker="true">
            <mat-label>Start Quantity</mat-label>
            <input
              type="number"
              matInput
              formControlName="startQuantity"
              [decimal]="true"
            />
            <mat-error *ngIf="form.controls.startQuantity.hasError('required')">
              Start Quantity is required.
            </mat-error>
            <mat-error
              *ngIf="form.controls.startQuantity.hasError('greaterThan')"
              >Start Quantity should be more than 0</mat-error
            >
            <mat-error
              *ngIf="form.controls.startQuantity.hasError('decimalPoint')"
              >Decimal point should be less than 4</mat-error
            >
          </mat-form-field>
        </div>
        <div class="form__box">
          <mat-form-field appearance="outline" hideRequiredMarker="true">
            <mat-label>Price</mat-label>
            <input
              type="number"
              matInput
              formControlName="price"
              [decimal]="true"
            />
            <mat-error *ngIf="form.controls.price.hasError('required')">
              Price is required.
            </mat-error>
            <mat-error *ngIf="form.controls.price.hasError('min')"
              >Price should be 0 or more</mat-error
            >
            <mat-error *ngIf="form.controls.price.hasError('decimalPoint')"
              >Decimal point should be less than 3</mat-error
            >
          </mat-form-field>
        </div>
        <div class="form__box">
          <mat-form-field appearance="outline" hideRequiredMarker="true">
            <mat-label>Date Loaded</mat-label>
            <input
              matInput
              [max]="timeAfter"
              [ngxMatDatetimePicker]="dateLoaded"
              formControlName="dateLoaded"
            />
            <mat-datepicker-toggle
              matIconSuffix
              [for]="$any(dateLoaded)"
            ></mat-datepicker-toggle>
            <ngx-mat-datetime-picker #dateLoaded></ngx-mat-datetime-picker>
            <mat-error *ngIf="form.controls.dateLoaded.hasError('required')"
              >Date Loaded is required</mat-error
            >
          </mat-form-field>
        </div>
      </div>
      <mat-dialog-actions align="end">
        <button mat-raised-button color="primary" type="submit">
          {{ isAdd ? 'Add ' : 'Save ' }}
        </button>
        <button
          mat-raised-button
          color="warn"
          type="button"
          mat-dialog-close=""
        >
          Cancel
        </button>
      </mat-dialog-actions>
    </form>
  </mat-dialog-content>
</div>
