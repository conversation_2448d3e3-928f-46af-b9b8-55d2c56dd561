<p-dialog
  [draggable]="false"
  [closable]="true"
  [modal]="true"
  [visible]="isVisible()"
  (visibleChange)="hideDialog()"
  [style]="{ width: '600px' }"
>
  <ng-template pTemplate="header">
    <div class="header">Change Billing Period</div>
  </ng-template>
  <ng-template pTemplate="content">
    <form [formGroup]="form">
      <div class="d-flex w-100 justify-content-between gap-8">
        <div class="d-flex w-50 flex-direction-column gap-4">
          <label class="fs-14"> Voyage Number </label>
          <span>{{ voyage?.voyageNumber }}</span>
        </div>

        <div class="d-flex w-50 flex-direction-column gap-4">
          <label class="fs-14"> New Billing Period </label>
          <p-calendar
            view="month"
            dateFormat="mm/yy"
            appendTo="body"
            formControlName="billingPeriod"
            (onClear)="billingPeriodCtrl?.setValue(null)"
          ></p-calendar>
          <small
            class="validation-control-error"
            *ngIf="billingPeriodCtrl?.invalid && billingPeriodCtrl?.touched"
          >
            New Billing Period is required
          </small>
        </div>
      </div>
    </form>
  </ng-template>
  <ng-template pTemplate="footer">
    <div class="actions">
      <button class="btn-tertiary" type="button" (click)="hideDialog()">
        Cancel
      </button>
      <button class="btn-primary" type="button" (click)="onSubmit()">
        Save
      </button>
    </div>
  </ng-template>
</p-dialog>
