import { createActionGroup, emptyProps, props } from '@ngrx/store';
import { errorProps } from 'libs/components/src/lib/functions/utility.functions';
import { Vessel } from 'libs/services/src/lib/services/vessels/interfaces/vessel.interface';
import { BillingVesselView } from '../../shared/interfaces/billing-vessel-view.interface';
import { Voyage } from 'libs/services/src/lib/services/voyages/interfaces/voyage.interface';

export const BillingVesselsActions = createActionGroup({
  source: 'BillingVessels',
  events: {
    load_Billing_Vessels: props<{ billingPeriodId: string }>(),
    load_Billing_Vessels_Success: props<{ billingVessels: Vessel[] }>(),
    load_Billing_Vessels_Failure: errorProps(),

    change_visibility_billing_period: props<{
      visible: boolean;
      voyage: Voyage | null;
    }>(),

    load_Billing_Vessel_Voyages: props<{
      billingPeriodId: string;
      vesselId: string;
    }>(),
    load_Billing_Vessel_Voyages_Success: props<{
      billingVesselVoyages: BillingVesselView | null;
    }>(),
    load_Billing_Vessel_Voyages_Failure: errorProps(),

    export_Billing_Vessel: props<{
      billingPeriodId: string;
      vesselId: string;
    }>(),
    export_Billing_Vessel_Success: emptyProps(),
    export_Billing_Vessel_Failure: errorProps(),

    change_Billing_Period: props<{
      billingPeriod: Date | null;
      voyageId: string;
      billingPeriodId: string;
      vesselId: string;
    }>(),
    change_Billing_Period_Success: props<{
      successMessage: string;
      billingPeriodId: string;
      vesselId: string;
    }>(),
    change_Billing_Period_Failure: errorProps(),
  },
});
