import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Ng<PERSON><PERSON>,
  Ng<PERSON><PERSON>,
  Ng<PERSON><PERSON><PERSON><PERSON>,
  NgSwitchDefault,
} from '@angular/common';
import { FormsModule } from '@angular/forms';

import { Store } from '@ngrx/store';

import { DialogModule } from 'primeng/dialog';
import { TableModule } from 'primeng/table';
import { InputTextModule } from 'primeng/inputtext';

import { CustomChipComponent } from 'libs/components/src/lib/components/custom-chip/custom-chip.component';
import { OperatorsNameHistoryTableFields } from '../../helper/operators-name-history-table.enum';
import { InitializeOperatorsNameHistoryTable } from '../../helper/operators-name-history-table';
import { operatorsFeature } from 'libs/services/src/lib/services/maintenance/store/features/operators.features';

@Component({
  standalone: true,
  selector: 'md-clients-history-name-table',
  templateUrl: './operators-name-history-table.html',
  imports: [
    DialogModule,
    TableModule,
    InputTextModule,
    NgIf,
    NgFor,
    NgSwitch,
    NgSwitchCase,
    NgSwitchDefault,
    FormsModule,
    CustomChipComponent,
    DatePipe,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ClientsNameHistoryTableComponent {
  private readonly store = inject(Store);
  operator = this.store.selectSignal(operatorsFeature.selectOperator);
  listAssetClientsColumns = InitializeOperatorsNameHistoryTable();
  tableFields = OperatorsNameHistoryTableFields;
  tableWidth = 350;
  searchValue = '';
}
