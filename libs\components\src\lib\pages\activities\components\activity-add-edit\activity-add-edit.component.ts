import { DatePipe, NgIf } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  DestroyRef,
  inject,
  OnInit,
} from '@angular/core';
import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

import { Actions, ofType } from '@ngrx/effects';
import { Store } from '@ngrx/store';

import { DialogModule } from 'primeng/dialog';
import { DropdownModule } from 'primeng/dropdown';
import { InputTextModule } from 'primeng/inputtext';
import { MultiSelectModule } from 'primeng/multiselect';
import { TooltipModule } from 'primeng/tooltip';

import { Activity } from 'libs/services/src/lib/services/maintenance/interfaces/activity.interface';
import { ActivityConfigActions } from 'libs/services/src/lib/services/maintenance/store/actions/activity-config.actions';
import { activityConfigFeature } from 'libs/services/src/lib/services/maintenance/store/features';

@Component({
  selector: 'md-activity-add-edit',
  standalone: true,
  templateUrl: './activity-add-edit.component.html',
  imports: [
    ReactiveFormsModule,
    NgIf,
    DialogModule,
    DropdownModule,
    MultiSelectModule,
    InputTextModule,
    DatePipe,
    TooltipModule,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ActivityAddEditComponent implements OnInit {
  private readonly destroyRef = inject(DestroyRef);
  private readonly actions = inject(Actions);
  private readonly store = inject(Store);
  isVisible = this.store.selectSignal(
    activityConfigFeature.selectIsVisibleAddEdit
  );
  activity = this.store.selectSignal(activityConfigFeature.selectActivity);
  activityTypeList = this.store.selectSignal(
    activityConfigFeature.selectActivityTypeList
  );
  chargeabilityList = this.store.selectSignal(
    activityConfigFeature.selectChargeabilityMapList
  );
  activities = this.store.selectSignal(
    activityConfigFeature.selectFilteredActivities
  );
  activityDep = ['INT', 'PASS', 'NCHPASS'];
  activityChar = ['HCL', 'HCD', 'PASS', 'INT'];

  form = new FormGroup({
    name: new FormControl<string>('', [Validators.required]),
    chargeability: new FormControl<string>('', [Validators.required]),
    code: new FormControl<string>('', [Validators.required]),
    type: new FormControl<string>('', [Validators.required]),
    parallelActivityIds: new FormControl<string[]>([]),
  });

  controls = {
    name: this.form.get('name'),
    chargeability: this.form.get('chargeability'),
    code: this.form.get('code'),
    type: this.form.get('type'),
    parallelActivityIds: this.form.get('parallelActivityIds'),
  };

  ngOnInit() {
    this.actions
      .pipe(ofType(ActivityConfigActions.change_visibility_add_edit))
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(({ activity }) => {
        this.form.reset();
        this.form.markAsUntouched();
        this.controls.parallelActivityIds?.enable();
        if (activity != null) {
          this.form.patchValue({
            ...activity,
          });
        }
      });

    this.controls.type?.valueChanges
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((value) => {
        if (this.activityDep.includes(value ?? '')) {
          this.controls.parallelActivityIds?.disable();
          this.controls.parallelActivityIds?.patchValue([]);
        } else {
          this.controls.parallelActivityIds?.enable();
        }

        if (
          this.activityChar.includes(value ?? '') &&
          this.controls.chargeability?.value === 'NCH'
        ) {
          this.controls.chargeability?.patchValue(null);
        }
      });
  }

  hideDialog() {
    this.store.dispatch(
      ActivityConfigActions.change_visibility_add_edit({
        visible: false,
        activity: null,
      })
    );
  }

  onSubmit() {
    this.form.markAllAsTouched();

    if (this.form.valid) {
      const activity = {
        ...this.form.value,
      } as Activity;
      if (this.activity()) {
        this.store.dispatch(
          ActivityConfigActions.edit_Activity_Config({
            activity,
            activityId: this.activity()!.activityId,
          })
        );
      } else {
        this.store.dispatch(
          ActivityConfigActions.add_Activity_Config({
            activity,
          })
        );
      }
    }
  }
}
