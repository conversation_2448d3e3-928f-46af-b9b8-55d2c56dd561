import { createActionGroup, emptyProps, props } from '@ngrx/store';
import { AssetHistory } from 'libs/services/src/lib/services/maintenance/interfaces/asset-history.interface';
import { Asset } from 'libs/services/src/lib/services/maintenance/interfaces/asset.interface';

export const AssetsAllocateActions = createActionGroup({
  source: '[AssetsAllocate]',
  events: {
    init_assets_allocate: emptyProps(),
    change_visibility_add_edit: props<{
      visible: boolean;
      asset: Asset | null;
    }>(),
    change_visibility_history: props<{
      visible: boolean;
      assetsHistory: AssetHistory[];
    }>(),
    change_visibility_mobile_well: props<{
      visible: boolean;
      asset: Asset | null;
    }>(),
  },
});
