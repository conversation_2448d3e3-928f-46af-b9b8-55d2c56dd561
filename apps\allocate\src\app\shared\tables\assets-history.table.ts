import { ColumnModel } from 'libs/components/src/lib/models/column.model';
import { AssetsHistoryTableFields } from '../enums/assets-history-table-fields.enum';

export function InitializeAssetsHistoryTable(): ColumnModel[] {
  const assetsHistoryListColumns = [
    new ColumnModel(AssetsHistoryTableFields.clientName, 'Operator', 150, {
      sortable: true,
    }),
    new ColumnModel(
      AssetsHistoryTableFields.startDateTime,
      'Start Date ',
      150,
      {
        sortable: true,
      }
    ),
    new ColumnModel(AssetsHistoryTableFields.endDateTime, 'End Date', 150, {
      sortable: true,
    }),
  ];
  return assetsHistoryListColumns;
}
