import {
  ChangeDetectionStrategy,
  Component,
  DestroyRef,
  inject,
  OnInit,
} from '@angular/core';
import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { NgIf } from '@angular/common';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

import { Store } from '@ngrx/store';
import { Actions, ofType } from '@ngrx/effects';

import { DropdownModule } from 'primeng/dropdown';
import { InputSwitchModule } from 'primeng/inputswitch';

import { settingsFeature } from 'libs/services/src/lib/services/settings/shared/store/features';
import { SettingActions } from 'libs/services/src/lib/services/settings/shared/store/actions/setting.actions';
import { AppSettings } from 'libs/services/src/lib/services/settings/shared/interfaces/app-settings.interface';

@Component({
  selector: 'lha-settings',
  standalone: true,
  imports: [ReactiveFormsModule, NgIf, DropdownModule, InputSwitchModule],
  templateUrl: './settings.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SettingsComponent implements OnInit {
  private readonly store = inject(Store);
  private readonly actions = inject(Actions);
  private readonly destroyRef = inject(DestroyRef);

  ports = this.store.selectSignal(settingsFeature.selectPorts);
  form = new FormGroup({
    settingId: new FormControl(''),
    currency: new FormControl<string>('', [Validators.required]),
    settingsDefaultInitialPortId: new FormControl<string>('', [
      Validators.required,
    ]),
    allowPassToPass: new FormControl<boolean>(false),
    allowPassToInt: new FormControl<boolean>(false),
  });

  controls = {
    currency: this.form.get('currency'),
    settingsDefaultInitialPortId: this.form.get('settingsDefaultInitialPortId'),
  };

  currencyList = [
    {
      currency: '£',
    },
    {
      currency: '€',
    },
    {
      currency: '¥',
    },
  ];

  ngOnInit(): void {
    this.store.dispatch(SettingActions.initialise_Settings_Page());

    this.actions
      .pipe(
        ofType(SettingActions.load_Settings_Success),
        takeUntilDestroyed(this.destroyRef)
      )
      .subscribe(({ settings }) => {
        this.form.patchValue({ ...settings });
      });
  }

  saveSettings(): void {
    this.form.markAllAsTouched();

    if (this.form.valid) {
      const settings = {
        ...this.form.value,
      } as AppSettings;

      if (settings.settingId) {
        this.store.dispatch(SettingActions.edit_Settings({ settings }));
      } else {
        this.store.dispatch(SettingActions.add_Settings({ settings }));
      }
    }
  }
}
