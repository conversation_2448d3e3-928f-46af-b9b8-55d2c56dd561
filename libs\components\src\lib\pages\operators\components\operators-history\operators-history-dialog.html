<p-dialog
  [draggable]="true"
  [closable]="true"
  [modal]="true"
  [visible]="isVisible()"
  (visibleChange)="hideDialog()"
  [style]="{ width: '800px' }"
>
  <ng-template pTemplate="header">
    <div class="header">History</div>
  </ng-template>
  <p-tabView class="horizontal-tab border-bottom pb-22 custom-layout">
    <p-tabPanel header="Clusters">
      <div class="mt-10">
        <md-asset-clients-history-table
          [isOperatorsPage]="true"
          [filteredOperatorAssets]="clusterClientAssets()"
        ></md-asset-clients-history-table>
      </div>
    </p-tabPanel>
    <p-tabPanel header="Installations">
      <div class="mt-10">
        <md-asset-clients-history-table
          [filteredOperatorAssets]="installationsClientAssets()"
          [isOperatorsPage]="true"
        ></md-asset-clients-history-table>
      </div>
    </p-tabPanel>
    <p-tabPanel header="Wells">
      <div class="mt-10">
        <md-asset-clients-history-table
          [filteredOperatorAssets]="wellsClientAssets()"
          [isOperatorsPage]="true"
        ></md-asset-clients-history-table>
      </div>
    </p-tabPanel>
    <p-tabPanel header="Name">
      <div class="mt-10">
        <md-clients-history-name-table></md-clients-history-name-table>
      </div>
    </p-tabPanel>
  </p-tabView>
</p-dialog>
