import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>Def<PERSON>,
  NgI<PERSON>,
} from '@angular/common';
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  DestroyRef,
  inject,
  OnInit,
  ViewChild,
} from '@angular/core';
import { FormsModule } from '@angular/forms';
import { Actions, ofType } from '@ngrx/effects';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

import { Store } from '@ngrx/store';
import { ConfirmationService } from 'primeng/api';

import { InputTextModule } from 'primeng/inputtext';
import { Table, TableModule } from 'primeng/table';

import { InitializeActivitiesTable } from './helper/activities-table';
import { ActivitiesTableFields } from './helper/activities-table-fields.enum';
import { Activity } from 'libs/services/src/lib/services/maintenance/interfaces/activity.interface';
import { ActivityConfigActions } from 'libs/services/src/lib/services/maintenance/store/actions/activity-config.actions';
import { CustomChipComponent } from 'libs/components/src/lib/components/custom-chip/custom-chip.component';
import { activityConfigFeature } from 'libs/services/src/lib/services/maintenance/store/features';
import { FilterByPipe } from 'libs/components/src/lib/pipes';
import { Constant } from 'libs/services/src/lib/services/shared/constant.interface';
import { ActivityAddEditComponent } from './components/activity-add-edit/activity-add-edit.component';

@Component({
  selector: 'md-activities',
  standalone: true,
  templateUrl: './activities.page.html',
  imports: [
    InputTextModule,
    TableModule,
    NgFor,
    NgSwitch,
    NgSwitchCase,
    NgSwitchDefault,
    FormsModule,
    NgIf,
    CustomChipComponent,
    FilterByPipe,
    ActivityAddEditComponent
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ActivitiesPage implements OnInit {
  @ViewChild('table') table!: Table;
  private readonly store = inject(Store);
  private readonly actions = inject(Actions);
  private readonly confirmationService = inject(ConfirmationService);
  private readonly destroyRef = inject(DestroyRef);
  private readonly cdr = inject(ChangeDetectorRef);

  searchValue = '';
  listColumns = InitializeActivitiesTable();
  activities: Activity[] = [];
  tableFields = ActivitiesTableFields;
  tableWidth = 700;
  activityTypeList = this.store.selectSignal(
    activityConfigFeature.selectActivityTypeList
  );

  byValue = (value: string) => (item: Constant) => item.value === value;

  ngOnInit() {
    this.store.dispatch(ActivityConfigActions.init_activities());

    this.actions
      .pipe(
        ofType(ActivityConfigActions.load_Activity_Configs_Success),
        takeUntilDestroyed(this.destroyRef)
      )
      .subscribe(({ activities }) => {
        this.searchValue = '';
        this.activities = [...activities];
        this.table.reset();
        this.cdr.markForCheck();
      });
  }

  create() {
    this.store.dispatch(
      ActivityConfigActions.change_visibility_add_edit({
        visible: true,
        activity: null,
      })
    );
  }

  edit(activity: Activity) {
    this.store.dispatch(
      ActivityConfigActions.change_visibility_add_edit({
        visible: true,
        activity,
      })
    );
  }

  remove(activity: Activity) {
    this.confirmationService.confirm({
      header: 'Delete',
      message: `
          Deleting this blocking activity may affect other parts of Master Data.
          <br>
          Do you want to remove this blocking activity?
        `,
      acceptLabel: 'Confirm',
      rejectLabel: 'Cancel',
      acceptButtonStyleClass: 'btn-negative-primary',
      rejectButtonStyleClass: 'btn-tertiary',
      acceptIcon: 'none',
      rejectIcon: 'none',
      accept: () => {
        this.store.dispatch(
          ActivityConfigActions.remove_Activity_Config({
            id: activity.activityId,
          })
        );
      },
    });
  }

  export() {
    this.store.dispatch(ActivityConfigActions.export_Activities());
  }
}
