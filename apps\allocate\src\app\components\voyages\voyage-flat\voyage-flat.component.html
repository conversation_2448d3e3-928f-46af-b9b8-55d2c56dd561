<ng-template #header>
  <div class="voyage_flat__header">
    <div class="voyage_flat__header_left">
      <ng-content select="[leftSide]"></ng-content>
    </div>
    <div class="voyage_flat__header_right">
      <ng-content select="[leftFterSearch]"></ng-content>
      <lha-search
        class="al"
        [value]="searchQuery.searchTerm"
        (querySearch)="applyFilter($event)"
      ></lha-search>
      <ng-content select="[rightFterSearch]"></ng-content>
    </div>
  </div>
</ng-template>
<lha-page-container
  [contentRelative]="true"
  [tableContainer]="true"
  [footerTemplate]="footer"
  [headerTemplate]="header"
>
  <div class="voyage_flat" *lhaLoading="loading">
    <div class="voyage_flat__container">
      <ng-container *ngFor="let item of voyageViewList">
        <lha-voyage-card [voyage]="item"></lha-voyage-card>
      </ng-container>
    </div>
    <div *ngIf="!voyageViewList.length" class="voyage_flat__no_results">
      No results found.
    </div>
  </div>
</lha-page-container>

<ng-template #footer>
  <mat-paginator
    [pageSizeOptions]="[10, 20]"
    [length]="voyageListCopy.length"
    [pageSize]="searchQuery.pageSize"
    [pageIndex]="searchQuery.pageIndex"
    (page)="changePage($event)"
    showFirstLastButtons
  >
  </mat-paginator>
</ng-template>
