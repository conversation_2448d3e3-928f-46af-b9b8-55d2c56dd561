import { DateTime } from 'luxon';

/**
 * Converts a date from the user's local timezone to UTC
 * @param date - The date to convert (can be Date object or ISO string)
 * @param userTimezone - The user's timezone (e.g., 'America/New_York', 'Europe/London')
 * @returns Date object in UTC
 */
export function convertToUtcFromUserTimezone(
  date: Date | string,
  userTimezone: string
): Date {
  if (!date) {
    throw new Error('Date is required for timezone conversion');
  }

  if (!userTimezone) {
    throw new Error('User timezone is required for conversion');
  }

  try {
    // Convert input to DateTime object
    let dateTime: DateTime;

    if (typeof date === 'string') {
      // Parse ISO string and assume it's in the user's timezone
      dateTime = DateTime.fromISO(date, { zone: userTimezone });
    } else {
      // Convert Date object assuming it represents local time in user's timezone
      dateTime = DateTime.fromJSDate(date, { zone: userTimezone });
    }

    if (!dateTime.isValid) {
      throw new Error(
        `Invalid date provided: ${date}. Error: ${dateTime.invalidReason}`
      );
    }

    // Convert to UTC and return as JavaScript Date
    return dateTime.toUTC().toJSDate();
  } catch (error) {
    throw new Error(
      `Failed to convert date to UTC: ${
        error instanceof Error ? error.message : 'Unknown error'
      }`
    );
  }
}

/**
 * Converts a UTC date to the user's local timezone
 * @param utcDate - The UTC date to convert (can be Date object or ISO string)
 * @param userTimezone - The user's timezone (e.g., 'America/New_York', 'Europe/London')
 * @returns Date object in user's timezone
 */
export function convertFromUtcToUserTimezone(
  utcDate: Date | string,
  userTimezone: string
): Date {
  if (!utcDate) {
    throw new Error('UTC date is required for timezone conversion');
  }

  if (!userTimezone) {
    throw new Error('User timezone is required for conversion');
  }

  try {
    // Convert input to DateTime object in UTC
    let dateTime: DateTime;

    if (typeof utcDate === 'string') {
      // Parse ISO string as UTC
      dateTime = DateTime.fromISO(utcDate, { zone: 'utc' });
    } else {
      // Convert Date object as UTC
      dateTime = DateTime.fromJSDate(utcDate, { zone: 'utc' });
    }

    if (!dateTime.isValid) {
      throw new Error(
        `Invalid UTC date provided: ${utcDate}. Error: ${dateTime.invalidReason}`
      );
    }

    // Convert to user's timezone and return as JavaScript Date
    return dateTime.setZone(userTimezone).toJSDate();
  } catch (error) {
    throw new Error(
      `Failed to convert UTC date to user timezone: ${
        error instanceof Error ? error.message : 'Unknown error'
      }`
    );
  }
}

/**
 * Formats a date in the user's timezone
 * @param date - The date to format (can be Date object or ISO string)
 * @param userTimezone - The user's timezone (e.g., 'America/New_York', 'Europe/London')
 * @param format - The format string (default: 'dd/MM/yyyy HH:mm')
 * @param isUtc - Whether the input date is in UTC (default: true)
 * @returns Formatted date string
 */
export function formatDateInUserTimezone(
  date: Date | string,
  userTimezone: string,
  format: string = 'dd/MM/yyyy HH:mm',
  isUtc: boolean = true
): string {
  if (!date) {
    return '';
  }

  if (!userTimezone) {
    throw new Error('User timezone is required for formatting');
  }

  try {
    // Convert input to DateTime object
    let dateTime: DateTime;

    if (typeof date === 'string') {
      dateTime = DateTime.fromISO(date, { zone: isUtc ? 'utc' : userTimezone });
    } else {
      dateTime = DateTime.fromJSDate(date, {
        zone: isUtc ? 'utc' : userTimezone,
      });
    }

    if (!dateTime.isValid) {
      throw new Error(
        `Invalid date provided: ${date}. Error: ${dateTime.invalidReason}`
      );
    }

    // Convert to user's timezone and format
    return dateTime.setZone(userTimezone).toFormat(format);
  } catch (error) {
    console.error('Error formatting date:', error);
    return '';
  }
}

/**
 * Formats a date assuming it's already in the user's timezone (no conversion)
 * @param date - The date to format (can be Date object or ISO string)
 * @param userTimezone - The user's timezone (used for DST calculations)
 * @param format - The format string (default: 'dd/MM/yyyy HH:mm')
 * @returns Formatted date string
 */
export function formatDateAsUserTimezone(
  date: Date | string,
  userTimezone: string,
  format: string = 'dd/MM/yyyy HH:mm'
): string {
  if (!date) {
    return '';
  }

  if (!userTimezone) {
    throw new Error('User timezone is required for formatting');
  }

  try {
    // Convert input to DateTime object, treating it as if it's already in the user's timezone
    let dateTime: DateTime;

    if (typeof date === 'string') {
      // Parse the string and assume it represents time in the user's timezone
      dateTime = DateTime.fromISO(date, { zone: userTimezone });
    } else {
      // Convert Date object assuming it represents time in the user's timezone
      dateTime = DateTime.fromJSDate(date, { zone: userTimezone });
    }

    if (!dateTime.isValid) {
      throw new Error(
        `Invalid date provided: ${date}. Error: ${dateTime.invalidReason}`
      );
    }

    // Format in the same timezone (no conversion)
    return dateTime.toFormat(format);
  } catch (error) {
    console.error('Error formatting date:', error);
    return '';
  }
}

/**
 * Gets the current date/time in the user's timezone
 * @param userTimezone - The user's timezone (e.g., 'America/New_York', 'Europe/London')
 * @returns Date object in user's timezone
 */
export function getCurrentDateInUserTimezone(userTimezone: string): Date {
  if (!userTimezone) {
    throw new Error('User timezone is required');
  }

  try {
    return DateTime.now().setZone(userTimezone).toJSDate();
  } catch (error) {
    throw new Error(
      `Failed to get current date in user timezone: ${
        error instanceof Error ? error.message : 'Unknown error'
      }`
    );
  }
}

/**
 * Checks if a timezone is valid
 * @param timezone - The timezone to validate
 * @returns boolean indicating if timezone is valid
 */
export function isValidTimezone(timezone: string): boolean {
  if (!timezone) {
    return false;
  }

  try {
    const dt = DateTime.now().setZone(timezone);
    return dt.isValid;
  } catch {
    return false;
  }
}

/**
 * Gets timezone offset in minutes for a specific timezone
 * @param timezone - The timezone to get offset for
 * @param date - Optional date to get offset for (defaults to now)
 * @returns Offset in minutes
 */
export function getTimezoneOffset(
  timezone: string,
  date?: Date | string
): number {
  if (!timezone) {
    throw new Error('Timezone is required');
  }

  try {
    const dateTime = date
      ? typeof date === 'string'
        ? DateTime.fromISO(date)
        : DateTime.fromJSDate(date)
      : DateTime.now();

    return dateTime.setZone(timezone).offset;
  } catch (error) {
    throw new Error(
      `Failed to get timezone offset: ${
        error instanceof Error ? error.message : 'Unknown error'
      }`
    );
  }
}
