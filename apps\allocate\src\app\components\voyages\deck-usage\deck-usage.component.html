<div class="deck-usage" *ngIf="deckUsageState()">
  <div class="deck-usage__toolbar">
    <h2 class="deck-usage__title">Deck Usage</h2>
    <div class="deck-usage__toolbar-actions">
      <button
        class="btn-secondary"
        type="button"
        lhaIsVoyageLockedDisable
        (click)="addEditDeckUsage()"
      >
        Create Cargo
      </button>
    </div>
  </div>
  <div class="deck-usage__content">
    <div class="deck-usage__column">
      <p-card>
        <ng-template pTemplate="header">
          <div class="deck-usage__header">
            <h3 class="deck-usage__table-header" tableHeaderTitle>Cargo In</h3>
            <div class="deck-usage__toolbar-actions">
              <form [formGroup]="searchInForm">
                <div class="deck-usage__search-container">
                  <i class="pi pi-search"></i>
                  <input
                    type="text"
                    pInputText
                    formControlName="searchString"
                    placeholder="Search..."
                    (input)="onSearchInChange()"
                  />
                </div>
              </form>
              <button
                class="btn-secondary"
                type="button"
                [disabled]="deckUsageState().loading.export"
                (click)="exportDeckIn()"
              >
                Export
              </button>
            </div>
          </div>
        </ng-template>
        <div class="deck-usage__table-container">
          <p-table
            #dtIn
            [columns]="columns"
            [value]="deckUsagesInMutable()"
            [loading]="deckUsageState().loading.list"
            [paginator]="true"
            [rows]="query.pageSize"
            [rowsPerPageOptions]="[5, 10, 20, 50]"
            [globalFilterFields]="[
              'assetName',
              'numberOfLifts',
              'totalWeight',
              'clientName'
            ]"
            styleClass="p-datatable-sm"
            [tableStyle]="{ 'min-width': '50rem' }"
          >
            <ng-template pTemplate="header" let-columns>
              <tr>
                <th
                  *ngFor="let column of columns"
                  [pSortableColumn]="column.sortable ? column.field : null"
                  [style.width.px]="column.width"
                >
                  {{ column.name }}
                  <p-sortIcon
                    *ngIf="column.sortable"
                    [field]="column.field"
                  ></p-sortIcon>
                </th>
                <th *ngIf="voyage()?.voyageStatus !== voyageStatus.Completed">
                  Actions
                </th>
              </tr>
            </ng-template>
            <ng-template pTemplate="body" let-item let-columns="columns">
              <tr class="deck-usage__row-actions">
                <td *ngFor="let column of columns">{{ item[column.field] }}</td>
                <td *ngIf="voyage()?.voyageStatus !== voyageStatus.Completed">
                  <div class="deck-usage__actions-column">
                    <button
                      type="button"
                      class="btn-icon-only"
                      (click)="addEditDeckUsage(item)"
                      lhaIsVoyageLockedDisable
                    >
                      <i class="pi pi-pencil"></i>
                    </button>
                    <button
                      type="button"
                      class="btn-icon-only"
                      (click)="removeDeckUsage(item)"
                      lhaIsVoyageLockedDisable
                    >
                      <i class="pi pi-trash"></i>
                    </button>
                  </div>
                </td>
              </tr>
            </ng-template>
            <ng-template pTemplate="emptymessage">
              <tr>
                <td colspan="5" class="text-center">
                  No cargo in records found.
                </td>
              </tr>
            </ng-template>
          </p-table>
        </div>
      </p-card>
    </div>
    <div class="deck-usage__column">
      <p-card>
        <ng-template pTemplate="header">
          <div class="deck-usage__header">
            <h3
              class="deck-usage__table-header"
              style="margin-bottom: 10px"
              tableHeaderTitle
            >
              Cargo Out
            </h3>
            <div class="deck-usage__toolbar-actions">
              <form [formGroup]="searchOutForm">
                <div class="deck-usage__search-container">
                  <i class="pi pi-search"></i>
                  <input
                    type="text"
                    pInputText
                    formControlName="searchString"
                    placeholder="Search..."
                    (input)="onSearchOutChange()"
                  />
                </div>
              </form>
              <button
                class="btn-secondary"
                type="button"
                [disabled]="deckUsageState().loading.export"
                (click)="exportDeckOut()"
              >
                Export
              </button>
            </div>
          </div>
        </ng-template>
        <div class="deck-usage__table-container">
          <p-table
            #dtOut
            [columns]="columns"
            [value]="deckUsagesOutMutable()"
            [loading]="deckUsageState().loading.list"
            [paginator]="true"
            [rows]="query.pageSize"
            [rowsPerPageOptions]="[5, 10, 20, 50]"
            [globalFilterFields]="[
              'assetName',
              'numberOfLifts',
              'totalWeight',
              'clientName'
            ]"
            styleClass="p-datatable-sm"
            [tableStyle]="{ 'min-width': '50rem' }"
          >
            <ng-template pTemplate="header" let-columns>
              <tr>
                <th
                  *ngFor="let column of columns"
                  [pSortableColumn]="column.sortable ? column.field : null"
                  [style.width.px]="column.width"
                >
                  {{ column.name }}
                  <p-sortIcon
                    *ngIf="column.sortable"
                    [field]="column.field"
                  ></p-sortIcon>
                </th>
                <th *ngIf="voyage()?.voyageStatus !== voyageStatus.Completed">
                  Actions
                </th>
              </tr>
            </ng-template>
            <ng-template pTemplate="body" let-item let-columns="columns">
              <tr class="deck-usage__row-actions">
                <td *ngFor="let column of columns">{{ item[column.field] }}</td>
                <td *ngIf="voyage()?.voyageStatus !== voyageStatus.Completed">
                  <div class="deck-usage__actions-column">
                    <button
                      class="btn-icon-only"
                      type="button"
                      (click)="addEditDeckUsage(item)"
                      lhaIsVoyageLockedDisable
                    >
                      <i class="pi pi-pencil"></i>
                    </button>
                    <button
                      class="btn-icon-only"
                      type="button"
                      lhaIsVoyageLockedDisable
                      (click)="removeDeckUsage(item)"
                    >
                      <i class="pi pi-trash"></i>
                    </button>
                  </div>
                </td>
              </tr>
            </ng-template>
            <ng-template pTemplate="emptymessage">
              <tr>
                <td colspan="5" class="text-center">
                  No cargo out records found.
                </td>
              </tr>
            </ng-template>
          </p-table>
        </div>
      </p-card>
    </div>
  </div>
</div>

<p-confirmDialog />
<lha-deck-usage-add-edit />
