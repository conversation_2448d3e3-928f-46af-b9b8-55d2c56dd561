<p-dialog
  [draggable]="true"
  [closable]="true"
  [modal]="true"
  [visible]="isVisible()"
  (visibleChange)="hideDialog()"
  [style]="{ width: '600px' }"
>
  <ng-template pTemplate="header">
    <div class="header">{{ distance() ? 'Edit' : 'Add' }} Distance</div>
  </ng-template>
  <ng-template pTemplate="content">
    <form [formGroup]="form" class="d-flex flex-wrap gap-16">
      <div class="d-flex w-100 justify-content-between gap-8">
        <div class="d-flex w-50 flex-direction-column gap-4">
          <label class="fs-14">First Asset</label>
          <p-dropdown
            [options]="assets()"
            [filter]="true"
            [showClear]="true"
            formControlName="baseAssetId"
            optionLabel="name"
            optionValue="assetId"
            appendTo="body"
            styleClass="new-version"
            panelStyleClass="new-version-panel"
          />
          <small
            class="validation-control-error"
            *ngIf="
              controls.baseAssetId?.invalid &&
              controls.baseAssetId?.touched &&
              controls.baseAssetId?.hasError('required')
            "
          >
            First Asset is required.
          </small>
        </div>
        <div class="d-flex w-50 flex-direction-column gap-4">
          <label class="fs-14">Second Asset</label>
          <p-dropdown
            [options]="assets()"
            [filter]="true"
            [showClear]="true"
            formControlName="toAssetId"
            optionLabel="name"
            optionValue="assetId"
            appendTo="body"
            styleClass="new-version"
            panelStyleClass="new-version-panel"
          />
          <small
            class="validation-control-error"
            *ngIf="controls.toAssetId?.invalid && controls.toAssetId?.touched"
          >
            {{
              controls.toAssetId?.hasError('required')
                ? 'First Asset is required.'
                : 'Second and First Asset must be different.'
            }}
          </small>
        </div>
      </div>
      <div class="d-flex w-100 justify-content-between gap-8">
        <div class="d-flex w-50 flex-direction-column gap-4">
          <label class="fs-14">Distance (miles)</label>
          <p-inputNumber
            formControlName="distanceInMiles"
            mode="decimal"
            [maxFractionDigits]="5"
          />
          <small
            class="validation-control-error"
            *ngIf="
              controls.distanceInMiles?.invalid &&
              controls.distanceInMiles?.touched
            "
          >
            {{
              controls.distanceInMiles?.hasError('required')
                ? 'First Asset is required.'
                : 'Distance must be a number greater than 0.'
            }}
          </small>
        </div>
      </div>
    </form>
  </ng-template>
  <ng-template pTemplate="footer">
    <div class="actions">
      <button class="btn-tertiary" type="button" (click)="hideDialog()">
        Cancel
      </button>
      <button class="btn-primary" type="button" (click)="onSubmit()">
        {{ distance() ? 'Save' : 'Add' }}
      </button>
    </div>
  </ng-template>
</p-dialog>
