<div class="voyage_card">
  <div class="voyage_card__img">
    <img
      *ngIf="voyage.vesselVesselPictureId"
      [src]="url + '/api/vessel/photo/' + voyage.vesselVesselPictureId"
      alt=""
      (click)="showImgView()"
    />
    <mat-icon *ngIf="!voyage.vesselVesselPictureId" color="primary"
      >directions_boat</mat-icon
    >
  </div>
  <a [routerLink]="[voyage.voyageId]" class="voyage_card__des">
    <span class="voyage_card__box">
      <span class="voyage_card__title"> Vessel </span>
      <span class="voyage_card__text">
        {{ voyage.displayVesselName }}
      </span>
    </span>
    <span class="voyage_card__box">
      <span class="voyage_card__title"> Voyage </span>
      <span class="voyage_card__text">
        {{ voyage.voyageNumber }}
      </span>
    </span>
    <span class="voyage_card__box">
      <span class="voyage_card__title"> Total Mileage </span>
      <span class="voyage_card__text">
        {{ voyage.totalMileage }}
      </span>
    </span>
    <span class="voyage_card__box">
      <span class="voyage_card__title"> Date </span>
      <span class="voyage_card__text">
        {{ voyage.createdDate | date : 'd-M-yy' }}
      </span>
    </span>
    <mat-icon class="voyage_card__arr" color="warn">arrow_forward</mat-icon>
    <mat-icon
      color="warn"
      class="voyage_card__issue"
      *ngIf="voyage.voyageNotValid"
      >error_outline</mat-icon
    >
  </a>
</div>
