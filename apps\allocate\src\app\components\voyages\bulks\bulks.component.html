<lha-table
  *ngIf="bulksState()"
  [columns]="dynamicColumns()"
  [loading]="bulksState().loading.list"
  [exportLoading]="bulksState().loading.export"
  [data]="bulksState().bulks"
  [pageSize]="query.pageSize"
  [canExportTable]="true"
  [tableName]="voyage()?.voyageNumber + '_bulks'"
  (changeFilter)="changeBulkType($event)"
  (exportTable)="exportBulkTransactions()"
  [filters]="filters"
>
  <h3 class="mat-title--warn" style="margin-bottom: 10px" tableHeaderTitle>
    Bulk Transactions
  </h3>
  <div
    class="bulk-quantities"
    tableHeaderFilterRight
    *ngIf="bulksState().bulkTypeId"
  >
    <ng-container *ngIf="bulkQuantities$ | async as bq">
      <ng-container *ngIf="bulktype$ | async as bt">
        <div class="bulk-quantities__text">
          Start {{ bq.startQuantity }} {{ bt.unitName }}
        </div>
        <div class="bulk-quantities__text">
          End {{ bq.endQuantity }} {{ bt.unitName }}
        </div>
      </ng-container>
    </ng-container>
  </div>
  <ng-container tableHeaderRightBtn>
    <button mat-icon-button *lhaIsVoyageLocked style="pointer-events: none">
      <mat-icon color="warn">lock</mat-icon>
    </button>
    <button
      mat-raised-button
      lhaIsVoyageLockedDisable
      (click)="addEditBulk(null)"
      color="primary"
    >
      Create
    </button>
  </ng-container>
  <ng-template lhaHeaderCellTemplate="dayRatePrice">
    {{ 'Price ' + ((appSettings$ | async)?.currency ?? '') }}
  </ng-template>
  <ng-template lhaCellTemplate="completedDateTime" let-item>
    {{ item.completedDateTime | date : 'dd/MM/yyyy HH:mm' }}
  </ng-template>
  <ng-template lhaCellTemplate="dayRatePrice" let-item>
    {{ item.dayRatePrice | number : '1.2-2' }}
  </ng-template>
  <ng-template lhaCellTemplate="bulkTransactionId" let-item>
    <button
      mat-icon-button
      color="primary"
      (click)="addEditBulk(item)"
      lhaIsVoyageLockedDisable
    >
      <mat-icon>edit</mat-icon>
    </button>
    <button
      mat-icon-button
      color="warn"
      (click)="removeBulk(item)"
      lhaIsVoyageLockedDisable
    >
      <mat-icon>delete</mat-icon>
    </button>
  </ng-template>
</lha-table>
