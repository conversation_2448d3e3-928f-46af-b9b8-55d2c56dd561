import { Component, inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { ImageAuthDirective } from '../../../shared/directives/image-auth.directive';

@Component({
  selector: 'lha-image-view',
  standalone: true,
  imports: [
    MatIconModule,
    MatDialogModule,
    MatButtonModule,
    ImageAuthDirective,
  ],
  templateUrl: './image-view.component.html',
  styleUrls: ['./image-view.component.scss'],
})
export class ImageViewComponent {
  data: { imgUrl: string } = inject(MAT_DIALOG_DATA);
}
