import { Component, inject, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import {
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { LoadingDirective } from 'libs/components/src/lib/directives/loading.directive';
import { AsyncPipe, NgIf } from '@angular/common';
import { DeckUsage } from 'libs/services/src/lib/services/voyages/interfaces/deck-usage.interface';
import { ActivatedRoute } from '@angular/router';
import { UtilityService } from 'libs/services/src/lib/services/utility.service';
import { Store } from '@ngrx/store';
import { Actions, ofType } from '@ngrx/effects';
import { Subject, takeUntil } from 'rxjs';
import { DeckUsageActions } from 'libs/services/src/lib/services/voyages/store/actions/deck-usage.actions';
import { deckUsagesFeature } from 'libs/services/src/lib/services/voyages/store/features';
import { greaterThan } from 'libs/components/src/lib/validators/greaterThan';
import { ButtonModule } from 'primeng/button';
import { InputNumberModule } from 'primeng/inputnumber';
import { DropdownModule } from 'primeng/dropdown';
import { InputSwitchModule } from 'primeng/inputswitch';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { DialogModule } from 'primeng/dialog';

@Component({
  selector: 'lha-deck-usage-add-edit',
  standalone: true,
  imports: [
    FormsModule,
    LoadingDirective,
    ReactiveFormsModule,
    AsyncPipe,
    NgIf,
    ButtonModule,
    InputNumberModule,
    DropdownModule,
    InputSwitchModule,
    ProgressSpinnerModule,
    DialogModule,
  ],
  templateUrl: './deck-usage-add-edit.component.html',
  styleUrls: ['./deck-usage-add-edit.component.scss'],
})
export class DeckUsageAddEditComponent implements OnInit, OnDestroy {
  activatedRoute = inject(ActivatedRoute);
  utilityService = inject(UtilityService);
  store = inject(Store);
  action = inject(Actions);
  vm = this.store.selectSignal(deckUsagesFeature.selectDeckUsagesState);
  voyageId = this.utilityService.getParamsFromRoute(
    this.activatedRoute.snapshot.root
  )['id'];
  isAdd = true;
  unsubscribe: Subject<boolean> = new Subject();
  deckUsage!: DeckUsage;
  cargoTypeOptions = [
    { label: 'Cargo In', value: true },
    { label: 'Cargo Out', value: false },
  ];

  form = new FormGroup({
    assetId: new FormControl<string>('', [Validators.required]),
    numberOfLifts: new FormControl<number | null>(null, [
      Validators.required,
      greaterThan(0),
    ]),
    totalWeight: new FormControl<number | null>(null, [
      Validators.required,
      greaterThan(0),
    ]),
    isCargoIn: new FormControl<boolean>(false),
  });

  ngOnInit(): void {
    this.subCloseDialog();

    this.action
      .pipe(
        ofType(DeckUsageActions.open_Deck_Usage_Add_Edit_Dialog),
        takeUntil(this.unsubscribe)
      )
      .subscribe((action) => {
        this.isAdd = !action.isEdit;
        if (action.deckUsage) {
          this.deckUsage = action.deckUsage;
          this.pathForm(this.deckUsage);
        }
      });
  }

  ngOnDestroy(): void {
    this.unsubscribe.next(true);
    this.unsubscribe.complete();
  }

  private subCloseDialog(): void {
    this.action
      .pipe(
        ofType(
          DeckUsageActions.add_Deck_Usage_Success,
          DeckUsageActions.edit_Deck_Usage_Success
        )
      )
      .pipe(takeUntil(this.unsubscribe))
      .subscribe(() => {
        this.hideDialog();
      });
  }

  closeDialog(): void {
    this.hideDialog();
  }

  hideDialog(): void {
    this.form.reset();
    this.form.markAsUntouched();
    this.store.dispatch(
      DeckUsageActions.open_Deck_Usage_Add_Edit_Dialog({
        isVisible: false,
        isEdit: false,
        deckUsage: null,
      })
    );
  }

  isVisibleDialog(): boolean {
    return this.vm().isDeckUsageAddEditDialogVisible;
  }

  private pathForm(deckUsage: DeckUsage): void {
    this.form.patchValue(deckUsage);
  }

  saveDeckUsage(): void {
    if (this.form.invalid) {
      this.form.markAllAsTouched();
      return;
    }

    const model = {
      ...this.form.value,
      voyageId: this.voyageId,
    } as DeckUsage;

    if (this.isAdd) {
      this.store.dispatch(
        DeckUsageActions.add_Deck_Usage({ deckUsage: model })
      );
    } else {
      this.store.dispatch(
        DeckUsageActions.edit_Deck_Usage({
          deckUsageId: this.deckUsage.deckUsageId,
          deckUsage: model,
        })
      );
    }
  }
}
