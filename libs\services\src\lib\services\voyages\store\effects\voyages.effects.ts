import { HttpErrorResponse } from '@angular/common/http';
import { inject } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { catchError, concatMap, map, mergeMap, of } from 'rxjs';
import { Voyage } from '../../interfaces/voyage.interface';
import { Asset } from '../../../maintenance/interfaces/asset.interface';
import { VoyageService } from '../../voyage.service';
import { VoyagesActions } from '../actions/voyages.actions';
import { FileService } from '../../../file.service';
import { Store } from '@ngrx/store';
import { filter, switchMap, tap } from 'rxjs/operators';
import { MatDialog } from '@angular/material/dialog';
import { TankManagementActions } from '../actions/tank-management.actions';
import { ActivityActions } from '../actions/activity.actions';
import { BulksActions } from '../actions/bulks.actions';
import { UtilityService } from '../../../utility.service';
import { ActivatedRoute, Router } from '@angular/router';
import { Vessel } from '../../../vessels/interfaces/vessel.interface';
import { DeckUsageActions } from '../actions/deck-usage.actions';
import { ImageViewComponent } from '../../../../../../../../apps/allocate/src/app/components/voyages/image-view/image-view.component';
import { AssetService } from '../../../maintenance/asset.service';

export const loadVoyages = createEffect(
  (actions = inject(Actions), voyageService = inject(VoyageService)) => {
    return actions.pipe(
      ofType(VoyagesActions.load_Voyages),
      switchMap(() =>
        voyageService.loadVoyages().pipe(
          map((res: Voyage[]) =>
            VoyagesActions.load_Voyages_Success({ voyages: res })
          ),
          catchError((error: HttpErrorResponse) =>
            of(VoyagesActions.load_Voyages_Failure({ error }))
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const loadVoyagesList = createEffect(
  (actions = inject(Actions), voyageService = inject(VoyageService)) => {
    return actions.pipe(
      ofType(VoyagesActions.load_Voyage_Lists),
      switchMap(({ startDate, endDate }) =>
        voyageService.loadVoyages(startDate!, endDate!).pipe(
          map((res: Voyage[]) =>
            VoyagesActions.load_Voyage_Lists_Success({ voyages: res })
          ),
          catchError((error: HttpErrorResponse) =>
            of(VoyagesActions.load_Voyage_Lists_Failure({ error }))
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const loadVoyagesListSuccesses = createEffect(
  (actions = inject(Actions)) => {
    return actions.pipe(
      ofType(
        VoyagesActions.add_Voyage_Success,
        VoyagesActions.import_Voyage_Template_Success,
        VoyagesActions.edit_Voyage_Success,
        VoyagesActions.complete_Voyage_Success,
        VoyagesActions.incomplete_Voyage_Success
      ),
      switchMap(() =>
        of(
          VoyagesActions.load_Voyage_Lists({
            startDate: null,
            endDate: null,
          })
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const loadTransportRequestAssignedActiveVoyages = createEffect(
  (actions = inject(Actions), voyageService = inject(VoyageService)) => {
    return actions.pipe(
      ofType(VoyagesActions.load_Transport_Request_Assigned_Active_Voyages),
      mergeMap(({ locationId, status }) =>
        voyageService
          .loadTransportRequestAssignedActiveVoyagesByLocationId(
            locationId,
            status
          )
          .pipe(
            map((res: Voyage[]) =>
              VoyagesActions.load_Transport_Request_Assigned_Active_Voyages_Success(
                { voyages: res }
              )
            ),
            catchError((error: HttpErrorResponse) =>
              of(
                VoyagesActions.load_Transport_Request_Assigned_Active_Voyages_Failure(
                  { error }
                )
              )
            )
          )
      )
    );
  },
  {
    functional: true,
  }
);

export const addVoyage = createEffect(
  (
    actions = inject(Actions),
    voyageService = inject(VoyageService),
    router = inject(Router)
  ) => {
    return actions.pipe(
      ofType(VoyagesActions.add_Voyage),
      mergeMap((action) =>
        voyageService.addVoyage(action.voyage).pipe(
          map((res: Voyage) => {
            router.navigate(['voyages', res.voyageId, 'activity']);
            return VoyagesActions.add_Voyage_Success({
              voyage: res,
              successMessage: 'Voyage added successfully!',
            });
          }),
          catchError((error: HttpErrorResponse) =>
            of(VoyagesActions.add_Voyage_Failure({ error }))
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const completeVoyage = createEffect(
  (
    actions = inject(Actions),
    voyageService = inject(VoyageService),
    fileService = inject(FileService)
  ) => {
    return actions.pipe(
      ofType(VoyagesActions.complete_Voyage),
      mergeMap((action) =>
        voyageService.completeVoyage(action.voyageId).pipe(
          mergeMap((response) => {
            if (response instanceof ArrayBuffer && response.byteLength > 0) {
              fileService.downloadFile(response, 'VoyageCompletedErrors');

              return of(
                VoyagesActions.complete_Voyage_Failure({
                  error: new HttpErrorResponse({
                    status: 400,
                    statusText:
                      'Cannot complete voyage - see downloaded file for errors',
                    error:
                      'Cannot complete voyage - see downloaded file for errors',
                  }),
                })
              );
            } else {
              return of(
                VoyagesActions.complete_Voyage_Success({
                  successMessage: 'Voyage completed successfully',
                })
              );
            }
          }),
          catchError((error: HttpErrorResponse) =>
            of(VoyagesActions.complete_Voyage_Failure({ error }))
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const incompleteVoyage = createEffect(
  (actions = inject(Actions), voyageService = inject(VoyageService)) => {
    return actions.pipe(
      ofType(VoyagesActions.incomplete_Voyage),
      mergeMap((action) =>
        voyageService.incompleteVoyage(action.voyageId).pipe(
          map((res: Voyage) =>
            VoyagesActions.incomplete_Voyage_Success({
              voyage: res,
              successMessage: 'Voyage incompleted successfully!',
            })
          ),
          catchError((error: HttpErrorResponse) =>
            of(VoyagesActions.incomplete_Voyage_Failure({ error }))
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const editVoyage = createEffect(
  (actions = inject(Actions), voyageService = inject(VoyageService)) => {
    return actions.pipe(
      ofType(VoyagesActions.edit_Voyage),
      mergeMap((action) =>
        voyageService.editVoyage(action.voyage, action.voyageId).pipe(
          map((res: Voyage) =>
            VoyagesActions.edit_Voyage_Success({
              voyage: res,
              successMessage: 'Voyage edited successfully!',
            })
          ),
          catchError((error: HttpErrorResponse) =>
            of(VoyagesActions.edit_Voyage_Failure({ error }))
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const loadVoyageVessels = createEffect(
  (
    actions = inject(Actions),
    store = inject(Store),
    voyageService = inject(VoyageService)
  ) => {
    return actions.pipe(
      ofType(VoyagesActions.load_Voyage_Lists),
      mergeMap(() =>
        voyageService.loadVessels().pipe(
          map((res: Vessel[]) =>
            VoyagesActions.load_Vessels_Success({ vessels: res })
          ),
          catchError((error: HttpErrorResponse) =>
            of(VoyagesActions.load_Vessels_Failure({ error }))
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const loadVoyagePorts = createEffect(
  (
    actions = inject(Actions),
    store = inject(Store),
    assetService = inject(AssetService)
  ) => {
    return actions.pipe(
      ofType(VoyagesActions.load_Voyage_Lists),
      mergeMap(() =>
        assetService.loadPorts().pipe(
          map((res: Asset[]) =>
            VoyagesActions.load_Ports_Success({ ports: res })
          ),
          catchError((error: HttpErrorResponse) =>
            of(VoyagesActions.load_Ports_Failure({ error }))
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const exportVoyageTemplate = createEffect(
  (
    actions = inject(Actions),
    voyageService = inject(VoyageService),
    fileService = inject(FileService)
  ) => {
    return actions.pipe(
      ofType(VoyagesActions.export_Voyage_Template),
      mergeMap(() =>
        voyageService.exportVoyageTemplate().pipe(
          map((res: ArrayBuffer) => {
            fileService.downloadFile(res, 'voyage-template');
            return VoyagesActions.export_Voyage_Template_Success();
          }),
          catchError((error: HttpErrorResponse) =>
            of(VoyagesActions.export_Voyage_Template_Failure({ error }))
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const exportVoyages = createEffect(
  (
    actions = inject(Actions),
    voyageService = inject(VoyageService),
    fileService = inject(FileService)
  ) => {
    return actions.pipe(
      ofType(VoyagesActions.export_Voyages),
      mergeMap(() =>
        voyageService.exportVoyages().pipe(
          map((res: ArrayBuffer) => {
            fileService.downloadFile(res, 'Voyages');
            return VoyagesActions.export_Voyages_Success();
          }),
          catchError((error: HttpErrorResponse) =>
            of(VoyagesActions.export_Voyages_Failure({ error }))
          )
        )
      )
    );
  },
  {
    // You can omit this part if not needed
    // This indicates that the effect is a pure function and can be tree-shaken during optimization
    functional: true,
  }
);

export const importVoyageTemplate = createEffect(
  (
    actions = inject(Actions),
    voyageService = inject(VoyageService),
    fileService = inject(FileService)
  ) => {
    return actions.pipe(
      ofType(VoyagesActions.import_Voyage_Template),
      concatMap(({ voyageTemplate }) =>
        fileService.importTemplate(voyageTemplate)
      ),
      mergeMap((action) =>
        voyageService.importVoyageTemplate(action).pipe(
          mergeMap((response) => {
            if (response instanceof ArrayBuffer && response.byteLength > 0) {
              fileService.downloadFile(response, 'VoyageImportErrors');

              return of(
                VoyagesActions.import_Voyage_Template_Failure({
                  error: new HttpErrorResponse({
                    status: 400,
                    statusText: 'File processed but with issues',
                    error:
                      'There are issues with the import file. Please see downloaded file for the errors',
                  }),
                })
              );
            } else {
              return of(
                VoyagesActions.import_Voyage_Template_Success({
                  successMessage: 'File imported successfully',
                })
              );
            }
          }),
          catchError((error: HttpErrorResponse) =>
            of(VoyagesActions.import_Voyage_Template_Failure({ error }))
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const loadVoyageAfterActions = createEffect(
  (
    actions = inject(Actions),
    utilityService = inject(UtilityService),
    voyageService = inject(VoyageService),
    activatedRoute = inject(ActivatedRoute)
  ) => {
    return actions.pipe(
      ofType(
        TankManagementActions.add_Tank_Management_Success,
        TankManagementActions.edit_Tank_Management_Success,
        TankManagementActions.remove_Tank_Management_Success,
        ActivityActions.load_Activities_Success,
        BulksActions.add_Bulk_Success,
        BulksActions.edit_Bulk_Success,
        BulksActions.remove_Bulk_Success,
        DeckUsageActions.remove_Deck_Usage_Success,
        DeckUsageActions.add_Deck_Usage_Success,
        DeckUsageActions.edit_Deck_Usage_Success
      ),
      map(
        () =>
          utilityService.getParamsFromRoute(activatedRoute.snapshot.root)['id']
      ),
      mergeMap((id) =>
        voyageService.getVoyageById(id).pipe(
          map((res: Voyage) =>
            VoyagesActions.load_Voyage_Success({ voyage: res })
          ),
          catchError((error: HttpErrorResponse) =>
            of(VoyagesActions.load_Voyage_Failure({ error }))
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const loadVoyage = createEffect(
  (
    actions = inject(Actions),
    utilityService = inject(UtilityService),
    voyageService = inject(VoyageService),
    activatedRoute = inject(ActivatedRoute),
    router = inject(Router)
  ) => {
    return actions.pipe(
      ofType(VoyagesActions.load_Voyage),
      map(
        () =>
          utilityService.getParamsFromRoute(activatedRoute.snapshot.root)['id']
      ),
      filter(() => !router.url.includes('activity')),
      mergeMap((id) =>
        voyageService.getVoyageById(id).pipe(
          map((res: Voyage) =>
            VoyagesActions.load_Voyage_Success({ voyage: res })
          ),
          catchError((error: HttpErrorResponse) =>
            of(VoyagesActions.load_Voyage_Failure({ error }))
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const openVoyageImageViewDialog = createEffect(
  (actions = inject(Actions), dialog = inject(MatDialog)) => {
    return actions.pipe(
      ofType(VoyagesActions.open_Voyage_Image_View_Dialog),
      tap((action) => {
        dialog.open(ImageViewComponent, {
          panelClass: 'image_dialog',
          autoFocus: false,
          data: {
            imgUrl: action.imgUrl,
          },
        });
      })
    );
  },
  {
    functional: true,
    dispatch: false,
  }
);
