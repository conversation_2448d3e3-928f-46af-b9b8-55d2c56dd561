import {
  ChangeDetectionStrategy,
  Component,
  computed,
  inject,
} from '@angular/core';
import { Store } from '@ngrx/store';
import { NgClass, NgIf } from '@angular/common';

import { ConfirmationService } from 'primeng/api';
import { TableModule } from 'primeng/table';

import { DangerousGoodsPopupComponent } from 'libs/components/src/lib/components/cargo-popups/dangerous-goods-popup/dangerous-goods-popup.component';
import { VendorAddressPopupComponent } from 'libs/components/src/lib/components/cargo-popups/vendor-address-popup/vendor-address-popup.component';
import { currentUserFeature } from 'libs/auth/src/lib/store/current-user/current-user.features';
import { CargoListItem } from 'libs/components/src/lib/interfaces/cargo-list-item.interface';
import { VoyageDirection } from 'libs/services/src/lib/services/voyages/enums/voyage-direction.enum';
import { CancelDialogComponent } from 'libs/components/src/lib/components/cancel-dialog/cancel-dialog.component';
import { DeleteDialogComponent } from 'libs/components/src/lib/components/delete-dialog/delete-dialog.component';
import { MovePopupComponent } from 'libs/components/src/lib/components/cargo-popups/move-popup/move-popup.component';
import { DangerousGoodsPopupActions } from 'libs/services/src/lib/services/maintenance/store/actions/dangerous-goods-popup.actions';
import { DangerousGoodsPopupType } from 'libs/services/src/lib/services/maintenance/enums/dangerous-goods-popup-type.enum';
import { FilterByPipe } from 'libs/components/src/lib/pipes';
import { VoyageCargoStatus } from 'libs/services/src/lib/services/transport-requests/enums/voyage-cargo-status.enum';
import { VoyageStatus } from 'libs/components/src/lib/enums/voyage-status.enum';
import { voyageCargoPopupsFeature } from 'libs/components/src/lib/store/cargo-popups/cargo-popup.feature';
import { VoyageCargoPopupsActions } from 'libs/components/src/lib/store/cargo-popups/cargo-popup.actions';
import { UtilityService } from 'libs/services/src/lib/services/utility.service';
import { cargoListEditPageFeature } from 'libs/services/src/lib/services/voyages/store/features/cargo-list-edit-page.feature';
import { CargoListEditActions } from 'libs/services/src/lib/services/voyages/store/actions/cargo-list-edit.actions';
import { flowFeature } from 'libs/services/src/lib/services/voyages/store/features/flow.feature';
import { ToastService } from 'libs/components/src/lib/services/toast.service';

@Component({
  standalone: true,
  imports: [
    DangerousGoodsPopupComponent,
    VendorAddressPopupComponent,
    CancelDialogComponent,
    DeleteDialogComponent,
    NgIf,
    FilterByPipe,
    MovePopupComponent,
    TableModule,
    NgClass,
  ],
  selector: 'app-cargo-list-edit-actions',
  templateUrl: './cargo-list-edit-actions.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CargoListEditActionsComponent {
  private readonly store = inject(Store);
  private readonly toastService = inject(ToastService);
  private readonly confirmationService = inject(ConfirmationService);
  utilityService = inject(UtilityService);
  currentUser = this.store.selectSignal(currentUserFeature.selectUser);
  voyageDirection = VoyageDirection;
  selectedItems = this.store.selectSignal(
    cargoListEditPageFeature.selectSelectedItems
  );
  voyageData = this.store.selectSignal(flowFeature.selectVoyage);
  activeFlowVoyages = this.store.selectSignal(flowFeature.selectFlowVoyages);
  moveDataLoading = this.store.selectSignal(flowFeature.selectPageLoading);
  isVisibleMove = this.store.selectSignal(
    voyageCargoPopupsFeature.selectIsVisibleMove
  );
  isOpenVendorDialog = false;
  isOpenCancelDialog = false;
  isOpenDeleteDialog = false;
  pageIndex = 0;

  isBumpDisabled = computed(() => {
    if (!this.selectedItems().length) {
      return true;
    }

    const hasIdItems = this.selectedItems().filter(this.hasId).length > 0;

    if (!hasIdItems) {
      return true;
    }

    if (this.voyageData()?.voyageStatus === VoyageStatus.Draft) {
      return true;
    }

    if (
      this.selectedItems().some(
        (item) =>
          item.status === VoyageCargoStatus.Draft ||
          item.isCancelled ||
          item.isBumped
      )
    ) {
      return true;
    }

    return false;
  });

  hasId = () => (item: CargoListItem) => !!item.voyageCargoId;

  filterForCanceled = () => (item: CargoListItem) =>
    !!item.voyageCargoId &&
    !item.isCancelled &&
    !item.hasLifts &&
    !item.passedInspection &&
    !item.arrivalTime &&
    !item.customsCleared &&
    !item.dispatched;

  filterForDeleted = () => (item: CargoListItem) =>
    !!item.voyageCargoId &&
    !item.hasLifts &&
    !item.passedInspection &&
    !item.arrivalTime &&
    !item.customsCleared &&
    !item.dispatched;

  isMoveDisable = computed(() => {
    if (
      this.voyageData()?.voyageStatus !== VoyageStatus.Released &&
      this.voyageData()?.voyageStatus !== VoyageStatus.Submitted
    ) {
      return true;
    }

    const createdItems = this.selectedItems().filter(this.hasId);

    if (!createdItems.length) {
      return true;
    }

    const isInValid = createdItems.some(
      (item) =>
        item.isBumped ||
        item.isCancelled ||
        item.hasLifts ||
        item.isMoved ||
        item.completedLift ||
        item.status === VoyageCargoStatus.Draft
    );

    if (isInValid) {
      return true;
    }

    return false;
  });

  isCancelDisable = computed(() => {
    const createdItems = this.selectedItems().filter(this.hasId());

    if (!createdItems.length) {
      return true;
    }

    const isInValid = createdItems.every((item) => item.isCancelled);

    if (isInValid) {
      return true;
    }

    return false;
  });

  confirmCancel() {
    this.selectedItems()
      .filter(this.filterForCanceled())
      .forEach((cargo) => {
        this.store.dispatch(
          CargoListEditActions.update_Voyage_Cargo({
            cargo: { ...cargo, isCancelled: true, isBumped: false },
          })
        );
      });

    this.isOpenCancelDialog = false;
  }

  confirmDelete() {
    this.store.dispatch(
      CargoListEditActions.delete_Voyage_Cargo({
        voyageCargoIds: this.selectedItems()
          .filter(this.filterForDeleted())
          .map((cargoItem: CargoListItem) => cargoItem.voyageCargoId),
        voyageId: this.voyageData()!.voyageId,
      })
    );

    this.isOpenDeleteDialog = false;
  }

  submitBump() {
    this.selectedItems()
      .filter((cargoItem: CargoListItem) => !!cargoItem.voyageCargoId)
      .forEach((cargo) => {
        this.store.dispatch(
          CargoListEditActions.bump_Voyage_Cargo({
            cargo: { ...cargo, isBumped: true, isCancelled: false },
          })
        );
      });
  }

  bump(event?: Event) {
    this.confirmationService.confirm({
      target: event?.target as EventTarget,
      message: 'Please confirm bumping of these cargo(s)',
      header: 'Bump Cargo(s)',
      rejectButtonStyleClass: 'btn-tertiary',
      acceptButtonStyleClass: 'btn-negative-primary',
      acceptIcon: 'none',
      rejectIcon: 'none',
      acceptLabel: 'Submit',
      rejectLabel: 'Cancel',
      accept: () => {
        this.submitBump();
      },
    });
  }

  copy() {
    this.store.dispatch(
      CargoListEditActions.create_Voyage_Cargo({
        cargo: { ...this.selectedItems()[0], status: VoyageCargoStatus.Draft },
      })
    );
  }

  reinstate() {
    this.selectedItems()
      .filter((cargoItem: CargoListItem) => !!cargoItem.voyageCargoId)
      .forEach((cargo) => {
        const cargoCopy = this.utilityService.deepCopy(cargo);

        if (cargoCopy.collectDate) {
          const collectDate = new Date(cargoCopy.collectDate);
          cargoCopy.collectDate = collectDate.toISOString().split('T')[0]; // Format as YYYY-MM-DD
        }

        this.store.dispatch(
          CargoListEditActions.update_Voyage_Cargo({
            cargo: {
              ...cargoCopy,
              isBumped: false,
              isCancelled: false,
              isMoved: false,
            },
          })
        );
      });
  }

  move() {
    this.store.dispatch(
      VoyageCargoPopupsActions.visible_Change_Move({
        isVisible: true,
      })
    );
  }

  dangerousGoods() {
    this.store.dispatch(
      DangerousGoodsPopupActions.change_visibility({
        visible: true,
        cargoId: this.selectedItems()[0].voyageCargoId,
        popupType: DangerousGoodsPopupType.VoyageCargo,
      })
    );
  }

  showVendorAddress() {
    this.isOpenVendorDialog = !this.isOpenVendorDialog;
  }

  onReinstate(event: Event) {
    this.confirmationService.confirm({
      target: event.target as EventTarget,
      message: 'Are you sure you want to reinstate these line (s)?',
      header: 'Reinstate',
      rejectButtonStyleClass: 'btn-tertiary',
      acceptButtonStyleClass: 'btn-primary',
      acceptIcon: 'none',
      rejectIcon: 'none',
      acceptLabel: 'Reinstate',
      rejectLabel: 'Cancel',
      accept: () => {
        this.reinstate();
      },
    });
  }
}
