import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import {
  Date<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  NgI<PERSON>,
  Ng<PERSON><PERSON>,
  Ng<PERSON><PERSON>Case,
  NgSwitchDefault,
} from '@angular/common';
import { FormsModule } from '@angular/forms';

import { Store } from '@ngrx/store';

import { DialogModule } from 'primeng/dialog';
import { TableModule } from 'primeng/table';
import { InputTextModule } from 'primeng/inputtext';

import { assetsAllocateFeature } from '../../../store/features/assets.feature';
import { AssetsAllocateActions } from '../../../store/actions/assets.actions';
import { AssetsHistoryTableFields } from '../../../shared/enums/assets-history-table-fields.enum';
import { InitializeAssetsHistoryTable } from '../../../shared/tables/assets-history.table';
import { CustomChipComponent } from 'libs/components/src/lib/components/custom-chip/custom-chip.component';

@Component({
  standalone: true,
  selector: 'allocate-assets-history',
  templateUrl: './asset-history.component.html',
  imports: [
    DialogModule,
    TableModule,
    InputTextModule,
    NgIf,
    NgFor,
    NgSwitch,
    NgSwitchCase,
    NgSwitchDefault,
    FormsModule,
    CustomChipComponent,
    DatePipe,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AssetsHistoryComponent {
  private readonly store = inject(Store);
  assetsHistory = this.store.selectSignal(
    assetsAllocateFeature.selectAssetsHistory
  );
  isVisible = this.store.selectSignal(
    assetsAllocateFeature.selectIsVisibleHistory
  );
  listColumns = InitializeAssetsHistoryTable();
  tableFields = AssetsHistoryTableFields;
  tableWidth = 350;
  searchValue = '';

  hideDialog() {
    this.store.dispatch(
      AssetsAllocateActions.change_visibility_history({
        visible: false,
        assetsHistory: [],
      })
    );
  }
}
