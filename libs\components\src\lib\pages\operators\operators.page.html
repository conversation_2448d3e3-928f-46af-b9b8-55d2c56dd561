<div class="d-flex justify-content-between mb-40">
  <b class="fs-24">Operators</b>

  <div class="d-flex gap-20">
    <span class="p-input-icon-left">
      <em class="pi pi-search"></em>
      <input
        type="text"
        pInputText
        [(ngModel)]="searchValue"
        placeholder="Search..."
        (input)="table.filterGlobal(searchValue,'contains')"
      />
    </span>

    <button type="button" class="btn-primary" (click)="create()">
      Create
    </button>
  </div>
</div>

<p-table
  #table
  [columns]="listColumns"
  [value]="operators"
  [scrollable]="true"
  scrollHeight="calc(100vh - 273px)"
  [rowsPerPageOptions]="[10, 25, 50]"
  [paginator]="true"
  [rows]="10"
  [filterDelay]="0"
  [globalFilterFields]="[
    tableFields.name,
    tableFields.createdByName,
    tableFields.updatedByName,
 ]"
>
  <ng-template pTemplate="header" let-columns>
    <tr>
      <th
        *ngFor="let column of columns"
        [style.min-width.px]="column.width"
        [style.width.%]="(column.width / tableWidth) * 100"
        [pSortableColumn]="column.field"
        [pSortableColumnDisabled]="!column.sortable"
      >
        <span>{{ column.name }}</span>
        <p-sortIcon *ngIf="column.sortable" [field]="column.field" />
      </th>
    </tr>
  </ng-template>
  <ng-template
    let-index="rowIndex"
    pTemplate="body"
    let-row
    let-columns="columns"
  >
    <tr>
      <ng-container *ngFor="let column of columns">
        <ng-container [ngSwitch]="column.field">
          <td *ngSwitchCase="tableFields.isActive">
            <span class="d-flex align-items-center">
              <i
                class="pi fs-16"
                [ngClass]="row.isActive ? 'pi-check' : ''"
                [style.color]="row.isActive ? 'green' : ''"
              >
              </i>
            </span>
          </td>
          <td *ngSwitchCase="tableFields.locationNames">
            <div class="d-flex gap-5 flex-wrap">
              <lha-custom-chip
                *ngFor="let item of row.locationNames"
                cssClass="draft"
                [size]="null"
                [text]="item"
              />
            </div>
          </td>
          <td *ngSwitchCase="tableFields.assetNames">
            <div class="d-flex gap-5 flex-wrap">
              <lha-custom-chip
                *ngFor="let item of row.assetNames"
                cssClass="draft"
                [size]="null"
                [text]="item"
              />
            </div>
          </td>
          <td *ngSwitchCase="tableFields.createdByName">
            <div class="d-flex flex-direction-column gap-4">
              <span>{{row.createdByName}}</span>
              <span>{{row.createdDate | date : 'dd/MM/yyyy HH:mm'}}</span>
            </div>
          </td>
          <td *ngSwitchCase="tableFields.updatedByName">
            <div class="d-flex flex-direction-column gap-4">
              <span>{{row.updatedByName}}</span>
              <span>{{row.updatedDate | date : 'dd/MM/yyyy HH:mm'}}</span>
            </div>
          </td>
          <td *ngSwitchCase="tableFields.actions">
            <div class="d-flex gap-8 flex-wrap">
              <button type="button" class="btn-icon-only" (click)="edit(row)">
                <em class="pi pi-pencil"></em>
              </button>
              <button
                type="button"
                class="btn-icon-only"
                (click)="openHistoryDialog(row)"
              >
                <em class="pi pi-link"></em>
              </button>
            </div>
          </td>
          <td *ngSwitchDefault>{{ row[column.field] }}</td>
        </ng-container>
      </ng-container>
    </tr>
  </ng-template>
  <ng-template pTemplate="emptymessage">
    <tr>
      <td colspan="10" style="text-align: center">No results found</td>
    </tr>
  </ng-template>
</p-table>

<md-operators-add-edit />
<md-operator-history />
