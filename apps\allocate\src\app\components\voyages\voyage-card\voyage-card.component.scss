.voyage_card {
  overflow: hidden;
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  width: 100%;
  transition: 0.3s all ease-in-out;
  height: 100%;
  border: 1px solid var(--theme-white-tertiary);
  &:hover {
    box-shadow: 0 5px 15px rgba(#000000, 0.2);
    .voyage_card__arr {
      transform: translateX(5px);
    }
  }
  &__img {
    height: 105px;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    img {
      object-fit: cover;
      width: 100%;
      height: 100%;
      cursor: pointer;
    }
    mat-icon {
      font-size: 56px;
      width: 56px;
      height: 56px;
    }
  }
  &__des {
    display: flex;
    flex-wrap: wrap;
    column-gap: 2%;
    row-gap: 15px;
    padding: 15px 10px;
    position: relative;
    border-radius: 0 0 12px 12px;
    border-top: 1px solid var(--theme-white-tertiary);
    flex-grow: 1;
  }
  &__box {
    width: 49%;
    font-size: 14px;
  }
  &__title {
    font-weight: bold;
    display: block;
    margin-bottom: 5px;
  }
  &__text {
    display: block;
    color: #4f6566;
    word-break: break-all;
  }
  &__arr {
    position: absolute;
    right: 9%;
    bottom: 13%;
    transition: 0.2s all ease-in-out;
  }
  &__issue {
    position: absolute;
    right: 9%;
    top: 13px;
    transition: 0.2s all ease-in-out;
  }
}
