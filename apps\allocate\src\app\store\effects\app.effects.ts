import { inject } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { switchMap, of } from 'rxjs';
import { AppActions } from '../actions/app.actions';
import { CurrentUserActions } from 'libs/auth/src/lib/store/current-user/current-user.actions';
import { LocationActions } from 'libs/services/src/lib/services/maintenance/store/actions/locations.actions';
import { ClientLocationActions } from 'libs/services/src/lib/services/client-locations/store/actions/client-locations.actions';
import { SettingActions } from 'libs/services/src/lib/services/settings/shared/store/actions/setting.actions';

export const applicationInit$ = createEffect(
  (actions = inject(Actions)) => {
    return actions.pipe(
      ofType(AppActions.application_init),
      switchMap(() =>
        of(
          CurrentUserActions.load_Current_User(),
          LocationActions.load_Locations(),
          ClientLocationActions.load_User_Client_Locations(),
          SettingActions.load_Settings()
        )
      )
    );
  },
  { functional: true }
);
