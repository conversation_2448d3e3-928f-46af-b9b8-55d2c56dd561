<div
  *ngIf="bulksState()"
  cdkDrag
  cdkDragRootElement=".cdk-overlay-pane"
  cdkDragHandle
  cdkDragBoundary=".cdk-overlay-container"
>
  <div mat-dialog-title>
    <h4>{{ isAdd ? 'Add ' : 'Edit ' }} Bulk Transaction</h4>
  </div>
  <mat-dialog-content *lhaLoading="bulksState().loading.createEdit">
    <form [formGroup]="form" (ngSubmit)="saveBulk()">
      <div class="form__block">
        <div class="form__box">
          <mat-form-field appearance="outline" hideRequiredMarker="true">
            <mat-label>Bulk Transaction Number</mat-label>
            <input matInput formControlName="bulkTransactionNumber" />
            <mat-error
              *ngIf="form.controls.bulkTransactionNumber.hasError('required')"
            >
              Bulk Transaction Number is required.
            </mat-error>
          </mat-form-field>
        </div>
        <div class="form__box">
          <mat-form-field appearance="outline" hideRequiredMarker="true">
            <mat-label>Select Transaction Type</mat-label>
            <mat-select formControlName="transactionType">
              <mat-option
                *ngFor="let item of bulksState().transactionTypesList"
                [value]="item.value"
              >
                {{ item.description }}
              </mat-option>
            </mat-select>
            <mat-error
              *ngIf="form.controls.transactionType.hasError('required')"
            >
              Transaction Type is required.
            </mat-error>
          </mat-form-field>
        </div>
        <div class="form__box">
          <lha-single-select
            [options]="bulkTypes()"
            formControlName="bulkTypeId"
            bindValue="bulkTypeId"
            placeholder="Select Bulk Type"
          />
          <div
            *ngIf="
              form.controls.bulkTypeId.invalid &&
              form.controls.bulkTypeId.touched
            "
          >
            <mat-error *ngIf="form.controls.bulkTypeId.hasError('required')">
              Bulk Type is required.
            </mat-error>
          </div>
        </div>
        <div class="form__box">
          <mat-form-field appearance="outline" hideRequiredMarker="true">
            <mat-label>Quantity</mat-label>
            <input
              matInput
              formControlName="quantity"
              type="number"
              [decimal]="true"
            />
            <mat-error *ngIf="form.controls.quantity.hasError('required')">
              Quantity is required.
            </mat-error>
            <mat-error *ngIf="form.controls.quantity.hasError('greaterThan')">
              Quantity must be a number greater than 0.
            </mat-error>
            <mat-error *ngIf="form.controls.quantity.hasError('decimalPoint')"
              >Decimal point should be less than 4</mat-error
            >
          </mat-form-field>
        </div>
        <div class="form__box">
          <lha-single-select
            [options]="bulksState().tankTypesList"
            formControlName="tankTypeId"
            bindValue="tankTypeId"
            placeholder="Select Tank Type"
          />
          <div
            *ngIf="
              form.controls.tankTypeId.invalid &&
              form.controls.tankTypeId.touched
            "
          >
            <mat-error *ngIf="form.controls.tankTypeId.hasError('required')">
              Tank Type is required.
            </mat-error>
          </div>
        </div>
        <div class="form__box">
          <mat-form-field appearance="outline" hideRequiredMarker="true">
            <mat-label>Del. Ticket</mat-label>
            <input matInput formControlName="delTicket" type="text" />
          </mat-form-field>
        </div>
        <div
          class="form__box"
          *ngIf="
            dep.trCompletedDateTime.includes(
              form.controls.transactionType.value ?? ''
            )
          "
        >
          <mat-form-field appearance="outline" hideRequiredMarker="true">
            <mat-label>Completed Date</mat-label>
            <input
              matInput
              [ngxMatDatetimePicker]="completedDateTime"
              formControlName="completedDateTime"
            />
            <mat-datepicker-toggle
              matIconSuffix
              [for]="$any(completedDateTime)"
            ></mat-datepicker-toggle>
            <ngx-mat-datetime-picker
              #completedDateTime
            ></ngx-mat-datetime-picker>
            <mat-error
              *ngIf="form.controls.completedDateTime.hasError('required')"
              >Date is required</mat-error
            >
          </mat-form-field>
        </div>
        <div
          class="form__box"
          *ngIf="form.controls.transactionType.value === 'LOA'"
        >
          <mat-form-field appearance="outline" hideRequiredMarker="true">
            <mat-label>Price</mat-label>
            <input
              matInput
              formControlName="dayRatePrice"
              type="number"
              [decimal]="true"
            />
            <span class="currency-prefix" matPrefix>{{
              (appSettings$ | async)?.currency ?? ''
            }}</span>
            <mat-error *ngIf="form.controls.dayRatePrice.hasError('required')">
              Price is required.
            </mat-error>
            <mat-error *ngIf="form.controls.dayRatePrice.hasError('min')">
              Price must be a number greater than or equal to 0.
            </mat-error>
            <mat-error
              *ngIf="form.controls.dayRatePrice.hasError('decimalPoint')"
              >Decimal point should be less than 2</mat-error
            >
          </mat-form-field>
        </div>
        <div
          class="form__box"
          *ngIf="
            dep.trLocations.includes(form.controls.transactionType.value ?? '')
          "
        >
          <lha-single-select
            [options]="filteredAssetsList()"
            formControlName="assetId"
            bindValue="assetId"
            placeholder="Asset"
          />
          <div
            *ngIf="
              form.controls.assetId.invalid && form.controls.assetId.touched
            "
          >
            <mat-error *ngIf="form.controls.assetId.hasError('required')">
              Asset is required.
            </mat-error>
          </div>
        </div>
        <div
          class="form__box"
          *ngIf="
            dep.trOperator.includes(
              form.controls.transactionType.value ?? ''
            ) && assetType === 'POR'
          "
        >
          <lha-single-select
            [options]="bulksState().operators"
            formControlName="clientId"
            bindValue="clientId"
            placeholder="Operator"
          />
          <div
            *ngIf="
              form.controls.clientId.invalid && form.controls.clientId.touched
            "
          >
            <mat-error *ngIf="form.controls.clientId.hasError('required')">
              Operator is required.
            </mat-error>
          </div>
        </div>
      </div>
      <mat-dialog-actions align="end">
        <button mat-raised-button color="primary" type="submit">
          {{ isAdd ? 'Add ' : 'Save ' }}
        </button>
        <button
          mat-raised-button
          color="warn"
          type="button"
          mat-dialog-close=""
        >
          Cancel
        </button>
      </mat-dialog-actions>
    </form>
  </mat-dialog-content>
</div>
