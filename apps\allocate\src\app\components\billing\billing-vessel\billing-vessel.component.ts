import {
  Component,
  computed,
  DestroyRef,
  effect,
  inject,
  Input,
  OnInit,
} from '@angular/core';
import { Store } from '@ngrx/store';
import {
  ActivatedRoute,
  NavigationEnd,
  Router,
  RouterOutlet,
} from '@angular/router';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

import { MenuItem } from 'primeng/api';
import { PanelMenuModule } from 'primeng/panelmenu';

import { billingVesselsFeature } from '../../../store/features/billing-vessel.features';

@Component({
  selector: 'lha-billing-vessel',
  standalone: true,
  imports: [PanelMenuModule, RouterOutlet],
  templateUrl: './billing-vessel.component.html',
})
export class BillingVesselComponent implements OnInit {
  @Input() billingPeriodId = '';
  private readonly store = inject(Store);
  private readonly destroyRef = inject(DestroyRef);
  private readonly activatedRoute = inject(ActivatedRoute);
  private readonly router = inject(Router);

  vessels = this.store.selectSignal(billingVesselsFeature.selectBillingVessels);

  navItems = computed<MenuItem[]>(() => {
    return this.vessels().map((vessel) => {
      return {
        label: vessel.name,
        routerLink: `${vessel.vesselId}`,
        permissions: [],
      };
    });
  });

  constructor() {
    effect(
      () => {
        if (
          !this.activatedRoute.children[0]?.snapshot.params['vesselId'] &&
          this.vessels().length
        ) {
          this.router.navigate([this.vessels()[0].vesselId], {
            relativeTo: this.activatedRoute,
          });
        }
      },
      {
        allowSignalWrites: true,
      }
    );
  }

  ngOnInit(): void {
    this.router.events
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((event) => {
        if (event instanceof NavigationEnd) {
          if (
            !this.activatedRoute.children[0]?.snapshot.params['vesselId'] &&
            this.vessels().length
          ) {
            this.router.navigate([this.vessels()[0].vesselId], {
              relativeTo: this.activatedRoute,
            });
          }
        }
      });
  }
}
