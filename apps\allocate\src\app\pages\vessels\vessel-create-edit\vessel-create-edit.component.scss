mat-error {
  font-size: 11px;
}
.form {
  &__block {
    row-gap: 10px;
    display: grid;
    grid-template-rows: repeat(10, 1fr);
    grid-auto-flow: column;
    grid-template-columns: 40% 55%;
    grid-column-gap: 5%;
  }
  &__action {
    margin-top: 15px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    button,
    .mdc-button {
      margin-left: 15px;
    }
  }
  &__box {
    display: flex;
    align-items: center;
    width: 100%;
    mat-label {
      width: 190px;
      margin-top: -20px;
      font-weight: 500;
      flex-shrink: 0;
      &.mat-label--plain {
        margin-top: 0;
      }
    }
    &_left,
    &_right {
      display: flex;
      align-items: center;
      width: 60%;
    }
    &_right {
      width: 40%;
      padding-left: 20px;
      mat-label {
        width: 80px;
      }
    }
  }
}
.vessel {
  &__img {
    position: relative;
    &_error {
      position: absolute;
      text-align: center;
      top: 90%;
      left: 50%;
      transform: translateX(-50%);
      z-index: 1;
      width: 150px;
    }
    mat-error {
      color: var(--theme-warn);
      font-size: 11px;
    }
  }
}
mat-slide-toggle {
  margin-right: 15px;
}
.form__action {
  margin-bottom: 15px;
}
@media (max-width: 1300px) {
  .form {
    &__box {
      mat-label {
        font-size: 14px;
        width: 150px;
      }
      &_right {
        mat-label {
          width: 60px;
        }
      }
    }
  }
}
@media (max-width: 1000px) {
  .form {
    &__block {
      display: block;
    }
  }
}
