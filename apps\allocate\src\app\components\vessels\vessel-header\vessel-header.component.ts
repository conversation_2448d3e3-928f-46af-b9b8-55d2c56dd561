import { Component, inject } from '@angular/core';
import { MatCardModule } from '@angular/material/card';
import { Store } from '@ngrx/store';
import { AsyncPipe } from '@angular/common';
import {vesselsFeature} from "libs/services/src/lib/services/vessels/store/features/vessels.features";
import { HeaderNavComponent } from 'libs/components/src/lib/components/header-nav/header-nav.component';

@Component({
  selector: 'lha-vessel-header',
  standalone: true,
  imports: [HeaderNavComponent, MatCardModule, AsyncPipe],
  templateUrl: './vessel-header.component.html',
  styleUrls: ['./vessel-header.component.scss'],
})
export class VesselHeaderComponent {
  store = inject(Store);
  vessel$ = this.store.select(vesselsFeature.selectVessel);
  categoriesItems = [
    {
      title: 'Details',
      link: 'details',
      permissions: [],
    },
    {
      title: 'Tanks',
      link: 'tanks',
      permissions: [],
    },
    {
      title: 'Hire Statement',
      link: 'hire-statement',
      permissions: [],
    },
  ];
}
