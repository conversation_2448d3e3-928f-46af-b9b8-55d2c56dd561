import { Actions, concatLatestFrom, createEffect, ofType } from '@ngrx/effects';
import { inject } from '@angular/core';
import { catchError, map, mergeMap, of } from 'rxjs';
import { HttpErrorResponse } from '@angular/common/http';
import { VesselTankActions } from '../actions/vessel-tank.actions';
import { VesselTank } from '../../interfaces/vessel-tank.interface';
import { Store } from '@ngrx/store';
import { vesselTanksFeature } from '../features';
import { MatDialog } from '@angular/material/dialog';
import { filter, tap } from 'rxjs/operators';
import { FileService } from '../../../file.service';
import { VesselTankAddEditComponent } from '../../../../../../../../apps/allocate/src/app/components/vessels/vessel-tank-add-edit/vessel-tank-add-edit.component';
import { VesselTankService } from '../../vessel-tank.service';
import { UtilityService } from '../../../utility.service';
import { ActivatedRoute } from '@angular/router';
import { TankType } from '../../../maintenance/interfaces/tank-type.interface';
import { BulkType } from '../../../maintenance/interfaces/bulk-type.interface';
import { DialogOptions } from '../../../config/dialog-options';

export const loadVesselTanks = createEffect(
  (
    actions = inject(Actions),
    store = inject(Store),
    utilityService = inject(UtilityService),
    vesselTankService = inject(VesselTankService),
    activatedRoute = inject(ActivatedRoute)
  ) => {
    return actions.pipe(
      ofType(
        VesselTankActions.load_Vessel_Tanks,
        VesselTankActions.load_Vessel_Tanks_Lists,
        VesselTankActions.update_Vessel_Tank_Queries,
        VesselTankActions.remove_Vessel_Tank_Success,
        VesselTankActions.add_Vessel_Tank_Success,
        VesselTankActions.edit_Vessel_Tank_Success
      ),
      filter(
        () =>
          !!utilityService.getParamsFromRoute(activatedRoute.snapshot.root)[
            'id'
          ]
      ),
      concatLatestFrom(() => store.select(vesselTanksFeature.selectQuery)),
      map(([, res]) => {
        return {
          query: res,
          vesselId: utilityService.getParamsFromRoute(
            activatedRoute.snapshot.root
          )['id'],
        };
      }),
      mergeMap((action) =>
        vesselTankService
          .loadTankListByVesselId(action.vesselId, action.query)
          .pipe(
            map((res: VesselTank[]) =>
              VesselTankActions.load_Vessel_Tanks_Success({ vesselTanks: res })
            ),
            catchError((error: HttpErrorResponse) =>
              of(VesselTankActions.load_Vessel_Tanks_Failure({ error }))
            )
          )
      )
    );
  },
  {
    functional: true,
  }
);

export const loadTankTypes = createEffect(
  (
    actions = inject(Actions),
    store = inject(Store),
    vesselTankService = inject(VesselTankService)
  ) => {
    return actions.pipe(
      ofType(VesselTankActions.load_Vessel_Tanks_Lists),
      mergeMap(() =>
        vesselTankService.loadTankTypes().pipe(
          map((res: TankType[]) =>
            VesselTankActions.load_Vessel_Tank_Types_Success({ tankTypes: res })
          ),
          catchError((error: HttpErrorResponse) =>
            of(VesselTankActions.load_Vessel_Tank_Types_Failure({ error }))
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const loadBulkTypes = createEffect(
  (
    actions = inject(Actions),
    store = inject(Store),
    vesselTankService = inject(VesselTankService)
  ) => {
    return actions.pipe(
      ofType(VesselTankActions.load_Vessel_Tanks_Lists),
      mergeMap((action) =>
        vesselTankService.loadBulkTypes().pipe(
          map((res: BulkType[]) =>
            VesselTankActions.load_Vessel_Bulk_Types_Success({ bulkTypes: res })
          ),
          catchError((error: HttpErrorResponse) =>
            of(VesselTankActions.load_Vessel_Bulk_Types_Failure({ error }))
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const loadVesselTanksList = createEffect(
  (
    actions = inject(Actions),
    store = inject(Store),
    activatedRoute = inject(ActivatedRoute),
    utilityService = inject(UtilityService),
    vesselTankService = inject(VesselTankService)
  ) => {
    return actions.pipe(
      ofType(
        VesselTankActions.load_Vessel_Tanks,
        VesselTankActions.update_Vessel_Tank_Queries,
        VesselTankActions.remove_Vessel_Tank_Success,
        VesselTankActions.add_Vessel_Tank_Success,
        VesselTankActions.edit_Vessel_Tank_Success
      ),
      filter(
        () =>
          !utilityService.getParamsFromRoute(activatedRoute.snapshot.root)['id']
      ),
      concatLatestFrom(() => store.select(vesselTanksFeature.selectQuery)),
      map(([, res]) => res),
      mergeMap((action) =>
        vesselTankService.loadTankList(action).pipe(
          map((res: VesselTank[]) =>
            VesselTankActions.load_Vessel_Tanks_Success({ vesselTanks: res })
          ),
          catchError((error: HttpErrorResponse) =>
            of(VesselTankActions.load_Vessel_Tanks_Failure({ error }))
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const removeVesselTank = createEffect(
  (
    actions = inject(Actions),
    vesselTankService = inject(VesselTankService)
  ) => {
    return actions.pipe(
      ofType(VesselTankActions.remove_Vessel_Tank),
      mergeMap((action) =>
        vesselTankService.removeTank(action.id).pipe(
          map((res: VesselTank) =>
            VesselTankActions.remove_Vessel_Tank_Success({
              vesselTank: res,
              successMessage: 'VesselTank remove successfully!',
            })
          ),
          catchError((error: HttpErrorResponse) =>
            of(VesselTankActions.remove_Vessel_Tank_Failure({ error }))
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const addVesselTank = createEffect(
  (
    actions = inject(Actions),
    vesselTankService = inject(VesselTankService)
  ) => {
    return actions.pipe(
      ofType(VesselTankActions.add_Vessel_Tank),
      mergeMap((action) =>
        vesselTankService.addTank(action.vesselTank).pipe(
          map((res: VesselTank) =>
            VesselTankActions.add_Vessel_Tank_Success({
              vesselTank: res,
              successMessage: 'Vessel Tank added successfully!',
            })
          ),
          catchError((error: HttpErrorResponse) =>
            of(VesselTankActions.add_Vessel_Tank_Failure({ error: error }))
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const editVesselTank = createEffect(
  (
    actions = inject(Actions),
    vesselTankService = inject(VesselTankService)
  ) => {
    return actions.pipe(
      ofType(VesselTankActions.edit_Vessel_Tank),
      mergeMap((action) =>
        vesselTankService.editTank(action.vesselTankId, action.vesselTank).pipe(
          map((res: VesselTank) =>
            VesselTankActions.edit_Vessel_Tank_Success({
              vesselTank: res,
              successMessage: 'Vessel Tank edited successfully!',
            })
          ),
          catchError((error: HttpErrorResponse) =>
            of(VesselTankActions.edit_Vessel_Tank_Failure({ error: error }))
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const exportVesselTanks = createEffect(
  (
    actions = inject(Actions),
    vesselTankService = inject(VesselTankService),
    fileService = inject(FileService),
    utilityService = inject(UtilityService),
    activatedRoute = inject(ActivatedRoute)
  ) => {
    return actions.pipe(
      ofType(VesselTankActions.export_Vessel_Tanks),
      map(() =>
        utilityService.getParamsFromRoute(activatedRoute.snapshot.root)
      ),
      mergeMap((params) =>
        vesselTankService.exportVesselTanks(params['id']).pipe(
          map((res: ArrayBuffer) => {
            fileService.downloadFile(res, 'Vessel Tanks');
            return VesselTankActions.export_Vessel_Tanks_Success();
          }),
          catchError((error: HttpErrorResponse) =>
            of(VesselTankActions.export_Vessel_Tanks_Failure({ error }))
          )
        )
      )
    );
  },
  {
    // You can omit this part if not needed
    // This indicates that the effect is a pure function and can be tree-shaken during optimization
    functional: true,
  }
);

export const openVesselTankDialog = createEffect(
  (
    actions = inject(Actions),
    dialog = inject(MatDialog),
    dialogOptions = inject(DialogOptions)
  ) => {
    return actions.pipe(
      ofType(VesselTankActions.open_Vessel_Tank_Dialog),
      tap((action) => {
        dialog.open(VesselTankAddEditComponent, {
          ...dialogOptions,
          data: {
            vesselTank: action.vesselTank,
          },
        });
      })
    );
  },
  {
    functional: true,
    dispatch: false,
  }
);
