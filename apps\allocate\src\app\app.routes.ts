import { Routes } from '@angular/router';
import { provideEffects } from '@ngrx/effects';
import { provideState } from '@ngrx/store';

import { userPermissionsGuard } from 'libs/auth/src/lib/guards/permissions.guard';
import {
  activitiesFeature,
  bulkRequestFeature,
  bulksFeature,
  deckUsagesFeature,
  tankManagementsFeature,
  voyagesFeature,
} from 'libs/services/src/lib/services/voyages/store/features';

import * as voyageEffects from 'libs/services/src/lib/services/voyages/store/effects';
import * as vesselEffects from 'libs/services/src/lib/services/vessels/store/effects';
import * as usersEffects from 'libs/services/src/lib/services/maintenance/store/effects';
import * as billingEffects from './store/effects/billing.effects';
import * as billingVesselEffects from './store/effects/billing-vessel.effects';
import * as billingOperatorEffects from './store/effects/billing-operator.effects';

import { usersFeature } from 'libs/services/src/lib/services/maintenance/store/features/users.features';
import { UserRole } from 'libs/auth/src/lib/constants/userRole.enum';
import { vesselTanksFeature } from 'libs/services/src/lib/services/vessels/store/features/vessel-tanks.features';
import { hireStatementFeature } from 'libs/services/src/lib/services/vessels/store/features/hire-statements.feature';
import { vesselsFeature } from 'libs/services/src/lib/services/vessels/store/features/vessels.features';
import { billingOperatorsFeature } from './store/features/billing-operator.features';
import { billingsFeature } from './store/features/billing.features';
import { billingVesselsFeature } from './store/features/billing-vessel.features';

export const appRoutes: Routes = [
  { path: '', redirectTo: 'voyages', pathMatch: 'full' },

  {
    path: 'profile',
    providers: [provideEffects([usersEffects]), provideState(usersFeature)],
    loadComponent: () =>
      import('../app/pages/profile/profile.component').then(
        (c) => c.ProfileComponent
      ),
  },
  {
    path: 'voyages',
    providers: [
      provideState(voyagesFeature),
      provideState(bulksFeature),
      provideState(tankManagementsFeature),
      provideState(activitiesFeature),
      provideState(deckUsagesFeature),
      provideState(bulkRequestFeature),
      provideEffects([voyageEffects]),
    ],
    loadChildren: () =>
      import('./routings/voyages-routes').then((r) => r.VoyagesRoute),
  },
  {
    path: 'billing',
    providers: [
      provideState(billingsFeature),
      provideState(billingOperatorsFeature),
      provideState(billingVesselsFeature),
      provideEffects([
        billingEffects,
        billingVesselEffects,
        billingOperatorEffects,
      ]),
    ],
    loadChildren: () =>
      import('./routings/billing-routes').then((r) => r.BillingRoutes),
  },
  {
    path: 'maintenance',
    canActivate: [userPermissionsGuard([UserRole.Admin, UserRole.SupportUser])],
    loadChildren: () =>
      import('./routings/maintenance.routing').then((r) => r.MaintenanceRoutes),
  },
  {
    path: 'vessels',
    providers: [
      provideState(vesselsFeature),
      provideState(vesselTanksFeature),
      provideState(hireStatementFeature),
      provideEffects([vesselEffects]),
    ],
    loadChildren: () =>
      import('./routings/vessels-routes').then((r) => r.VesselsRoute),
  },
  {
    path: 'settings',
    canActivate: [userPermissionsGuard([UserRole.SupportUser])],
    loadComponent: () =>
      import('./pages/settings/settings.component').then(
        (c) => c.SettingsComponent
      ),
  },

  { path: '**', redirectTo: 'voyages' },
];
