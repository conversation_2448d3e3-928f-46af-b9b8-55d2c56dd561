{"extends": "./tsconfig.json", "compilerOptions": {"outDir": "../../dist/out-tsc", "declaration": true, "declarationMap": true, "inlineSources": true, "types": []}, "exclude": ["src/**/*.spec.ts", "src/test-setup.ts", "jest.config.ts", "src/**/*.test.ts"], "include": ["src/**/*.ts", "../components/src/lib/pages/operators/helper/operator-name-history.interface.ts", "src/lib/services/maintenance/interfaces/operator-state.interface.ts"]}