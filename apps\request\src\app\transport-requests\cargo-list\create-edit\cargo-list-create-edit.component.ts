import {
  ChangeDetectionStrategy,
  Component,
  computed,
  DestroyRef,
  effect,
  inject,
  Input,
  OnInit,
  signal,
} from '@angular/core';
import { NgIf } from '@angular/common';
import { Store } from '@ngrx/store';
import { ButtonModule } from 'primeng/button';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import {
  ActivatedRoute,
  NavigationEnd,
  Router,
  RouterOutlet,
} from '@angular/router';
import { lookaheadFeature } from 'libs/services/src/lib/services/lookahead/store/features';
import { TransportRequestHeaderComponent } from '../../transport-request-header/transport-request-header.component';
import { HeaderNavComponent } from 'libs/components/src/lib/components/header-nav/header-nav.component';
import { NavBar } from 'libs/components/src/lib/interfaces/nav-bar.interface';
import { ConfirmationService, MenuItem } from 'primeng/api';
import { BreadcrumbModule } from 'primeng/breadcrumb';
import { filter } from 'rxjs';
import { Actions } from '@ngrx/effects';
import { transportRequestFeature } from 'libs/services/src/lib/services/transport-requests/store/features/transport-request.feature';
import { transportRequestCargoFeature } from 'libs/services/src/lib/services/transport-requests/store/features/transport-request-cargo.feature';
import { transportRequestBulkCargoFeature } from 'libs/services/src/lib/services/transport-requests/store/features/transport-request-bulk-cargo.feature';
import { TransportRequestCreateEditActions } from 'libs/services/src/lib/services/transport-requests/store/actions/transport-request-create-edit.actions';
import { CardModule } from 'primeng/card';
import { TransportRequestCargoSnapshotActions } from 'libs/services/src/lib/services/transport-requests/store/actions/transport-request-cargo-snapshot.actions';
import { transportRequestCargoSnapshotFeature } from 'libs/services/src/lib/services/transport-requests/store/features';
import { DropdownModule } from 'primeng/dropdown';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { CompareActions } from '../../../store/actions/compare-actions';
import { ProgressSpinnerModule } from 'primeng/progressspinner';

@Component({
  selector: 'transport-request-cargo-list-create-edit',
  templateUrl: './cargo-list-create-edit.component.html',
  styleUrls: ['./cargo-list-create-edit.component.scss'],
  standalone: true,
  imports: [
    NgIf,
    ButtonModule,
    TransportRequestHeaderComponent,
    HeaderNavComponent,
    RouterOutlet,
    DropdownModule,
    ReactiveFormsModule,
    BreadcrumbModule,
    CardModule,
    ProgressSpinnerModule,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CargoListCreateEditComponent implements OnInit {
  private readonly destroyRef = inject(DestroyRef);
  @Input() transportRequestId = '';
  @Input() sailingRequestId = '';
  store = inject(Store);
  router = inject(Router);
  route = inject(ActivatedRoute);
  actions = inject(Actions);
  private readonly confirmationService = inject(ConfirmationService);
  sailingRequest = this.store.selectSignal(
    lookaheadFeature.selectSailingRequest
  );
  transportRequest = this.store.selectSignal(
    transportRequestFeature.selectTransportRequest
  );
  cargoList = this.store.selectSignal(
    transportRequestCargoFeature.selectTransportRequestCargos
  );
  loading = this.store.selectSignal(
    transportRequestCargoSnapshotFeature.selectSubmitRevisionLoading
  );
  bulkList = this.store.selectSignal(
    transportRequestBulkCargoFeature.selectTransportRequestBulkCargoes
  );
  selectedVersionIndex = this.store.selectSignal(
    transportRequestCargoFeature.selectSelectedVersionIndex
  );
  canCreateNewVersion = this.store.selectSignal(
    transportRequestCargoSnapshotFeature.selectCanCreateNewVersion
  );
  transportRequestCargoVersions = this.store.selectSignal(
    transportRequestCargoSnapshotFeature.selectTransportRequestCargoSnapshotVersions
  );
  navItems: NavBar[] = [];
  type = signal('');
  version = new FormControl('');

  breadcrumb = computed<MenuItem[]>(() => {
    const voyageDirection = this.sailingRequest()?.transportRequests.find(
      (tr: any) => tr.transportRequestId === this.transportRequestId
    )?.voyageDirectionName;
    const clusterName = this.sailingRequest()?.clusterName;
    return [
      {
        label: 'Lookahead',
        routerLink: '/sailing-schedule',
        styleClass: 'active',
      },
      {
        label: clusterName
          ? `${clusterName} - ${voyageDirection ?? ''}`
          : voyageDirection ?? '',
        routerLink: `/transport-requests/${this.sailingRequestId}/${
          this.transportRequestId
        }/${this.router.url.split('/')[6]}`,
        styleClass: 'active',
      },
      {
        label: `Edit Cargo List`,
      },
    ];
  });

  constructor() {
    effect(() => {
      if (this.bulkList() && this.cargoList()) this.setNavItems();
    });
  }

  ngOnInit(): void {
    this.store.dispatch(
      TransportRequestCargoSnapshotActions.load_Transport_Request_Cargo_Versions(
        { transportRequestId: this.transportRequestId }
      )
    );
    this.handleNavigation();
    this.updateTransportRequestType();
    this.store.dispatch(
      TransportRequestCreateEditActions.load_Transport_Requests_Create_Edit({
        sailingRequestId: this.sailingRequestId,
        transportRequestId: this.transportRequestId,
      })
    );
    this.store
      .select(
        transportRequestCargoSnapshotFeature.selectTransportRequestCargoSnapshotVersions
      )
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((versions) => {
        this.version.setValue(versions[0]?.transportRequestCargoSnapshotId);
      });
  }

  setNavItems() {
    this.navItems = [
      {
        title: 'Cargo',
        link: `/transport-requests/${this.sailingRequestId}/${this.transportRequestId}/cargo-list/create-edit/cargo`,
        permissions: [],
      },
      {
        title: 'Bulks',
        link: `/transport-requests/${this.sailingRequestId}/${this.transportRequestId}/cargo-list/create-edit/bulks`,
        permissions: [],
        disable: !this.sailingRequest()?.isBulkReq,
      },
      {
        title: 'Material Details',
        link: `/transport-requests/${this.sailingRequestId}/${this.transportRequestId}/cargo-list/create-edit/material-details`,
        permissions: [],
        disable: !(this.bulkList().length || this.cargoList().length),
      },
    ];
  }

  handleNavigateCompare() {
    this.router.navigate(
      [
        'compare',
        this.sailingRequest()?.sailingRequestId,
        this.transportRequestId,
      ],
      {
        queryParams: {
          view: this.route.children[0]?.snapshot.routeConfig?.path,
        },
      }
    );
  }

  disabledCompareBtn = computed(() => {
    return this.transportRequestCargoVersions().length < 2;
  });

  handleNavigation() {
    this.router.events
      .pipe(
        filter((event) => event instanceof NavigationEnd),
        takeUntilDestroyed(this.destroyRef)
      )
      .subscribe(() => {
        this.transportRequestId =
          this.route.snapshot.params['transportRequestId'];
        this.updateTransportRequestType();
      });
  }

  changeTarget(snapshotId: string) {
    this.store.dispatch(
      CompareActions.set_Target_Snapshot_Id({ snapshotId: snapshotId })
    );
  }

  onSubmitNewRevision(event: Event) {
    this.confirmationService.confirm({
      target: event.target as EventTarget,
      message: 'Are you sure you want to submit new revision?',
      header: 'Submit new revision',
      rejectButtonStyleClass: 'btn-tertiary',
      acceptButtonStyleClass: 'btn-primary',
      acceptIcon: 'none',
      rejectIcon: 'none',
      acceptLabel: 'Submit',
      rejectLabel: 'Cancel',
      accept: () => {
        this.submitRevision();
      },
    });
  }

  submitRevision() {
    this.store.dispatch(
      TransportRequestCargoSnapshotActions.submit_Transport_Request_Cargo_Revision(
        {
          transportRequestId: this.route.snapshot.params['transportRequestId'],
        }
      )
    );
  }

  updateTransportRequestType() {
    this.type.set(this.router.url.split('/')[6]);
    switch (this.router.url.split('/')[6]) {
      case 'cargo':
        this.type.set('Cargo');
        break;
      case 'bulks':
        this.type.set('Bulks');
        break;
      case 'material-details':
        this.type.set('Material Details');
        break;
    }
  }
}
