@import 'libs/styles/src/lib/styles/shared-styles.scss';
// html,
body {
  height: 100%;
  margin: 0;
  padding: 0;
}

* {
  font-family: 'Ubuntu', sans-serif;
  /* Firefox */
  /* IE and Edge */
}

::-webkit-scrollbar {
  width: 8px !important;
  height: 8px;
  display: block !important;
}
::-webkit-scrollbar-track {
  background: var(--scroll-track-color);
  border-radius: 8px;
}
::-webkit-scrollbar-thumb {
  background: var(--scroll-color);
  border-radius: 8px;
}

* {
  scrollbar-width: auto !important;
}

main {
  padding: 20px 24px 0px;
}

.voyage-icon {
  mask: url('assets/voyage.svg');
}
.billing-icon {
  mask: url('assets/billing.svg');
}
.maintenance-icon {
  mask: url('assets/maintenance.svg');
}
.vessels-icon {
  mask: url('assets/vessels.svg');
}
.settings-icon {
  mask: url('assets/settings.svg');
}
