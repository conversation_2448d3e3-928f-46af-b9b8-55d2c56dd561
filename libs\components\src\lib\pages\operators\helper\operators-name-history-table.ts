import { ColumnModel } from 'libs/components/src/lib/models/column.model';
import { OperatorsNameHistoryTableFields } from './operators-name-history-table.enum';

export function InitializeOperatorsNameHistoryTable(): ColumnModel[] {
  const operatorNameHistoryListColumns = [
    new ColumnModel(OperatorsNameHistoryTableFields.name, 'Name', 150, {
      sortable: true,
    }),
    new ColumnModel(
      OperatorsNameHistoryTableFields.startDateTime,
      'From',
      150,
      {
        sortable: true,
      }
    ),
    new ColumnModel(OperatorsNameHistoryTableFields.endDateTime, 'To', 150, {
      sortable: true,
    }),
  ];
  return operatorNameHistoryListColumns;
}
