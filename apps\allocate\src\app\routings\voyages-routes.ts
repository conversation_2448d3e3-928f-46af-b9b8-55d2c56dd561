import { Route } from '@angular/router';
import { BulksComponent } from '../components/voyages/bulks/bulks.component';
import { TankManagementComponent } from '../components/voyages/tank-management/tank-management.component';
import { DeckUsageComponent } from '../components/voyages/deck-usage/deck-usage.component';
import { ActivityComponent } from '../components/voyages/activity/activity.component';
import { BulkRequestComponent } from '../components/voyages/bulk-request/bulk-request.component';

export const VoyagesRoute: Route[] = [
  {
    path: '',
    loadComponent: () =>
      import('../pages/voyages/voyages-list/voyages-list.component').then(
        (c) => c.VoyagesListComponent
      ),
  },
  {
    path: ':id',
    loadComponent: () =>
      import('../pages/voyages/voyage-details/voyage-details.component').then(
        (c) => c.VoyageDetailsComponent
      ),
    children: [
      { path: '', redirectTo: 'activity', pathMatch: 'full' },
      { path: 'activity', component: ActivityComponent },
      { path: 'bulks', component: BulksComponent },
      { path: 'tank-management', component: TankManagementComponent },
      { path: 'deck-usage', component: DeckUsageComponent },
      { path: 'bulk-requests', component: BulkRequestComponent },
    ],
  },
];
