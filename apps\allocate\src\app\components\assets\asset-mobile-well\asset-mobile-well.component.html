<p-dialog
  [draggable]="false"
  [closable]="true"
  [modal]="true"
  [visible]="isVisible()"
  (visibleChange)="hideDialog()"
  [style]="{ width: '800px' }"
>
  <ng-template pTemplate="header">
    <div class="header">Add Well to {{ asset()?.name }}</div>
  </ng-template>

  <div class="mt-12 mb-12 d-flex justify-content-end flex-wrap gap-20">
    <span class="p-input-icon-left">
      <em class="pi pi-search search-icon"></em>
      <input
        pInputText
        type="text"
        [(ngModel)]="searchTerm"
        (input)="dt.filterGlobal(searchTerm, 'contains')"
        placeholder="Search..."
      />
    </span>
    <button class="btn-primary" type="button" (click)="addNewRow()">
      Add
    </button>
  </div>

  <p-table
    #dt
    [value]="assetMobileWells"
    dataKey="mobileWellId"
    editMode="row"
    [rows]="10"
    [showCurrentPageReport]="true"
    [rowsPerPageOptions]="[10, 25, 50]"
    [paginator]="true"
    [globalFilterFields]="['name']"
    scrollHeight="550px"
  >
    <ng-template pTemplate="header">
      <tr>
        <th
          [style.min-width.px]="310"
          [style.width.%]="(310 / tableWidth) * 100"
        >
          Well Name
        </th>
        <th
          [style.min-width.px]="150"
          [style.width.%]="(180 / tableWidth) * 100"
        >
          Start Date
        </th>
        <th
          [style.min-width.px]="150"
          [style.width.%]="(180 / tableWidth) * 100"
        >
          End Date
        </th>
        <th
          [style.min-width.px]="72"
          [style.width.%]="(72 / tableWidth) * 100"
        ></th>
      </tr>
    </ng-template>
    <ng-template
      pTemplate="body"
      let-mobile
      let-editing="editing"
      let-ri="rowIndex"
    >
      <tr [pEditableRow]="mobile">
        <td>
          <p-cellEditor>
            <ng-template pTemplate="input">
              <p-dropdown
                [options]="wellAssetList()"
                appendTo="body"
                optionLabel="name"
                optionValue="assetId"
                [(ngModel)]="mobile.wellId"
                [showClear]="true"
                appendTo="body"
                panelStyleClass="new-version-panel"
                styleClass="new-version"
                [required]="true"
              />
            </ng-template>
            <ng-template pTemplate="output">
              {{ mobile.mobileName }}
            </ng-template>
          </p-cellEditor>
        </td>
        <td>
          <p-cellEditor>
            <ng-template pTemplate="input">
              <p-calendar
                [showTime]="true"
                [showSeconds]="false"
                appendTo="body"
                [(ngModel)]="mobile.startDateTime"
                dateFormat="dd/mm/yy"
                (onClear)="mobile.startDateTime = null"
                [maxDate]="mobile.endDateTime"
                [required]="true"
              ></p-calendar>
            </ng-template>
            <ng-template pTemplate="output">
              {{ mobile.startDateTime | date : 'dd/MM/yyyy HH:mm' }}
            </ng-template>
          </p-cellEditor>
        </td>
        <td>
          <p-cellEditor>
            <ng-template pTemplate="input">
              <p-calendar
                [showTime]="true"
                [showSeconds]="false"
                appendTo="body"
                [(ngModel)]="mobile.endDateTime"
                dateFormat="dd/mm/yy"
                (onClear)="mobile.endDateTime = null"
                [minDate]="mobile.startDateTime"
              ></p-calendar>
            </ng-template>
            <ng-template pTemplate="output">
              <ng-container *ngIf="mobile.endDateTime; current">
                {{ mobile.endDateTime | date : 'dd/MM/yyyy HH:mm' }}
              </ng-container>
              <ng-template #current>
                <lha-custom-chip
                  [cssClass]="'off'"
                  [text]="'Current'"
                >
                </lha-custom-chip>
              </ng-template>
            </ng-template>
          </p-cellEditor>
        </td>

        <td>
          <div class="d-flex align-items-center justify-content-between gap-8">
            <button
              *ngIf="!editing"
              type="button"
              pInitEditableRow
              (click)="onRowEditInit(mobile, ri)"
              class="btn-icon-only"
            >
              <em class="pi pi-pencil"></em>
            </button>
            <button
              *ngIf="!editing"
              type="button"
              (click)="remove(mobile)"
              class="btn-icon-only"
            >
              <em class="pi pi-trash"></em>
            </button>
            <button
              *ngIf="editing"
              type="button"
              pSaveEditableRow
              (click)="onRowEditSave(mobile)"
              [disabled]="!(mobile.startDateTime && mobile.wellId)"
              class="btn-icon-only"
            >
              <em class="pi pi-check"></em>
            </button>
            <button
              *ngIf="editing"
              pCancelEditableRow
              (click)="onRowEditCancel(mobile, ri)"
              class="btn-icon-only"
            >
              <em class="pi pi-times"></em>
            </button>
          </div>
        </td>
      </tr>
    </ng-template>
    <ng-template pTemplate="emptymessage">
      <tr>
        <td colspan="4" style="text-align: center">No results found</td>
      </tr>
    </ng-template>
  </p-table>
</p-dialog>
