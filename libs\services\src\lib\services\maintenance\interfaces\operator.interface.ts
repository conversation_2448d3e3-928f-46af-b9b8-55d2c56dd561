import { ReportType } from './report-type.interface';
import { Asset } from './asset.interface';
import { AssetHistory } from './asset-history.interface';
import { OperatorNameHistory } from '../../../../../../components/src/lib/pages/operators/helper/operator-name-history.interface';

export interface Operator {
  clientId: string;
  name: string;
  assetNames: Asset[];
  clientReportTypes: ReportType[];
  reportTypeNames: string[];
  reportTypeIds: string[];
  locationIds: string[];
  locationNames: string[];
  createdByName: string;
  updatedByName: string;
  createByDate: string;
  updatedByDate: string;
  isActive: boolean;
  customsCompliant: boolean;
  clientLogoId: string | null;
  hasImage: boolean;
  vatNumber: string;
  euNumber: string;
  clientAssets: AssetHistory[];
  clientNameHistory: OperatorNameHistory[];
}
