import {
  Directive,
  ElementRef,
  HostListener,
  inject,
  <PERSON><PERSON><PERSON><PERSON>,
  OnInit,
  Renderer2,
} from '@angular/core';
import { Store } from '@ngrx/store';
import { Subject, takeUntil } from 'rxjs';
import { Actions, ofType } from '@ngrx/effects';
import { AuthActions } from 'libs/auth/src/lib/store/auth.actions';

@Directive({
  selector: '[lhaImageAuth], img',
  standalone: true,
})
export class ImageAuthDirective implements OnInit, OnDestroy {
  onload = true;

  @HostListener('error', ['$event']) onError() {
    this.onload = false;
    this.triggerReset();
  }
  @HostListener('load', ['$event']) onLoad() {
    this.onload = true;
  }

  el = inject(ElementRef);
  src = '';
  renderer = inject(Renderer2);
  store = inject(Store);
  actions = inject(Actions);
  unsubscribe: Subject<boolean> = new Subject();

  ngOnInit(): void {
    this.subToActions();
    this.src = this.el.nativeElement.src;
  }

  ngOnDestroy(): void {
    this.unsubscribe.next(true);
    this.unsubscribe.complete();
  }

  private subToActions(): void {
    this.actions
      .pipe(ofType(AuthActions.refresh_Token_Success))
      .pipe(takeUntil(this.unsubscribe))
      .subscribe(() => {
        if (this.onload) {
          return;
        }
        /**
         * That was implemented in this way to display image after getting a valid token
         */
        this.renderer.setAttribute(this.el.nativeElement, 'src', '');
        this.renderer.setAttribute(this.el.nativeElement, 'src', this.src);
      });
  }

  private triggerReset(): void {
    if (this.onload) {
      return;
    }
    this.store.dispatch(AuthActions.refresh_Token());
  }
}
