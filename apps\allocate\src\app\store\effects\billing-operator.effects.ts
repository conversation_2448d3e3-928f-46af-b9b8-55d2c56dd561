import { inject } from '@angular/core';
import { HttpErrorResponse } from '@angular/common/http';

import { Actions, createEffect, ofType } from '@ngrx/effects';

import { catchError, map, mergeMap, of } from 'rxjs';
import { switchMap } from 'rxjs/operators';

import { BillingOperatorsActions } from '../actions/billing-operator.actions';
import { FileService } from 'libs/services/src/lib/services/file.service';
import { BillingOperatorService } from '../../shared/services/billing-operator.service';
import { BillingOperator } from '../../shared/interfaces/billing-operator.interface';
import { BillingOperatorView } from '../../shared/interfaces/billing-operator-view.interface';
import { BillingsActions } from '../actions/billing.actions';

export const loadBillingOperatorList = createEffect(
  (
    actions = inject(Actions),
    billingOperatorService = inject(BillingOperatorService)
  ) => {
    return actions.pipe(
      ofType(BillingOperatorsActions.load_Billing_Operators),
      mergeMap(({ billingPeriodId }) =>
        billingOperatorService.getBillingOperatorList(billingPeriodId).pipe(
          map((res: BillingOperator[]) =>
            BillingOperatorsActions.load_Billing_Operators_Success({
              billingOperators: res,
            })
          ),
          catchError((error: HttpErrorResponse) =>
            of(
              BillingOperatorsActions.load_Billing_Operators_Failure({
                error: error,
              })
            )
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const loadBillingOperatorsAfterCalculation = createEffect(
  (actions = inject(Actions)) => {
    return actions.pipe(
      ofType(
        BillingsActions.calc_Billing_Period_Success,
        BillingsActions.init_Billing_Detail
      ),
      switchMap(({ billingPeriodId }) =>
        of(BillingOperatorsActions.load_Billing_Operators({ billingPeriodId }))
      )
    );
  },
  {
    functional: true,
  }
);

export const loadBillingOperatorVoyageList = createEffect(
  (
    actions = inject(Actions),
    billingOperatorService = inject(BillingOperatorService)
  ) => {
    return actions.pipe(
      ofType(BillingOperatorsActions.load_Billing_Operator_Voyages),
      mergeMap(({ billingPeriodId, clientId }) =>
        billingOperatorService
          .loadClientBillingPeriodListByOperatorId(billingPeriodId, clientId)
          .pipe(
            map((res: BillingOperatorView) =>
              BillingOperatorsActions.load_Billing_Operator_Voyages_Success({
                billingOperatorVoyages: res,
              })
            ),
            catchError((error: HttpErrorResponse) =>
              of(
                BillingOperatorsActions.load_Billing_Operator_Voyages_Failure({
                  error: error,
                })
              )
            )
          )
      )
    );
  },
  {
    functional: true,
  }
);

export const exportBillingOperators = createEffect(
  (
    actions = inject(Actions),
    service = inject(BillingOperatorService),
    fileService = inject(FileService)
  ) => {
    return actions.pipe(
      ofType(BillingOperatorsActions.export_Billing_Operators),

      mergeMap(({ billingPeriodId, clientId }) =>
        service.exportBillingOperator(billingPeriodId, clientId).pipe(
          map((res: ArrayBuffer) => {
            fileService.downloadFile(res, 'Billing Period Operator');
            return BillingOperatorsActions.export_Billing_Operators_Success();
          }),
          catchError((error: HttpErrorResponse) =>
            of(
              BillingOperatorsActions.export_Billing_Operators_Failure({
                error,
              })
            )
          )
        )
      )
    );
  },
  {
    // You can omit this part if not needed
    // This indicates that the effect is a pure function and can be tree-shaken during optimization
    functional: true,
  }
);
