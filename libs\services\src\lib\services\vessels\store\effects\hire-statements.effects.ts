import { Actions, concatLatestFrom, createEffect, ofType } from '@ngrx/effects';
import { inject } from '@angular/core';
import { catchError, map, mergeMap, of } from 'rxjs';
import { HttpErrorResponse } from '@angular/common/http';
import { HireStatementActions } from '../actions/hire-statement.actions';
import { HireStatement } from '../../interfaces/hire-statement.interface';
import { Store } from '@ngrx/store';
import { hireStatementFeature } from '../features';
import { MatDialog } from '@angular/material/dialog';
import { filter, tap } from 'rxjs/operators';
import { FileService } from '../../../file.service';
import { HireStatementAddEditComponent } from '../../../../../../../../apps/allocate/src/app/components/vessels/hire-statement-add-edit/hire-statement-add-edit.component';
import { HireStatementService } from '../../hire-statement.service';
import { UtilityService } from '../../../utility.service';
import { ActivatedRoute, Router } from '@angular/router';
import { HireStatementBulkActions } from '../actions/hire-statement-bulk.actions';
import { HireStatementBulk } from '../../interfaces/hire-statement-bulk.interface';
import { routerNavigationAction } from '@ngrx/router-store';
import { HireStatementBulkAddEditComponent } from '../../../../../../../../apps/allocate/src/app/components/vessels/hire-statement-bulk-add-edit/hire-statement-bulk-add-edit.component';
import { BulkType } from '../../../maintenance/interfaces/bulk-type.interface';
import { DialogOptions } from '../../../config/dialog-options';

export const loadHireStatements = createEffect(
  (
    actions = inject(Actions),
    store = inject(Store),
    utilityService = inject(UtilityService),
    hireStatementService = inject(HireStatementService),
    activatedRoute = inject(ActivatedRoute)
  ) => {
    return actions.pipe(
      ofType(
        HireStatementActions.load_Hire_Statements,
        HireStatementActions.load_Hire_Statements_Lists,
        HireStatementActions.update_Hire_Statement_Queries,
        HireStatementActions.remove_Hire_Statement_Success,
        HireStatementActions.add_Hire_Statement_Success,
        HireStatementActions.edit_Hire_Statement_Success
      ),
      filter(
        () =>
          !!utilityService.getParamsFromRoute(activatedRoute.snapshot.root)[
            'id'
          ]
      ),
      concatLatestFrom(() => store.select(hireStatementFeature.selectQuery)),
      map(([, res]) => {
        return {
          query: res,
          vesselId: utilityService.getParamsFromRoute(
            activatedRoute.snapshot.root
          )['id'],
        };
      }),
      mergeMap((action) =>
        hireStatementService
          .loadHireStatementListByVesselId(action.vesselId, action.query)
          .pipe(
            map((res: HireStatement[]) =>
              HireStatementActions.load_Hire_Statements_Success({
                hireStatements: res,
              })
            ),
            catchError((error: HttpErrorResponse) =>
              of(HireStatementActions.load_Hire_Statements_Failure({ error }))
            )
          )
      )
    );
  },
  {
    functional: true,
  }
);

export const loadHireStatementsList = createEffect(
  (
    actions = inject(Actions),
    store = inject(Store),
    activatedRoute = inject(ActivatedRoute),
    utilityService = inject(UtilityService),
    hireStatementService = inject(HireStatementService)
  ) => {
    return actions.pipe(
      ofType(
        HireStatementActions.load_Hire_Statements,
        HireStatementActions.load_Hire_Statements_Lists,
        HireStatementActions.update_Hire_Statement_Queries,
        HireStatementActions.remove_Hire_Statement_Success,
        HireStatementActions.add_Hire_Statement_Success,
        HireStatementActions.edit_Hire_Statement_Success
      ),
      filter(
        () =>
          !utilityService.getParamsFromRoute(activatedRoute.snapshot.root)['id']
      ),
      concatLatestFrom(() => store.select(hireStatementFeature.selectQuery)),
      map(([, res]) => res),
      mergeMap((action) =>
        hireStatementService.loadHireStatementList(action).pipe(
          map((res: HireStatement[]) =>
            HireStatementActions.load_Hire_Statements_Success({
              hireStatements: res,
            })
          ),
          catchError((error: HttpErrorResponse) =>
            of(HireStatementActions.load_Hire_Statements_Failure({ error }))
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const loadHireStatementBulkTypes = createEffect(
  (
    actions = inject(Actions),
    store = inject(Store),
    hireStatementService = inject(HireStatementService)
  ) => {
    return actions.pipe(
      ofType(HireStatementActions.load_Hire_Statements_Lists),
      mergeMap(() =>
        hireStatementService.loadBulkTypes().pipe(
          map((res: BulkType[]) =>
            HireStatementActions.load_Bulk_Types_Success({ bulkTypes: res })
          ),
          catchError((error: HttpErrorResponse) =>
            of(HireStatementActions.load_Bulk_Types_Failure({ error }))
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const removeHireStatement = createEffect(
  (
    actions = inject(Actions),
    hireStatementService = inject(HireStatementService),
    router = inject(Router)
  ) => {
    return actions.pipe(
      ofType(HireStatementActions.remove_Hire_Statement),
      mergeMap((action) =>
        hireStatementService
          .removeHireStatement(action.hireStatement.hireStatementId)
          .pipe(
            map((res: HireStatement) => {
              router.navigate([
                'vessels',
                action.hireStatement.vesselId,
                'hire-statement',
              ]);
              return HireStatementActions.remove_Hire_Statement_Success({
                hireStatement: res,
                successMessage: 'Hire Statement removed successfully!',
              });
            }),
            catchError((error: HttpErrorResponse) =>
              of(HireStatementActions.remove_Hire_Statement_Failure({ error }))
            )
          )
      )
    );
  },
  {
    functional: true,
  }
);

export const addHireStatement = createEffect(
  (
    actions = inject(Actions),
    hireStatementService = inject(HireStatementService),
    router = inject(Router)
  ) => {
    return actions.pipe(
      ofType(HireStatementActions.add_Hire_Statement),
      mergeMap((action) =>
        hireStatementService.addHireStatement(action.hireStatement).pipe(
          map((res: HireStatement) => {
            router.navigate([
              'vessels',
              action.hireStatement.vesselId,
              'hire-statement',
              res.hireStatementId,
            ]);
            return HireStatementActions.add_Hire_Statement_Success({
              hireStatement: res,
              successMessage: 'Hire statement added successfully!',
            });
          }),
          catchError((error: HttpErrorResponse) =>
            of(HireStatementActions.add_Hire_Statement_Failure({ error: error }))
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const editHireStatement = createEffect(
  (
    actions = inject(Actions),
    hireStatementService = inject(HireStatementService)
  ) => {
    return actions.pipe(
      ofType(HireStatementActions.edit_Hire_Statement),
      mergeMap((action) =>
        hireStatementService
          .editHireStatement(action.hireStatementId, action.hireStatement)
          .pipe(
            map((res: HireStatement) =>
              HireStatementActions.edit_Hire_Statement_Success({
                hireStatement: res,
                successMessage: 'Hire Statement edited successfully!',
              })
            ),
            catchError((error: HttpErrorResponse) =>
              of(
                HireStatementActions.edit_Hire_Statement_Failure({ error: error })
              )
            )
          )
      )
    );
  },
  {
    functional: true,
  }
);

export const exportHireStatements = createEffect(
  (
    actions = inject(Actions),
    hireStatementService = inject(HireStatementService),
    fileService = inject(FileService),
    utilityService = inject(UtilityService),
    activatedRoute = inject(ActivatedRoute)
  ) => {
    return actions.pipe(
      ofType(HireStatementActions.export_Hire_Statements),
      map(() =>
        utilityService.getParamsFromRoute(activatedRoute.snapshot.root)
      ),
      mergeMap((params) =>
        hireStatementService.exportHireStatements(params['id']).pipe(
          map((res: ArrayBuffer) => {
            fileService.downloadFile(res, 'Hire Statements');
            return HireStatementActions.export_Hire_Statements_Success();
          }),
          catchError((error: HttpErrorResponse) =>
            of(HireStatementActions.export_Hire_Statements_Failure({ error }))
          )
        )
      )
    );
  },
  {
    // You can omit this part if not needed
    // This indicates that the effect is a pure function and can be tree-shaken during optimization
    functional: true,
  }
);

export const openHireStatementDialog = createEffect(
  (
    actions = inject(Actions),
    dialog = inject(MatDialog),
    dialogOptions = inject(DialogOptions)
  ) => {
    return actions.pipe(
      ofType(HireStatementActions.open_Hire_Statement_Dialog),
      tap((action) => {
        dialog.open(HireStatementAddEditComponent, {
          ...dialogOptions,
          data: {
            hireStatement: action.hireStatement,
          },
        });
      })
    );
  },
  {
    functional: true,
    dispatch: false,
  }
);

export const loadHireStatementBulkRoute = createEffect(
  (
    actions = inject(Actions),
    store = inject(Store),
    utilityService = inject(UtilityService),
    hireStatementService = inject(HireStatementService)
  ) => {
    return actions.pipe(
      ofType(routerNavigationAction),
      filter(
        ({ payload }) =>
          payload.routerState.url.includes('hire-statement') &&
          utilityService.urlIncludesId(payload.routerState.url)
      ),
      map(({ payload }) =>
        utilityService.getParamsFromRoute(payload.routerState.root)
      ),
      mergeMap((params) => {
        if (!params['itemId']) {
          return of([]).pipe(
            map((res: HireStatementBulk[]) =>
              HireStatementBulkActions.load_Hire_Statement_Bulks_Success({
                hireStatementBulks: res,
              })
            )
          );
        }
        return hireStatementService
          .loadHireStatementBulkList(params['itemId'])
          .pipe(
            map((res: HireStatementBulk[]) =>
              HireStatementBulkActions.load_Hire_Statement_Bulks_Success({
                hireStatementBulks: res,
              })
            ),
            catchError((error: HttpErrorResponse) =>
              of(
                HireStatementBulkActions.load_Hire_Statement_Bulks_Failure({
                  error,
                })
              )
            )
          );
      })
    );
  },
  {
    functional: true,
  }
);

export const loadHireStatementBulk = createEffect(
  (
    actions = inject(Actions),
    store = inject(Store),
    utilityService = inject(UtilityService),
    activatedRoute = inject(ActivatedRoute),
    hireStatementService = inject(HireStatementService)
  ) => {
    return actions.pipe(
      ofType(
        HireStatementBulkActions.remove_Hire_Statement_Bulk_Success,
        HireStatementBulkActions.add_Hire_Statement_Bulk_Success,
        HireStatementBulkActions.edit_Hire_Statement_Bulk_Success,
        HireStatementActions.edit_Hire_Statement_Success
      ),
      map(
        () =>
          utilityService.getParamsFromRoute(activatedRoute.snapshot.root)[
            'itemId'
          ]
      ),
      mergeMap((id) =>
        hireStatementService.loadHireStatementBulkList(id).pipe(
          map((res: HireStatementBulk[]) =>
            HireStatementBulkActions.load_Hire_Statement_Bulks_Success({
              hireStatementBulks: res,
            })
          ),
          catchError((error: HttpErrorResponse) =>
            of(
              HireStatementBulkActions.load_Hire_Statement_Bulks_Failure({ error })
            )
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const removeHireStatementBulk = createEffect(
  (
    actions = inject(Actions),
    hireStatementService = inject(HireStatementService)
  ) => {
    return actions.pipe(
      ofType(HireStatementBulkActions.remove_Hire_Statement_Bulk),
      mergeMap((action) =>
        hireStatementService.removeHireStatementBulk(action.id).pipe(
          map((res: HireStatementBulk) =>
            HireStatementBulkActions.remove_Hire_Statement_Bulk_Success({
              hireStatementBulk: res,
              successMessage: 'Hire Statement Bulk removed successfully!',
            })
          ),
          catchError((error: HttpErrorResponse) =>
            of(
              HireStatementBulkActions.remove_Hire_Statement_Bulk_Failure({ error })
            )
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const addHireStatementBulk = createEffect(
  (
    actions = inject(Actions),
    hireStatementService = inject(HireStatementService)
  ) => {
    return actions.pipe(
      ofType(HireStatementBulkActions.add_Hire_Statement_Bulk),
      mergeMap((action) =>
        hireStatementService
          .addHireStatementBulk(action.hireStatementBulk)
          .pipe(
            map((res: HireStatementBulk) =>
              HireStatementBulkActions.add_Hire_Statement_Bulk_Success({
                hireStatementBulk: res,
                successMessage: 'Hire statement Bulk added successfully!',
              })
            ),
            catchError((error: HttpErrorResponse) =>
              of(
                HireStatementBulkActions.add_Hire_Statement_Bulk_Failure({
                  error: error,
                })
              )
            )
          )
      )
    );
  },
  {
    functional: true,
  }
);

export const editHireStatementBulk = createEffect(
  (
    actions = inject(Actions),
    hireStatementService = inject(HireStatementService)
  ) => {
    return actions.pipe(
      ofType(HireStatementBulkActions.edit_Hire_Statement_Bulk),
      mergeMap((action) =>
        hireStatementService
          .editHireStatementBulk(
            action.hireStatementBulkId,
            action.hireStatementBulk
          )
          .pipe(
            map((res: HireStatementBulk) =>
              HireStatementBulkActions.edit_Hire_Statement_Bulk_Success({
                hireStatementBulk: res,
                successMessage: 'Hire Statement Bulk edited successfully!',
              })
            ),
            catchError((error: HttpErrorResponse) =>
              of(
                HireStatementBulkActions.edit_Hire_Statement_Bulk_Failure({
                  error: error,
                })
              )
            )
          )
      )
    );
  },
  {
    functional: true,
  }
);

export const exportHireStatementBulks = createEffect(
  (
    actions = inject(Actions),
    hireStatementService = inject(HireStatementService),
    fileService = inject(FileService),
    utilityService = inject(UtilityService),
    activatedRoute = inject(ActivatedRoute)
  ) => {
    return actions.pipe(
      ofType(HireStatementBulkActions.export_Hire_Statement_Bulks),
      map(() =>
        utilityService.getParamsFromRoute(activatedRoute.snapshot.root)
      ),
      mergeMap((params) =>
        hireStatementService.exportHireStatementBulks(params['itemId']).pipe(
          map((res: ArrayBuffer) => {
            fileService.downloadFile(res, 'Hire Statement Bulks');
            return HireStatementBulkActions.export_Hire_Statement_Bulks_Success();
          }),
          catchError((error: HttpErrorResponse) =>
            of(
              HireStatementBulkActions.export_Hire_Statement_Bulks_Failure({
                error,
              })
            )
          )
        )
      )
    );
  },
  {
    // You can omit this part if not needed
    // This indicates that the effect is a pure function and can be tree-shaken during optimization
    functional: true,
  }
);

export const openHireStatementBulkDialog = createEffect(
  (
    actions = inject(Actions),
    dialog = inject(MatDialog),
    dialogOptions = inject(DialogOptions)
  ) => {
    return actions.pipe(
      ofType(HireStatementBulkActions.open_Hire_Statement_Bulk_Dialog),
      tap((action) => {
        dialog.open(HireStatementBulkAddEditComponent, {
          ...dialogOptions,
          data: {
            hireStatementBulk: action.hireStatementBulk,
          },
        });
      })
    );
  },
  {
    functional: true,
    dispatch: false,
  }
);
