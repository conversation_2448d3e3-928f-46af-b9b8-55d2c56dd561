<p-dialog
  [draggable]="false"
  [closable]="true"
  [modal]="true"
  [visible]="isVisible()"
  (visibleChange)="hideDialog()"
  [style]="{ width: '800px' }"
>
  <ng-template pTemplate="header">
    <div class="header">Billing History</div>
  </ng-template>

  <div class="d-flex mb-20 gap-8 justify-content-end">
    <button
      class="btn-secondary"
      type="button"
      (click)="compareHistory()"
      [disabled]="selectedItems.length !== 2 || loading().comparing"
    >
      Compare
    </button>
    <button
      class="btn-secondary"
      type="button"
      (click)="downloadHistory()"
      [disabled]="selectedItems.length !== 1 || loading().download"
    >
      <em class="pi pi-download"></em>
      Download
    </button>
    <span class="p-input-icon-left">
      <em class="pi pi-search"></em>
      <input
        type="text"
        pInputText
        [(ngModel)]="searchValue"
        placeholder="Search..."
        (input)="table.filterGlobal(searchValue, 'contains')"
      />
    </span>
  </div>

  <p-table
    #table
    [columns]="listColumns"
    [value]="billingHistory"
    [scrollable]="true"
    scrollHeight="500px"
    [rowsPerPageOptions]="[10, 25, 50]"
    [paginator]="true"
    [rows]="10"
    [filterDelay]="0"
    [(selection)]="selectedItems"
    [globalFilterFields]="[tableFields.fileName, tableFields.comments]"
  >
    <ng-template pTemplate="header" let-columns>
      <tr>
        <ng-container *ngFor="let column of columns">
          <ng-container [ngSwitch]="column.field">
            <th
              *ngSwitchCase="tableFields.tableCheckbox"
              scope="col"
              [style.min-width.px]="column.width"
              [style.width.%]="(column.width / tableWidth) * 100"
            >
              <p-tableHeaderCheckbox></p-tableHeaderCheckbox>
            </th>
            <th
              *ngSwitchDefault
              [style.min-width.px]="column.width"
              [style.width.%]="(column.width / tableWidth) * 100"
              [pSortableColumn]="column.field"
              [pSortableColumnDisabled]="!column.sortable"
            >
              <span>{{ column.name }}</span>
              <p-sortIcon *ngIf="column.sortable" [field]="column.field" />
            </th>
          </ng-container>
        </ng-container>
      </tr>
    </ng-template>
    <ng-template
      let-index="rowIndex"
      pTemplate="body"
      let-row
      let-columns="columns"
    >
      <tr>
        <ng-container *ngFor="let column of columns">
          <ng-container [ngSwitch]="column.field">
            <td *ngSwitchCase="tableFields.tableCheckbox">
              <p-tableCheckbox
                [value]="row"
                (click)="$event.stopPropagation()"
              ></p-tableCheckbox>
            </td>
            <td *ngSwitchCase="tableFields.createdByName">
              <div class="d-flex flex-direction-column gap-4">
                <span>{{ row.createdByName }}</span>
                <span>{{ row.createdDate | date : 'dd/MM/yyyy HH:mm' }}</span>
              </div>
            </td>

            <td *ngSwitchDefault>{{ row[column.field] }}</td>
          </ng-container>
        </ng-container>
      </tr>
    </ng-template>
    <ng-template pTemplate="emptymessage">
      <tr>
        <td colspan="4" style="text-align: center">No results found</td>
      </tr>
    </ng-template>
  </p-table>
</p-dialog>
