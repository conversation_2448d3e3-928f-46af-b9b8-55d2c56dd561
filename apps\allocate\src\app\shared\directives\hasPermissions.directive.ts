import { Directive, inject, Input, OnDestroy, OnInit } from '@angular/core';
import { NgIf } from '@angular/common';
import { Store } from '@ngrx/store';
import { Subject, takeUntil } from 'rxjs';
import {selectHasUserPermission} from "libs/auth/src/lib/store/auth.feature";

@Directive({
  selector: '[lhaHasPermission]',
  standalone: true,
  hostDirectives: [NgIf],
})
export class HasPermissionDirective implements OnInit, OnDestroy {
  private readonly store = inject(Store);
  private readonly ngIfRef = inject(NgIf);
  private permissions: string[] = [];
  destroy$ = new Subject<void>();

  @Input()
  application: string = '';

  @Input()
  set lhaHasPermission(permissions: string[]) {
    this.permissions = permissions;
  }

  ngOnInit() {
    if (this.permissions.length > 0) {
      this.store
        .select(selectHasUserPermission(this.permissions))
        .pipe(takeUntil(this.destroy$))
        .subscribe((hasAccess) => {
          this.ngIfRef.ngIf = hasAccess;
        });
    } else {
      this.ngIfRef.ngIf = true;
    }
  }

  ngOnDestroy() {
    this.destroy$.next();
  }
}
