import { ColumnModel } from 'libs/components/src/lib/models/column.model';
import { OperatorsTableFields } from './operators-table-fields.enum';

export function InitializeOperatorsTable(): ColumnModel[] {
  const assetsListColumns = [
    new ColumnModel(OperatorsTableFields.name, 'Operator Name', 170, {
      sortable: true,
    }),
    new ColumnModel(OperatorsTableFields.vatNumber, 'VAT Number', 150),
    new ColumnModel(OperatorsTableFields.euNumber, 'EU Number', 150),
    new ColumnModel(OperatorsTableFields.isActive, 'Is Active', 80),
    new ColumnModel(OperatorsTableFields.createdByName, 'Created By', 150, {
      sortable: true,
    }),
    new ColumnModel(OperatorsTableFields.updatedByName, 'Updated By', 150, {
      sortable: true,
    }),
    new ColumnModel(OperatorsTableFields.assetNames, 'Assets', 250),
    new ColumnModel(OperatorsTableFields.locationNames, 'Locations', 250),
    new ColumnModel(OperatorsTableFields.actions, '', 100),
  ];
  return assetsListColumns;
}
