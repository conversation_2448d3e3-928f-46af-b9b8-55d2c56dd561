<p-dialog
  [draggable]="true"
  [closable]="true"
  [modal]="true"
  [visible]="isVisible()"
  (visibleChange)="hideDialog()"
  [style]="{ width: '600px' }"
>
  <ng-template pTemplate="header">
    <div class="header">
      {{ activityCategory() ? 'Edit' : 'Add' }} Activity Category

      <em
        *ngIf="activityCategory()"
        class="pi pi-info-circle"
        [pTooltip]="tooltipContent"
      ></em>
      <ng-template #tooltipContent>
        <div
          class="d-flex flex-direction-column gap-4 fs-12"
          *ngIf="activityCategory()?.createdByName"
        >
          <span>Created By: {{ activityCategory()?.createdByName }}</span>
          <span
            >Created Date:
            {{
              activityCategory()?.createdDate | date : 'dd/MM/yyyy HH:mm'
            }}</span
          >
        </div>
        <div
          class="d-flex flex-direction-column gap-4 fs-12"
          *ngIf="activityCategory()?.updatedByName"
        >
          <span>Last Updated By: {{ activityCategory()?.updatedByName }}</span>
          <span
            >Updated Date:
            {{
              activityCategory()?.updatedDate | date : 'dd/MM/yyyy HH:mm'
            }}</span
          >
        </div>
      </ng-template>
    </div>
  </ng-template>
  <ng-template pTemplate="content">
    <form [formGroup]="form" class="d-flex flex-wrap gap-16">
      <div class="d-flex w-100 justify-content-between gap-8">
        <div class="d-flex w-50 flex-direction-column gap-4">
          <label class="fs-14">Activity Category Name</label>
          <input pInputText type="text" formControlName="name" />
          <small
            class="validation-control-error"
            *ngIf="controls.name?.invalid && controls.name?.touched"
          >
            Category name is required.
          </small>
        </div>
        <div class="d-flex w-50 flex-direction-column gap-4">
          <label class="fs-14">Activity Type</label>
          <p-dropdown
            [options]="activityOptions"
            [filter]="true"
            [showClear]="true"
            formControlName="activityType"
            optionLabel="name"
            optionValue="value"
            placeholder="Select a Activity Type"
            appendTo="body"
            panelStyleClass="new-version-panel"
            styleClass="new-version"
          ></p-dropdown>
          <small
            class="validation-control-error"
            *ngIf="
              controls.activityType?.invalid && controls.activityType?.touched
            "
          >
            Activity Type is required.
          </small>
        </div>
      </div>
      <div class="d-flex w-100 justify-content-between gap-8">
        <div class="d-flex w-50 align-items-center gap-8">
          <p-inputSwitch formControlName="isHidden" />
          <label class="fs-14">Hidden from client</label>
        </div>
      </div>
    </form>
  </ng-template>
  <ng-template pTemplate="footer">
    <div class="actions">
      <button class="btn-tertiary" type="button" (click)="hideDialog()">
        Cancel
      </button>
      <button class="btn-primary" type="button" (click)="onSubmit()">
        {{ activityCategory() ? 'Save' : 'Add' }}
      </button>
    </div>
  </ng-template>
</p-dialog>
