import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { NgIf } from '@angular/common';
import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';

import { Store } from '@ngrx/store';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { DialogModule } from 'primeng/dialog';

import { billingsFeature } from '../../../store/features/billing.features';
import { BillingsActions } from '../../../store/actions/billing.actions';

@Component({
  selector: 'lha-billing-calculation',
  standalone: true,
  imports: [DialogModule, InputTextareaModule, ReactiveFormsModule, NgIf],
  templateUrl: './billing-calculation.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BillingCalculationComponent {
  private readonly store = inject(Store);
  billing = this.store.selectSignal(billingsFeature.selectBilling);
  isVisible = this.store.selectSignal(
    billingsFeature.selectIsVisibleCalcBillingPeriod
  );
  form = new FormGroup({
    comments: new FormControl<string>('', [Validators.required]),
  });

  commentsCtrl = this.form.get('comments');

  onSubmit() {
    if (this.form.invalid) {
      this.form.markAllAsTouched();
      return;
    }

    this.store.dispatch(
      BillingsActions.calc_Billing_Period({
        billingPeriodId: this.billing()?.billingPeriodId!,
        message: this.form.value.comments ?? '',
      })
    );
  }

  hideDialog() {
    this.form.reset();
    this.form.markAsUntouched();
    this.store.dispatch(
      BillingsActions.change_visibility_calc_billing_period({
        visible: false,
      })
    );
  }
}
