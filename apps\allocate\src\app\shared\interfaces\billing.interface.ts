import { BillingPeriod } from './billing-period.interface';

export interface Billing {
  billingPeriodId: string;
  billingPeriodName: string;
  isLocked: boolean;
  status: string;
  dateCompleted: Date;
  periodStartDate: Date;
  periodEndDate: Date;
  isSplitBilling: boolean;
  billingPeriodMonth: string;
  recalculate: boolean;
  voyageBillingPeriods: BillingPeriod[];
  numberOfVoyages: number;
  numberOfVessels: number;
  numberOfOperators: number;
}
