<div class="d-flex justify-content-between mb-40">
  <b class="fs-24">Activities</b>

  <div class="d-flex gap-20">
    <span class="p-input-icon-left">
      <em class="pi pi-search"></em>
      <input
        type="text"
        pInputText
        [(ngModel)]="searchValue"
        placeholder="Search..."
        (input)="table.filterGlobal(searchValue, 'contains')"
      />
    </span>

    <button type="button" class="btn-primary" (click)="create()">
      Create
    </button>

    <button
      type="button"
      class="btn-export align-items-center d-flex"
      (click)="export()"
    >
      <img src="assets/icons/exel.svg" />
      Export
    </button>
  </div>
</div>

<p-table
  #table
  [columns]="listColumns"
  [value]="activities"
  [scrollable]="true"
  scrollHeight="calc(100vh - 273px)"
  [rowsPerPageOptions]="[10, 25, 50]"
  [paginator]="true"
  [rows]="10"
  [filterDelay]="0"
  [globalFilterFields]="[
    tableFields.name,
    tableFields.code,
    tableFields.type,
    tableFields.displayChargeability,
 ]"
>
  <ng-template pTemplate="header" let-columns>
    <tr>
      <th
        *ngFor="let column of columns"
        [style.min-width.px]="column.width"
        [style.width.%]="(column.width / tableWidth) * 100"
        [pSortableColumn]="column.field"
        [pSortableColumnDisabled]="!column.sortable"
      >
        <span>{{ column.name }}</span>
        <p-sortIcon *ngIf="column.sortable" [field]="column.field" />
      </th>
    </tr>
  </ng-template>
  <ng-template
    let-index="rowIndex"
    pTemplate="body"
    let-row
    let-columns="columns"
  >
    <tr>
      <ng-container *ngFor="let column of columns">
        <ng-container [ngSwitch]="column.field">
          <td *ngSwitchCase="tableFields.type">
            <lha-custom-chip
              [cssClass]="row.type"
              [size]="150"
              [text]="(activityTypeList() | filterBy : byValue(row.type))[0].description"
            >
            </lha-custom-chip>
          </td>
          <td *ngSwitchCase="tableFields.actions">
            <div class="d-flex gap-8 flex-wrap">
              <button type="button" class="btn-icon-only" (click)="edit(row)">
                <em class="pi pi-pencil"></em>
              </button>

              <button
                type="button"
                class="btn-icon-only"
                [disabled]="row.usedInVoyage"
                (click)="remove(row)"
              >
                <em class="pi pi-trash"></em>
              </button>
            </div>
          </td>
          <td *ngSwitchDefault>{{ row[column.field] }}</td>
        </ng-container>
      </ng-container>
    </tr>
  </ng-template>
  <ng-template pTemplate="emptymessage">
    <tr>
      <td colspan="5" style="text-align: center">No results found</td>
    </tr>
  </ng-template>
</p-table>

<md-activity-add-edit/>
