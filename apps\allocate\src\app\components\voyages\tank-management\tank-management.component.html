<lha-table
  *ngIf="tankManagementState()"
  [columns]="dynamicColumns()"
  [loading]="tankManagementState().loading.list"
  [exportLoading]="tankManagementState().loading.export"
  [data]="tankManagementState().tankManagements"
  [canExportTable]="true"
  [pageSize]="query.pageSize"
  [tableName]="voyage()?.voyageNumber + '_tank management'"
  (queryChange)="onChangeQuery($event)"
  (exportTable)="exportTankStatuses()"
>
  <h3 class="mat-title--warn" tableHeaderLeft>Tank Status</h3>
  <ng-container tableHeaderRightBtn>
    <button mat-icon-button *lhaIsVoyageLocked style="pointer-events: none">
      <mat-icon color="warn">lock</mat-icon>
    </button>
    <button
      mat-raised-button
      lhaIsVoyageLockedDisable
      (click)="addEditTankManagement(null)"
      color="primary"
    >
      Create
    </button>
  </ng-container>
  <ng-template lhaCellTemplate="bulkTransactionBulkTypeFluidType" let-item>
    <lha-custom-chip
      [text]="
        item.bulkTransactionBulkTypeFluidType
          | propertyName
            : tankManagementState().fluidTypes
            : 'value'
            : 'description'
      "
    ></lha-custom-chip>
  </ng-template>
  <ng-template lhaCellTemplate="cleaned" let-item>
    <mat-icon
      [ngClass]="item.cleaned ? 'mat-icon--accept' : 'mat-icon--cancel'"
      >{{ item.cleaned ? 'check_circle' : 'cancel' }}</mat-icon
    >
  </ng-template>
  <ng-template lhaCellTemplate="tankStatusId" let-item>
    <button
      mat-icon-button
      color="primary"
      (click)="addEditTankManagement(item)"
      lhaIsVoyageLockedDisable
    >
      <mat-icon>edit</mat-icon>
    </button>
    <button
      mat-icon-button
      color="warn"
      (click)="removeTankManagement(item)"
      lhaIsVoyageLockedDisable
    >
      <mat-icon>delete</mat-icon>
    </button>
  </ng-template>
</lha-table>
