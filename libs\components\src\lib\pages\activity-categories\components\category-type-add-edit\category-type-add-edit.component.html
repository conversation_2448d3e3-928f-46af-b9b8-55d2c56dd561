<p-dialog
  [draggable]="true"
  [closable]="true"
  [modal]="true"
  [visible]="isVisible()"
  (visibleChange)="hideDialog()"
  [style]="{ width: '800px' }"
>
  <ng-template pTemplate="header">
    <div class="header">
      Add Category Type to {{ activityCategory()?.name }}
    </div>
  </ng-template>

  <div class="mt-12 mb-12 d-flex justify-content-end flex-wrap gap-20">
    <span class="p-input-icon-left">
      <em class="pi pi-search search-icon"></em>
      <input
        pInputText
        type="text"
        [(ngModel)]="searchTerm"
        (input)="dt.filterGlobal(searchTerm, 'contains')"
        placeholder="Search..."
      />
    </span>
    <button class="btn-primary" type="button" (click)="addNewRow()">Add</button>
  </div>

  <p-table
    #dt
    [value]="activityCategoryTypes"
    dataKey="activityCategoryTypeId"
    editMode="row"
    [rows]="10"
    [showCurrentPageReport]="true"
    [rowsPerPageOptions]="[10, 25, 50]"
    [paginator]="true"
    [globalFilterFields]="['name']"
    scrollHeight="550px"
  >
    <ng-template pTemplate="header">
      <tr>
        <th
          [style.min-width.px]="180"
          [style.width.%]="(180 / tableWidth) * 100"
        >
          Activity Category Type
        </th>
        <th
          *ngIf="activityCategory()?.activityType === activityType.Lifting"
          [style.min-width.px]="150"
          [style.width.%]="(180 / tableWidth) * 100"
        >
          Inbound Lifting
        </th>
        <th
          [style.min-width.px]="150"
          [style.width.%]="(180 / tableWidth) * 100"
        >
          Out of Port Activity
        </th>
        <th
          [style.min-width.px]="150"
          [style.width.%]="(180 / tableWidth) * 100"
        >
          Created By
        </th>
        <th
          [style.min-width.px]="72"
          [style.width.%]="(72 / tableWidth) * 100"
        ></th>
      </tr>
    </ng-template>
    <ng-template
      pTemplate="body"
      let-row
      let-editing="editing"
      let-ri="rowIndex"
    >
      <tr [pEditableRow]="row">
        <td>
          <p-cellEditor>
            <ng-template pTemplate="input">
              <input
                pInputText
                type="text"
                [required]="true"
                [(ngModel)]="row.name"
              />
            </ng-template>
            <ng-template pTemplate="output">
              {{ row.name }}
            </ng-template>
          </p-cellEditor>
        </td>
        <td *ngIf="activityCategory()?.activityType === activityType.Lifting">
          <p-cellEditor>
            <ng-template pTemplate="input">
              <p-inputSwitch
                [(ngModel)]="row.isInboundLifting"
                (onChange)="changeInboundLifting($event.checked, ri)"
              />
            </ng-template>
            <ng-template pTemplate="output">
              <i
                class="pi fs-16"
                [ngClass]="row.isInboundLifting ? 'pi-check' : 'pi-times'"
                [style.color]="row.isInboundLifting ? 'green' : 'red'"
              ></i>
            </ng-template>
          </p-cellEditor>
        </td>
        <td>
          <p-cellEditor>
            <ng-template pTemplate="input">
              <p-inputSwitch
                [(ngModel)]="row.createOutOfPortActivity"
                [disabled]="row.isInboundLifting"
              />
            </ng-template>
            <ng-template pTemplate="output">
              <i
                class="pi fs-16"
                [ngClass]="
                  row.createOutOfPortActivity ? 'pi-check' : 'pi-times'
                "
                [style.color]="row.createOutOfPortActivity ? 'green' : 'red'"
              ></i>
            </ng-template>
          </p-cellEditor>
        </td>
        <td>
          <div class="d-flex align-items-start flex-direction-column gap-4">
            <span>{{ row.createdByName }}</span>
            <span>{{ row.createdDate | date : 'dd/MM/yyyy HH:mm' }}</span>
          </div>
        </td>

        <td>
          <div class="d-flex align-items-center justify-content-between gap-8">
            <button
              *ngIf="!editing"
              type="button"
              pInitEditableRow
              (click)="onRowEditInit(row, ri)"
              class="btn-icon-only"
            >
              <em class="pi pi-pencil"></em>
            </button>
            <button
              *ngIf="!editing"
              type="button"
              (click)="remove(row)"
              class="btn-icon-only"
            >
              <em class="pi pi-trash"></em>
            </button>
            <button
              *ngIf="editing"
              type="button"
              pSaveEditableRow
              (click)="onRowEditSave(row)"
              [disabled]="!row.name"
              class="btn-icon-only"
            >
              <em class="pi pi-check"></em>
            </button>
            <button
              *ngIf="editing"
              pCancelEditableRow
              (click)="onRowEditCancel(row, ri)"
              class="btn-icon-only"
            >
              <em class="pi pi-times"></em>
            </button>
          </div>
        </td>
      </tr>
    </ng-template>
    <ng-template pTemplate="emptymessage">
      <tr>
        <td colspan="4" style="text-align: center">No results found</td>
      </tr>
    </ng-template>
  </p-table>
</p-dialog>
