import { <PERSON><PERSON><PERSON>, NgIf } from '@angular/common';
import { Component, DestroyRef, inject, OnInit, signal } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { CardModule } from 'primeng/card';
import { DividerModule } from 'primeng/divider';
import { CheckboxModule } from 'primeng/checkbox';
import { ButtonModule } from 'primeng/button';
import { TabViewModule } from 'primeng/tabview';
import { TableModule } from 'primeng/table';
import { Store } from '@ngrx/store';
import { DialogModule } from 'primeng/dialog';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { ReactiveFormsModule } from '@angular/forms';
import { DropdownModule } from 'primeng/dropdown';
import { ColumnModel } from 'libs/components/src/lib/models/column.model';
import { currentUserFeature } from 'libs/auth/src/lib/store/current-user/current-user.features';
import { hireRequestFeature } from 'libs/services/src/lib/services/contain/store/features/hire-request.feature';
import { CargoHiresTableFields } from '../../shared/enums/cargo-hires-table-fields.enum';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { DatePipe } from '@angular/common';
import { ContainLandingPageActions } from 'libs/services/src/lib/services/contain/store/actions/landing-page.actions';
import { locationsFeature } from 'libs/services/src/lib/services/maintenance/store/features';
import { HireRequestActions } from 'libs/services/src/lib/services/contain/store/actions/hire-request.actions';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { HireRequestFilterComponent } from '../../components/hire-request-filter/hire-request-filters.component';
import { HireRequestCargo } from 'libs/services/src/lib/services/contain/interfaces/hire-request-cargo.interface';
import { CargoHireDetailsDialogComponent } from '../../components/cargo-hire-details-dialog/cargo-hire-details.dialog';
import { CargoHireSetOffHireDateDialog } from '../../components/cargo-hire-set-off-hire-date-dialog/cargo-hire-set-off-hire-date.dialog';
import { CargoHireBatchUpdateDialog } from '../../components/cargo-hire-batch-update-dialog/cargo-hire-batch-update.dialog';

@Component({
  standalone: true,
  selector: 'hire-request-list',
  imports: [
    NgIf,
    NgFor,
    CardModule,
    DividerModule,
    CheckboxModule,
    ButtonModule,
    TabViewModule,
    TableModule,
    DialogModule,
    InputTextareaModule,
    DropdownModule,
    ReactiveFormsModule,
    DatePipe,
    FormsModule,
    CommonModule,
    HireRequestFilterComponent,
    CargoHireDetailsDialogComponent,
    CargoHireSetOffHireDateDialog,
    CargoHireBatchUpdateDialog,
  ],
  templateUrl: 'cargo-hires-list.component.html'
})
export class CargoHiresListComponent implements OnInit {
  store = inject(Store);
  readonly route = inject(ActivatedRoute);
  destroyRef = inject(DestroyRef);
  private readonly router = inject(Router);
  currentUser = this.store.selectSignal(currentUserFeature.selectUser);
  locations = this.store.selectSignal(locationsFeature.selectLocations);
  filterModel = this.store.selectSignal(hireRequestFeature.selectFilter);
  tableWidth = 3235;
  listColumns = signal<ColumnModel[]>([]);
  detailsDialogVisible = false;
  selectedHireRequestCargo: HireRequestCargo | null = null;
  selectedHireRequestCargoes: HireRequestCargo[] = [];
  updateSelectedHiresDialogVisible = false;
  offHiredDialogVisible = false;

  rows: HireRequestCargo[] = [];

  ngOnInit() {
    this.getCargoHiresList();

    this.listColumns.set([
      new ColumnModel(CargoHiresTableFields.checkbox, '', 5),
      new ColumnModel(CargoHiresTableFields.cargoCCUId, 'Unit', 70),
      new ColumnModel(CargoHiresTableFields.cargoDescription, 'Description', 100),
      new ColumnModel(CargoHiresTableFields.cargoUnitType, 'Type Name', 80),
      new ColumnModel(CargoHiresTableFields.onHiredDate, 'On-Hired', 130),
      new ColumnModel(CargoHiresTableFields.clientName, 'Client', 120),
      new ColumnModel(CargoHiresTableFields.billingAsset, 'Billing Asset', 90),
      new ColumnModel(CargoHiresTableFields.reference, 'Ref No / Well', 60),
      new ColumnModel(CargoHiresTableFields.shipped, 'Shipped', 130),
      new ColumnModel(CargoHiresTableFields.manifestOut, 'Manifest (Out)', 60),
      new ColumnModel(CargoHiresTableFields.assetName, 'Asset', 90),
      new ColumnModel(CargoHiresTableFields.returned, 'Returned', 130),
      new ColumnModel(CargoHiresTableFields.manifestIn, 'Manifest (In)', 60),
      new ColumnModel(
        CargoHiresTableFields.vendorInbound,
        'Vendor-Inbound',
        60
      ),
      new ColumnModel(CargoHiresTableFields.offHiredDate, 'Off-Hired', 60),
      new ColumnModel(CargoHiresTableFields.action, '', 10),
      new ColumnModel(CargoHiresTableFields.action, '', 10),
    ]);

    if (!this.currentUser() || !this.locations()) {
      this.initializeRelatedData();
    }

    this.store
      .select(hireRequestFeature.selectCargoHires)
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((value) => {
        this.rows = value ?? [];
      });
  }

  getCargoHiresList() {
    this.store.dispatch(
      HireRequestActions.load_Cargo_Hires_List({
        filterModel: this.filterModel(),
      })
    );
  }

  setDialogVisible(): void {
    this.detailsDialogVisible = !this.detailsDialogVisible;
  }

  setOffHiredDateDialogVisible(): void {
    this.offHiredDialogVisible = !this.offHiredDialogVisible;
  }

  setUpdateSelectedHiresDialogVisible(): void {
    this.selectedHireRequestCargoes = [];
    this.updateSelectedHiresDialogVisible =
      !this.updateSelectedHiresDialogVisible;
    this.getCargoHiresList();
  }

  initializeRelatedData(): void {
    this.store.dispatch(
      ContainLandingPageActions.initialize_Landing_Page({
        filterModel: this.filterModel(),
      })
    );
  }

  handleNavigate(path: string) {
    this.router.navigate([path]);
  }

  setSelectedRow(
    selected: boolean,
    hireRequestCargoId: string | undefined
  ): void {
    if (selected) {
      if (
        !this.selectedHireRequestCargoes.some(
          (x) => x.hireRequestCargoId === hireRequestCargoId
        )
      ) {
        this.selectedHireRequestCargoes.push(
          this.rows.find(
            (x) => x.hireRequestCargoId === hireRequestCargoId
          ) as HireRequestCargo
        );
      }
    } else {
      this.selectedHireRequestCargoes = this.selectedHireRequestCargoes.filter(
        (x) => x.hireRequestCargoId !== hireRequestCargoId
      );
    }
  }

  isRowSelected(hireRequestCargoId: string | undefined): boolean {
    return this.selectedHireRequestCargoes.some(
      (x) => x.hireRequestCargoId === hireRequestCargoId
    );
  }

  openDetailsDialog(hireRequestCargo: HireRequestCargo | null): void {
    if (hireRequestCargo?.hireRequestCargoId) {
      this.store.dispatch(
        HireRequestActions.load_Cargo_Hire_Events({
          hireRequestCargoId: hireRequestCargo?.hireRequestCargoId ?? '',
        })
      );
    }

    this.selectedHireRequestCargo = hireRequestCargo;
    this.detailsDialogVisible = true;
  }

  openUpdateSelectedHiresDialog(): void {
    this.updateSelectedHiresDialogVisible = true;
  }

  openSetOffHiredDateDialog(hireRequestCargo: HireRequestCargo): void {
    this.selectedHireRequestCargo = hireRequestCargo;
    this.offHiredDialogVisible = true;
  }
}
