<div
  *ngIf="vm$ | async as vm"
  cdkDrag
  cdkDragRootElement=".cdk-overlay-pane"
  cdkDragHandle
  cdkDragBoundary=".cdk-overlay-container"
>
  <div mat-dialog-title>
    <h4>{{ isAdd ? 'Add ' : 'Edit ' }} Hire Statement</h4>
  </div>
  <mat-dialog-content *lhaLoading="vm.loading.createEdit">
    <form [formGroup]="form" (ngSubmit)="saveHireStatement()">
      <div class="form__block">
        <div class="form__box">
          <lha-single-select
            [options]="typeList"
            formControlName="type"
            bindValue="name"
            placeholder="Type"
          />
          <div *ngIf="form.controls.type.invalid && form.controls.type.touched">
            <mat-error *ngIf="form.controls.type.hasError('required')">
              Type is required.
            </mat-error>
          </div>
        </div>
        <div class="form__box">
          <mat-form-field appearance="outline" hideRequiredMarker="true">
            <mat-label>Delivery Date</mat-label>
            <input
              matInput
              [ngxMatDatetimePicker]="deliveryDate"
              [max]="timeAfter"
              formControlName="deliveryDate"
              (dateChange)="changeDeliveryDate($event)"
            />
            <mat-datepicker-toggle
              matIconSuffix
              [for]="$any(deliveryDate)"
            ></mat-datepicker-toggle>
            <ngx-mat-datetime-picker
              [defaultTime]="defaultTime"
              #deliveryDate
            ></ngx-mat-datetime-picker>
            <mat-error *ngIf="form.controls.deliveryDate.hasError('required')"
              >Delivery date is required</mat-error
            >
          </mat-form-field>
        </div>
        <div class="form__box">
          <mat-form-field appearance="outline" hideRequiredMarker="true">
            <mat-label>Redelivery Date</mat-label>
            <input
              matInput
              [ngxMatDatetimePicker]="redeliveryDate"
              [min]="timeBefore"
              (dateChange)="changeRedeliveryDate($event)"
              formControlName="redeliveryDate"
            />
            <mat-datepicker-toggle
              matIconSuffix
              [for]="$any(redeliveryDate)"
            ></mat-datepicker-toggle>
            <ngx-mat-datetime-picker
              [defaultTime]="defaultTime"
              #redeliveryDate
            ></ngx-mat-datetime-picker>
            <mat-error *ngIf="form.controls.redeliveryDate.hasError('required')"
              >Redelivery date is required</mat-error
            >
          </mat-form-field>
        </div>
        <div class="form__box">
          <mat-form-field appearance="outline" hideRequiredMarker="true">
            <mat-label>Delivery Place</mat-label>
            <input matInput type="text" formControlName="deliveryPlace" />
            <mat-error *ngIf="form.controls.deliveryPlace.hasError('required')">
              Delivery place is required.
            </mat-error>
          </mat-form-field>
        </div>
        <div class="form__box">
          <mat-form-field appearance="outline" hideRequiredMarker="true">
            <mat-label>Redelivery Place</mat-label>
            <input matInput type="text" formControlName="redeliveryPlace" />
            <mat-error
              *ngIf="form.controls.redeliveryPlace.hasError('required')"
            >
              Redelivery place is required.
            </mat-error>
          </mat-form-field>
        </div>
        <div class="form__box">
          <mat-form-field appearance="outline" hideRequiredMarker="true">
            <mat-label>Day Rate</mat-label>
            <input
              matInput
              type="number"
              formControlName="dayRate"
              [decimal]="true"
            />
            <mat-error *ngIf="form.controls.dayRate.hasError('required')">
              Day rate is required.
            </mat-error>
            <mat-error *ngIf="form.controls.dayRate.hasError('greaterThan')"
              >Day Rate should be more than 0</mat-error
            >
            <mat-error *ngIf="form.controls.dayRate.hasError('decimalPoint')"
              >Decimal point should be less than 2</mat-error
            >
          </mat-form-field>
        </div>
      </div>
      <div class="form__box form__box--mb20">
        <mat-slide-toggle formControlName="isOnHire"
          >Is
          {{
            form.controls.isOnHire.value ? 'On' : 'Off'
          }}
          Hire</mat-slide-toggle
        >
      </div>
      <mat-dialog-actions align="end">
        <button mat-raised-button color="primary" type="submit">
          {{ isAdd ? 'Add ' : 'Save ' }}
        </button>
        <button
          mat-raised-button
          color="warn"
          type="button"
          mat-dialog-close=""
        >
          Cancel
        </button>
      </mat-dialog-actions>
    </form>
  </mat-dialog-content>
</div>
