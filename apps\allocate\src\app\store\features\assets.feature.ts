import { createFeature, createReducer } from '@ngrx/store';
import { immerOn } from 'ngrx-immer/store';

import { AssetsAllocateActions } from '../actions/assets.actions';
import { initialAssetsMDState } from '../states/assets.state';
import { AssetActions } from 'libs/services/src/lib/services/maintenance/store/actions/assets.actions';

export const assetsAllocateReducer = createReducer(
  initialAssetsMDState,

  immerOn(
    AssetsAllocateActions.change_visibility_add_edit,
    (state, { visible, asset }) => {
      state.isVisibleAddEdit = visible;
      state.asset = asset;
    }
  ),
  immerOn(
    AssetActions.add_Asset_Success,
    AssetActions.edit_Asset_Success,
    (state) => {
      state.isVisibleAddEdit = false;
      state.asset = null;
    }
  ),
  immerOn(
    AssetsAllocateActions.change_visibility_history,
    (state, { visible, assetsHistory }) => {
      state.isVisibleHistory = visible;
      state.assetsHistory = assetsHistory;
    }
  ),
  immerOn(
    AssetsAllocateActions.change_visibility_mobile_well,
    (state, { visible, asset }) => {
      state.isVisibleMobileWell = visible;
      state.asset = asset;
    }
  )
);
export const assetsAllocateFeature = createFeature({
  name: 'AssetsAllocate',
  reducer: assetsAllocateReducer,
});
