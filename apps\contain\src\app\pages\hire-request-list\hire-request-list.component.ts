import {
  <PERSON><PERSON><PERSON>,
  NgIf,
} from '@angular/common';
import {
  Component,
  DestroyRef,
  inject,
  OnInit,
  signal,
} from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { CardModule } from 'primeng/card';
import { DividerModule } from 'primeng/divider';
import { CheckboxModule } from 'primeng/checkbox';
import { ButtonModule } from 'primeng/button';
import { TabViewModule } from 'primeng/tabview';
import { TableModule } from 'primeng/table';
import { Store } from '@ngrx/store';
import { DialogModule } from 'primeng/dialog';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { ReactiveFormsModule } from '@angular/forms';
import { DropdownModule } from 'primeng/dropdown';
import { ColumnModel } from 'libs/components/src/lib/models/column.model';
import { currentUserFeature } from 'libs/auth/src/lib/store/current-user/current-user.features';
import { hireRequestFeature } from 'libs/services/src/lib/services/contain/store/features/hire-request.feature';
import { HireRequestTableFields } from '../../shared/enums/hire-request-table-fields.enum';
import { HireRequest } from 'libs/services/src/lib/services/contain/interfaces/hire-request.interface';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { DatePipe } from '@angular/common';
import { ContainLandingPageActions } from 'libs/services/src/lib/services/contain/store/actions/landing-page.actions';
import { locationsFeature } from 'libs/services/src/lib/services/maintenance/store/features';
import { HireRequestActions } from 'libs/services/src/lib/services/contain/store/actions/hire-request.actions';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { HireRequestDetailsDialogComponent } from "../../components/hire-request-details-dialog/hire-request-details.dialog";
import { HireRequestFilterComponent } from "../../components/hire-request-filter/hire-request-filters.component";

@Component({
  standalone: true,
  selector: 'hire-request-list',
  imports: [
    NgIf,
    NgFor,
    CardModule,
    DividerModule,
    CheckboxModule,
    ButtonModule,
    TabViewModule,
    TableModule,
    DialogModule,
    InputTextareaModule,
    DropdownModule,
    ReactiveFormsModule,
    DatePipe,
    FormsModule,
    CommonModule,
    HireRequestDetailsDialogComponent,
    HireRequestFilterComponent
],
  templateUrl: 'hire-request-list.component.html',
})
export class HireRequestsListComponent implements OnInit {
  store = inject(Store);
  readonly route = inject(ActivatedRoute);
  destroyRef = inject(DestroyRef);
  private readonly router = inject(Router);
  currentUser = this.store.selectSignal(currentUserFeature.selectUser);
  locations = this.store.selectSignal(locationsFeature.selectLocations);
  filterModel = this.store.selectSignal(hireRequestFeature.selectFilter);
  tableWidth = 3235;
  listColumns = signal<ColumnModel[]>([]);
  detailsDialogVisible = false;
  selectedHireRequest: HireRequest | null = null;
  createDialogVisible = false;

  rows: HireRequest[] = [];

  ngOnInit() {
    this.store.dispatch(HireRequestActions.load_Hire_Requests_List({ filterModel: this.filterModel() }));

    this.listColumns.set([
      new ColumnModel(HireRequestTableFields.checkbox, 'Awaiting Collection', 5),
      new ColumnModel(HireRequestTableFields.requestedDate, 'Req. Date', 140),
      new ColumnModel(HireRequestTableFields.requestedBy, 'Req. By', 60),
      new ColumnModel(HireRequestTableFields.cargoDescription, 'Description', 60),
      new ColumnModel(HireRequestTableFields.unitQuantity, 'Unit Qty', 60),
      new ColumnModel(HireRequestTableFields.clientName, 'Client', 130),
      new ColumnModel(HireRequestTableFields.assetName, 'Asset', 60),
      new ColumnModel(HireRequestTableFields.cstRequired, 'CST', 60),
      new ColumnModel(HireRequestTableFields.doorsRequired, 'Doors', 60),
      new ColumnModel(HireRequestTableFields.removableSidesRequired, 'Removable Sides', 60),
      new ColumnModel(HireRequestTableFields.netRequired, 'Net', 60),
      new ColumnModel(HireRequestTableFields.tarpaulinRequired, 'Tarp', 60),
      new ColumnModel(HireRequestTableFields.shelvesRequired, 'Shelves', 60),
      new ColumnModel(HireRequestTableFields.ccuSupplier, 'CCU Supplier', 60),
      new ColumnModel(HireRequestTableFields.allocatedUnits, 'Allocated Units', 60),
      new ColumnModel(HireRequestTableFields.action, '', 40),
    ]);

    if(!this.currentUser() || !this.locations()) {
      this.initializeRelatedData();
    }

    this.store
      .select(hireRequestFeature.selectConfirmedHireRequests)
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((value) => {
        this.rows = value ?? [];
      });
  }

  setDialogVisible(): void {
    this.detailsDialogVisible = !this.detailsDialogVisible;
  }

  setCreateDialogVisible(): void {
    this.createDialogVisible = !this.createDialogVisible;
  }

  initializeRelatedData(): void {
    this.store.dispatch(ContainLandingPageActions.initialize_Landing_Page({filterModel: this.filterModel()}));
  }

  handleNavigate(path: string) {
    this.router.navigate([path]);
  }

  setAwaitingCollection(hireRequestId: string | undefined): void {
    if(hireRequestId){
      this.store.dispatch(HireRequestActions.set_Awaiting_Collection({ hireRequestId: hireRequestId, filterModel: this.filterModel() }));
    }
  }

  openDetailsDialog(hireRequest: HireRequest | null): void {
    this.selectedHireRequest = hireRequest;
    this.detailsDialogVisible = true
  }
}
