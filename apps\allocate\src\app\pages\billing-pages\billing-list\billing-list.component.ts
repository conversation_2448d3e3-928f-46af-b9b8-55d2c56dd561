import {
  ChangeDetectionStrategy,
  ChangeDetector<PERSON>ef,
  Component,
  DestroyRef,
  inject,
  OnInit,
  ViewChild,
} from '@angular/core';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Ng<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>Case,
  NgSwitchDefault,
} from '@angular/common';
import { RouterLink } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

import { Store } from '@ngrx/store';
import { Actions, ofType } from '@ngrx/effects';

import { Table, TableModule } from 'primeng/table';
import { InputTextModule } from 'primeng/inputtext';

import { billingsFeature } from '../../../store/features/billing.features';
import { billingState } from '../../../store/states/billing.state';
import { Billing } from '../../../shared/interfaces/billing.interface';
import { BillingsActions } from '../../../store/actions/billing.actions';
import { BillingService } from '../../../shared/services/billing.service';
import { HasPermissionDirective } from '../../../shared/directives/hasPermissions.directive';

import { CustomChipComponent } from 'libs/components/src/lib/components/custom-chip/custom-chip.component';
import { UserRole } from 'libs/auth/src/lib/constants/userRole.enum';
import { BillingTableFields } from '../../../shared/enums/billing-table-fields.enum';
import { InitializeBillingsTable } from '../../../shared/tables/billing.table';
import { PortalDirective } from 'libs/components/src/lib/directives/portal.directive';
import { BillingHistoryComponent } from '../../../components/billing/billing-history/billing-history.component';

@Component({
  selector: 'lha-billing-list',
  standalone: true,
  imports: [
    DatePipe,
    RouterLink,
    CustomChipComponent,
    HasPermissionDirective,
    NgClass,
    InputTextModule,
    TableModule,
    NgFor,
    NgSwitch,
    NgSwitchCase,
    NgSwitchDefault,
    NgClass,
    FormsModule,
    NgIf,
    PortalDirective,
    BillingHistoryComponent,
  ],
  templateUrl: './billing-list.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BillingListComponent implements OnInit {
  @ViewChild('table') table!: Table;

  private readonly store = inject(Store);
  private readonly actions = inject(Actions);
  private readonly destroyRef = inject(DestroyRef);
  private readonly cdr = inject(ChangeDetectorRef);

  listColumns = InitializeBillingsTable();
  billings: Billing[] = [];
  tableFields = BillingTableFields;
  userRole = UserRole;
  tableWidth = 1220;
  searchValue = '';
  loading = this.store.selectSignal(billingsFeature.selectLoading);

  billingService = inject(BillingService);

  ngOnInit(): void {
    this.store.dispatch(
      BillingsActions.update_Billing_Queries({ query: billingState.query })
    );

    this.actions
      .pipe(
        ofType(BillingsActions.load_Billings_Success),
        takeUntilDestroyed(this.destroyRef)
      )
      .subscribe(({ billings }) => {
        this.searchValue = '';
        this.billings = [...billings];
        this.table.reset();
        this.cdr.markForCheck();
      });
  }

  export(): void {
    this.store.dispatch(BillingsActions.export_Billing());
  }

  openBillingHistory(billing: Billing): void {
    this.store.dispatch(
      BillingsActions.change_visibility_billing_history_period({
        visible: true,
        billing,
      })
    );
  }
}
