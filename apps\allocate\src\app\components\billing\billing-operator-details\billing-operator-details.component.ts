import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  DestroyRef,
  effect,
  inject,
  Input,
  OnInit,
  signal,
  ViewChild,
} from '@angular/core';
import { Store } from '@ngrx/store';

import { BillingOperatorsActions } from '../../../store/actions/billing-operator.actions';
import {
  DatePipe,
  DecimalPipe,
  NgFor,
  NgIf,
  NgStyle,
  NgSwitch,
  NgSwitchCase,
  NgSwitchDefault,
} from '@angular/common';
import { CustomChipComponent } from 'libs/components/src/lib/components/custom-chip/custom-chip.component';
import { BillingVoyageCardComponent } from '../billing-voyage-card/billing-voyage-card.component';
import { Voyage } from 'libs/services/src/lib/services/voyages/interfaces/voyage.interface';
import { settingsFeature } from 'libs/services/src/lib/services/settings/shared/store/features/setting.features';
import { billingOperatorsFeature } from '../../../store/features/billing-operator.features';
import { billingsFeature } from '../../../store/features/billing.features';
import { InitializeBillingsDetailTable } from '../../../shared/tables/billing-detail.table';
import { BillingDetailTableFields } from '../../../shared/enums/billing-detail-table-fields.enum';
import { FormsModule } from '@angular/forms';
import { CardModule } from 'primeng/card';
import { Table, TableModule } from 'primeng/table';
import { InputTextModule } from 'primeng/inputtext';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { Actions, ofType } from '@ngrx/effects';
import { RouterLink } from '@angular/router';

@Component({
  standalone: true,
  selector: 'lha-billing-operator-details',
  templateUrl: './billing-operator-details.component.html',
  imports: [
    NgStyle,
    NgIf,
    BillingVoyageCardComponent,
    DatePipe,
    DecimalPipe,
    CustomChipComponent,
    FormsModule,
    CardModule,
    NgSwitch,
    NgSwitchCase,
    NgSwitchDefault,
    TableModule,
    NgFor,
    InputTextModule,
    RouterLink,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BillingOperatorDetailsComponent implements OnInit {
  @ViewChild('table') table!: Table;
  @Input() set clientId(value: string) {
    this.#clientId.set(value);
  }
  private readonly store = inject(Store);
  private readonly actions = inject(Actions);
  private readonly destroyRef = inject(DestroyRef);
  private readonly cdr = inject(ChangeDetectorRef);
  #clientId = signal('');

  billingPeriodId = this.store.selectSignal(
    billingsFeature.selectBillingPeriodId
  );
  appSettings = this.store.selectSignal(settingsFeature.selectAppSettings);

  billingOperatorVoyages = this.store.selectSignal(
    billingOperatorsFeature.selectBillingOperatorVoyages
  );
  billingOperator = this.store.selectSignal(
    billingOperatorsFeature.selectBillingOperator
  );

  listColumns = InitializeBillingsDetailTable();
  tableFields = BillingDetailTableFields;
  searchValue = '';
  loading = this.store.selectSignal(billingOperatorsFeature.selectLoading);
  voyages: Voyage[] = [];
  tableWidth = 1000;

  constructor() {
    effect(
      () => {
        if (this.billingPeriodId() && this.#clientId()) {
          this.store.dispatch(
            BillingOperatorsActions.load_Billing_Operator_Voyages({
              billingPeriodId: this.billingPeriodId(),
              clientId: this.#clientId(),
            })
          );
        }
      },
      {
        allowSignalWrites: true,
      }
    );
  }

  ngOnInit(): void {
    this.actions
      .pipe(
        ofType(BillingOperatorsActions.load_Billing_Operator_Voyages_Success),
        takeUntilDestroyed(this.destroyRef)
      )
      .subscribe(({ billingOperatorVoyages }) => {
        this.searchValue = '';
        this.voyages = [...billingOperatorVoyages.voyages];
        this.table.reset();
        this.cdr.markForCheck();
      });
  }

  export(): void {
    this.store.dispatch(
      BillingOperatorsActions.export_Billing_Operators({
        billingPeriodId: this.billingPeriodId(),
        clientId: this.#clientId(),
      })
    );
  }
}
