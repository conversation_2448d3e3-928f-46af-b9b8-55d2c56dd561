<div
  *ngIf="vm$ | async as vm"
  cdkDrag
  cdkDragRootElement=".cdk-overlay-pane"
  cdkDragHandle
  cdkDragBoundary=".cdk-overlay-container"
>
  <div mat-dialog-title>
    <h4>{{ isAdd ? 'Add ' : 'Edit ' }} Vessel Tank</h4>
  </div>
  <mat-dialog-content *lhaLoading="vm.loading.createEdit">
    <form [formGroup]="form" (ngSubmit)="saveVesselTank()">
      <div class="form__block">
        <div class="form__box">
          <mat-form-field appearance="outline" hideRequiredMarker="true">
            <mat-label>Vessel Tank Name</mat-label>
            <input matInput type="text" formControlName="name" />
            <mat-error *ngIf="form.controls.name.hasError('required')">
              Vessel Tank Name is required.
            </mat-error>
          </mat-form-field>
        </div>
        <div class="form__box">
          <lha-single-select
            [options]="vm.tankTypes"
            formControlName="tankTypeId"
            bindValue="tankTypeId"
            placeholder="Tank Type"
          />
          <div
            *ngIf="
              form.controls.tankTypeId.invalid &&
              form.controls.tankTypeId.touched
            "
          >
            <mat-error *ngIf="form.controls.tankTypeId.hasError('required')">
              Type is required.
            </mat-error>
          </div>
        </div>
        <div class="form__box">
          <lha-single-select
            [options]="vm.bulkTypes"
            formControlName="bulkTypeId"
            bindValue="bulkTypeId"
            placeholder="Bulk Type"
          />
          <div
            *ngIf="
              form.controls.bulkTypeId.invalid &&
              form.controls.bulkTypeId.touched
            "
          >
            <mat-error *ngIf="form.controls.bulkTypeId.hasError('required')">
              Bulk type is required.
            </mat-error>
          </div>
        </div>
        <div class="form__box">
          <mat-form-field appearance="outline" hideRequiredMarker="true">
            <mat-label>Quantity</mat-label>
            <input matInput type="number" formControlName="quantity" [decimal]="true">
            <mat-error *ngIf="form.controls.quantity.hasError('decimalPoint')">Decimal point should be less than 4</mat-error>
          </mat-form-field>
        </div>
        <div class="form__box">
          <mat-form-field appearance="outline" hideRequiredMarker="true">
            <mat-label>Tank Status Change Date</mat-label>
            <input
              matInput
              [ngxMatDatetimePicker]="tankStatusChangeDate"
              formControlName="tankStatusChangeDate"
            />
            <mat-datepicker-toggle
              matIconSuffix
              [for]="$any(tankStatusChangeDate)"
            ></mat-datepicker-toggle>
            <ngx-mat-datetime-picker
              #tankStatusChangeDate
            ></ngx-mat-datetime-picker>
            <mat-error
              *ngIf="form.controls.tankStatusChangeDate.hasError('required')"
              >Tank Status Change Date is required</mat-error
            >
          </mat-form-field>
        </div>
        <div class="form__box">
          <mat-form-field appearance="outline" hideRequiredMarker="true">
            <mat-label>Day Rate Price</mat-label>
            <input matInput type="number" formControlName="dayRatePrice" [decimal]="true">
            <mat-error *ngIf="form.controls.dayRatePrice.hasError('decimalPoint')">Decimal point should be less than 2</mat-error>
          </mat-form-field>
        </div>
      </div>
      <div class="form__box">
        <mat-slide-toggle formControlName="cleaned">Cleaned</mat-slide-toggle>
      </div>
      <mat-dialog-actions align="end">
        <button mat-raised-button color="primary" type="submit">
          {{ isAdd ? 'Add ' : 'Save ' }}
        </button>
        <button
          mat-raised-button
          color="warn"
          type="button"
          mat-dialog-close=""
        >
          Cancel
        </button>
      </mat-dialog-actions>
    </form>
  </mat-dialog-content>
</div>
