import { Actions, concatLatestFrom, createEffect, ofType } from '@ngrx/effects';
import { inject } from '@angular/core';
import { catchError, map, mergeMap, of } from 'rxjs';
import { HttpErrorResponse } from '@angular/common/http';
import { DeckUsageActions } from '../actions/deck-usage.actions';
import { DeckUsage } from '../../interfaces/deck-usage.interface';
import { Store } from '@ngrx/store';
import { deckUsagesFeature } from '../features';
import { filter, tap } from 'rxjs/operators';
import { FileService } from '../../../file.service';
import { DeckUsageService } from '../../deck-usage.service';
import { ActivatedRoute } from '@angular/router';
import { UtilityService } from '../../../utility.service';
import { Asset } from '../../../maintenance/interfaces/asset.interface';

export const loadDeckUsages = createEffect(
  (
    actions = inject(Actions),
    store = inject(Store),
    utilityService = inject(UtilityService),
    deckUsageService = inject(DeckUsageService),
    activatedRoute = inject(ActivatedRoute)
  ) => {
    return actions.pipe(
      ofType(
        DeckUsageActions.load_Deck_Usages,
        DeckUsageActions.load_Deck_Usage_Lists,
        DeckUsageActions.update_Deck_Usage_Queries,
        DeckUsageActions.remove_Deck_Usage_Success,
        DeckUsageActions.add_Deck_Usage_Success,
        DeckUsageActions.edit_Deck_Usage_Success
      ),
      filter(
        () =>
          !!utilityService.getParamsFromRoute(activatedRoute.snapshot.root)[
            'id'
          ]
      ),
      concatLatestFrom(() => store.select(deckUsagesFeature.selectQuery)),
      map(([, res]) => {
        return {
          query: res,
          id: utilityService.getParamsFromRoute(activatedRoute.snapshot.root)[
            'id'
          ],
        };
      }),
      mergeMap((action) =>
        deckUsageService.loadDeckUsageList(action.id, action.query).pipe(
          map((res: DeckUsage[]) =>
            DeckUsageActions.load_Deck_Usages_Success({ deckUsages: res })
          ),
          catchError((error: HttpErrorResponse) =>
            of(DeckUsageActions.load_Deck_Usages_Failure({ error }))
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const loadDeckUsageAssets = createEffect(
  (actions = inject(Actions), deckUsageService = inject(DeckUsageService)) => {
    return actions.pipe(
      ofType(DeckUsageActions.load_Deck_Usage_Lists),
      mergeMap(() =>
        deckUsageService.loadAssetList().pipe(
          map((res: Asset[]) =>
            DeckUsageActions.load_Assets_Success({
              assets: res,
            })
          ),
          catchError((error: HttpErrorResponse) =>
            of(DeckUsageActions.load_Assets_Failure({ error }))
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const removeDeckUsage = createEffect(
  (actions = inject(Actions), deckUsageService = inject(DeckUsageService)) => {
    return actions.pipe(
      ofType(DeckUsageActions.remove_Deck_Usage),
      mergeMap((action) =>
        deckUsageService.removeDeckUsage(action.id).pipe(
          map((res: DeckUsage) =>
            DeckUsageActions.remove_Deck_Usage_Success({
              deckUsage: res,
              successMessage: 'Cargo removed successfully!',
            })
          ),
          catchError((error: HttpErrorResponse) =>
            of(DeckUsageActions.remove_Deck_Usage_Failure({ error: error }))
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const addDeckUsage = createEffect(
  (actions = inject(Actions), deckUsageService = inject(DeckUsageService)) => {
    return actions.pipe(
      ofType(DeckUsageActions.add_Deck_Usage),
      mergeMap((action) =>
        deckUsageService.addDeckUsage(action.deckUsage).pipe(
          map((res: DeckUsage) =>
            DeckUsageActions.add_Deck_Usage_Success({
              deckUsage: res,
              successMessage: 'Cargo added successfully!',
            })
          ),
          catchError((error: HttpErrorResponse) =>
            of(DeckUsageActions.add_Deck_Usage_Failure({ error: error }))
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const editDeckUsage = createEffect(
  (actions = inject(Actions), deckUsageService = inject(DeckUsageService)) => {
    return actions.pipe(
      ofType(DeckUsageActions.edit_Deck_Usage),
      mergeMap((action) =>
        deckUsageService
          .editDeckUsage(action.deckUsageId, action.deckUsage)
          .pipe(
            map((res: DeckUsage) =>
              DeckUsageActions.edit_Deck_Usage_Success({
                deckUsage: res,
                successMessage: 'Cargo edited successfully!',
              })
            ),
            catchError((error: HttpErrorResponse) =>
              of(DeckUsageActions.edit_Deck_Usage_Failure({ error: error }))
            )
          )
      )
    );
  },
  {
    functional: true,
  }
);

export const exportDeckUsages = createEffect(
  (
    actions = inject(Actions),
    deckUsageService = inject(DeckUsageService),
    fileService = inject(FileService),
    utilityService = inject(UtilityService),
    activatedRoute = inject(ActivatedRoute)
  ) => {
    return actions.pipe(
      ofType(DeckUsageActions.export_Deck_Usages),
      mergeMap((action) => {
        const params = utilityService.getParamsFromRoute(
          activatedRoute.snapshot.root
        );
        const fileName =
          action.deckUsageType === 'In' ? 'Deck Usages In' : 'Deck Usages Out';
        return deckUsageService
          .exportDeckUsages(params['id'], action.deckUsageType)
          .pipe(
            map((res: ArrayBuffer) => {
              fileService.downloadFile(res, fileName);
              return DeckUsageActions.export_Deck_Usage_Success();
            }),
            catchError((error: HttpErrorResponse) =>
              of(DeckUsageActions.export_Deck_Usage_Failure({ error }))
            )
          );
      })
    );
  },
  {
    functional: true,
  }
);
