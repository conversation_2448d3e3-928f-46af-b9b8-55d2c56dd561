import { Component, inject, Input } from '@angular/core';
import { Voyage } from 'libs/services/src/lib/services/voyages/interfaces/voyage.interface';
import { RouterLink } from '@angular/router';
import { DatePipe, NgIf } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { Store } from '@ngrx/store';
import { VoyagesActions } from 'libs/services/src/lib/services/voyages/store/actions/voyages.actions';
import { ImageAuthDirective } from '../../../shared/directives/image-auth.directive';
import { Environment } from 'env';

@Component({
  selector: 'lha-voyage-card',
  standalone: true,
  imports: [RouterLink, DatePipe, MatIconModule, NgIf, ImageAuthDirective],
  templateUrl: './voyage-card.component.html',
  styleUrls: ['./voyage-card.component.scss'],
})
export class VoyageCardComponent {
  @Input() voyage!: Voyage;
  environment = inject(Environment);
  url = this.environment.apiUrl;
  store = inject(Store);

  showImgView(): void {
    if (!this.voyage.vessel.vesselPictureId) {
      return;
    }
    this.store.dispatch(
      VoyagesActions.open_Voyage_Image_View_Dialog({
        imgUrl:
          this.url + '/api/vessel/photo/' + this.voyage.vessel.vesselPictureId,
      })
    );
  }
}
