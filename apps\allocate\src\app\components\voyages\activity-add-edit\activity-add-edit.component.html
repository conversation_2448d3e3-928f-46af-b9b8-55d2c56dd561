<p-dialog
  [draggable]="true"
  [closable]="true"
  [modal]="true"
  [visible]="isVisibleDialog()"
  [style]="{ width: '640px' }"
>
  <ng-template pTemplate="closeicon">
    <button
      class="p-dialog-header-icon p-dialog-header-close p-link"
      (click)="hideDialog()"
    >
      <span class="pi pi-times"></span>
    </button>
  </ng-template>
  <ng-template pTemplate="header">
    <div class="header">{{ isEdit() ? 'Edit' : 'Create' }} Activity</div>
  </ng-template>
  <ng-template pTemplate="content">
    <form [formGroup]="form">
      <div class="d-flex flex-wrap gap-20 mb-16">
        <div class="flex-1 d-flex flex-direction-column gap-16">
          <label class="f-bold">
            Activity <span style="color: red">*</span>
          </label>
          <p-dropdown
            [options]="activitiesState().activityTypes"
            [filter]="true"
            formControlName="activityId"
            optionLabel="code"
            optionValue="activityId"
            [showClear]="true"
            appendTo="body"
            panelStyleClass="new-version-panel"
            styleClass="new-version"
            (onClear)="clearActivity()"
          />
          <small
            class="validation-control-error"
            *ngIf="controls.activityId?.invalid && controls.activityId?.touched"
          >
            Activity is required.
          </small>
        </div>

        <div class="flex-1 d-flex flex-direction-column gap-16">
          <label class="f-bold">
            Asset <span style="color: red">*</span>
          </label>
          <p-dropdown
            [options]="filteredAssets()"
            [filter]="true"
            formControlName="assetId"
            optionLabel="name"
            optionValue="assetId"
            [showClear]="true"
            appendTo="body"
            panelStyleClass="new-version-panel"
            styleClass="new-version"
            (onClear)="clearAsset()"
          />
          <small
            class="validation-control-error"
            *ngIf="controls.assetId?.invalid && controls.assetId?.touched"
          >
            Asset is required.
          </small>
        </div>
      </div>

      <div class="d-flex flex-wrap gap-20 mb-16">
        <div class="flex-1 d-flex flex-direction-column gap-16">
          <label class="f-bold">
            Start Date <span style="color: red">*</span>
          </label>
          <p-calendar
            id="startDateTime"
            formControlName="startDateTime"
            [showIcon]="true"
            [showTime]="true"
            placeholder="Start Date"
            dateFormat="dd/mm/yy"
            appendTo="body"
            [maxDate]="controls.endDateTime?.value!"
          ></p-calendar>
          <small
            class="validation-control-error"
            *ngIf="
              controls.startDateTime?.invalid && controls.startDateTime?.touched
            "
          >
            Start Date is required.
          </small>
        </div>

        <div class="flex-1 d-flex flex-direction-column gap-16">
          <label class="f-bold">
            End Date <span style="color: red">*</span>
          </label>
          <p-calendar
            id="endDateTime"
            formControlName="endDateTime"
            [showIcon]="true"
            [showTime]="true"
            placeholder="End Date"
            dateFormat="dd/mm/yy"
            appendTo="body"
            [minDate]="controls.startDateTime?.value!"
          ></p-calendar>
          <small
            class="validation-control-error"
            *ngIf="
              controls.endDateTime?.invalid && controls.endDateTime?.touched
            "
          >
            End Date is required.
          </small>
        </div>
      </div>

      <div class="d-flex flex-wrap gap-20 mb-16">
        <div class="flex-1 d-flex flex-direction-column gap-16">
          <label class="f-bold"> Deck Space Used </label>
          <p-inputNumber
            type="number"
            [maxFractionDigits]="2"
            formControlName="deckSpaceUsed"
          />
          <small
            class="validation-control-error"
            *ngIf="form.controls.deckSpaceUsed.hasError('greaterThan')"
          >
            Deck Space Used should be more than 0
          </small>
        </div>

        <div class="flex-1 d-flex flex-direction-column gap-16">
          <label class="f-bold"> Comments </label>
          <input pInputText type="text" formControlName="comments" />
        </div>
      </div>

      <div *ngIf="isShowBilledAsset" class="d-flex flex-wrap gap-20 mb-16">
        <div class="flex-1 d-flex flex-direction-column gap-16 fixed-width">
          <label class="f-bold"
            >Billed Asset <span style="color: red">*</span></label
          >
          <p-dropdown
            [options]="activitiesState().offshoreAndMobileAssets"
            [filter]="true"
            formControlName="billedAssetId"
            optionLabel="name"
            optionValue="assetId"
            [showClear]="true"
            appendTo="body"
            panelStyleClass="new-version-panel"
            styleClass="new-version"
            (onClear)="clearBilledAsset()"
          />
          <small
            class="validation-control-error"
            *ngIf="
              controls.billedAssetId?.invalid && controls.billedAssetId?.touched
            "
          >
            Billed Asset is required.
          </small>
        </div>
      </div>
    </form>
  </ng-template>

  <ng-template pTemplate="footer">
    <div class="actions">
      <button class="btn-tertiary" type="button" (click)="hideDialog()">
        Cancel
      </button>
      <button
        [disabled]="loading().createEdit"
        class="btn-primary"
        type="button"
        (click)="saveActivity()"
      >
        <ng-container *ngIf="loading().createEdit; else saveText">
          <p-progressSpinner
            [styleClass]="'small-spinner-style-btn-white'"
          ></p-progressSpinner>
        </ng-container>
        <ng-template #saveText>Save</ng-template>
      </button>
    </div>
  </ng-template>
</p-dialog>
