import { inject, Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { BillingOperator } from '../interfaces/billing-operator.interface';
import { BillingOperatorView } from '../../shared/interfaces/billing-operator-view.interface';

@Injectable({
  providedIn: 'root',
})
export class BillingOperatorService {
  private readonly http = inject(HttpClient);

  getBillingOperatorList(billingPeriodId: string): Observable<BillingOperator[]> {
    return this.http.get<BillingOperator[]>(`/api/clientbillingperiod/${billingPeriodId}`);
  }

  loadClientBillingPeriodListByOperatorId(
    billingPeriodId: string,
    clientId: string
  ): Observable<BillingOperatorView> {
    return this.http.post<BillingOperatorView>(
      '/api/clientbillingperiod/clientid',
      {
        clientId,
        billingPeriodId,
      }
    );
  }

  exportBillingOperator(id: string, clientId: string): Observable<ArrayBuffer> {
    return this.http.get(
      `/api/clientbillingperiod/exportbillingoperator/${id}/${clientId}`,
      {
        responseType: 'arraybuffer',
      }
    );
  }
}
