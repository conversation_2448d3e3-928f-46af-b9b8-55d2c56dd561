import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from '@angular/common';
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  DestroyRef,
  inject,
  OnInit,
  ViewChild,
} from '@angular/core';
import { FormsModule } from '@angular/forms';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

import { Actions, ofType } from '@ngrx/effects';
import { Store } from '@ngrx/store';

import { InputTextModule } from 'primeng/inputtext';
import { Table, TableModule } from 'primeng/table';
import { InputSwitchModule } from 'primeng/inputswitch';

import { Operator } from 'libs/services/src/lib/services/maintenance/interfaces/operator.interface';
import { OperatorActions } from 'libs/services/src/lib/services/maintenance/store/actions/operators.actions';
import { CustomChipComponent } from 'libs/components/src/lib/components/custom-chip/custom-chip.component';
import { InitializeOperatorsTable } from './helper/operators-table';
import { OperatorsTableFields } from './helper/operators-table-fields.enum';
import { OperatorsAddEditComponent } from './components/operators-add-edit/operators-add-edit.component';
import { OperatorsHistoryDialogComponent } from './components/operators-history/operators-history-dialog';

@Component({
  selector: 'md-operators',
  standalone: true,
  templateUrl: './operators.page.html',
  imports: [
    FormsModule,
    InputTextModule,
    TableModule,
    NgFor,
    NgSwitch,
    NgSwitchCase,
    NgSwitchDefault,
    NgClass,
    FormsModule,
    NgIf,
    DatePipe,
    CustomChipComponent,
    OperatorsAddEditComponent,
    OperatorsHistoryDialogComponent,
    InputSwitchModule,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class OperatorsPage implements OnInit {
  @ViewChild('table') table!: Table;
  private readonly store = inject(Store);
  private readonly actions = inject(Actions);
  private readonly destroyRef = inject(DestroyRef);
  private readonly cdr = inject(ChangeDetectorRef);

  searchValue = '';
  tableWidth = 1700;
  listColumns = InitializeOperatorsTable();
  operators: Operator[] = [];
  tableFields = OperatorsTableFields;

  ngOnInit() {
    this.store.dispatch(OperatorActions.init_operators());

    this.actions
      .pipe(
        ofType(OperatorActions.load_Operators_Success),
        takeUntilDestroyed(this.destroyRef)
      )
      .subscribe(({ operators }) => {
        this.searchValue = '';
        this.operators = [...operators];
        this.table.reset();
        this.cdr.markForCheck();
      });
  }

  create() {
    this.store.dispatch(
      OperatorActions.change_visibility_add_edit({
        visible: true,
        operator: null,
      })
    );
  }

  edit(operator: Operator) {
    this.store.dispatch(
      OperatorActions.change_visibility_add_edit({
        visible: true,
        operator,
      })
    );
  }

  openHistoryDialog(operator: Operator) {
    this.store.dispatch(
      OperatorActions.change_visibility_history({
        visible: true,
        operator,
      })
    );
  }
}
