import { Route } from '@angular/router';
import { authGuard } from 'libs/auth/src/lib/guards/auth.guard';

const PrivateRoutingLoader = () =>
  import('./app.routes').then((m) => m.appRoutes);

export const AllocateWebRoutes: Route[] = [
  {
    path: '',
    loadChildren: PrivateRoutingLoader,
    canActivateChild: [authGuard],
  },
  { path: '', redirectTo: 'allocate', pathMatch: 'full' },
  { path: '**', redirectTo: 'allocate' },
];
