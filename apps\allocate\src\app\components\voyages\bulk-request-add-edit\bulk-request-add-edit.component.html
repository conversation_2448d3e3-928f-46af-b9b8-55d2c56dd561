<div
  *ngIf="vm$ | async as vm"
  cdkDrag
  cdkDragRootElement=".cdk-overlay-pane"
  cdkDragHandle
  cdkDragBoundary=".cdk-overlay-container"
>
  <div mat-dialog-title>
    <h4>{{ isAdd ? 'Add ' : 'Edit ' }} Bulk Request</h4>
  </div>
  <mat-dialog-content *lhaLoading="vm.loading.createEdit">
    <form [formGroup]="form" (ngSubmit)="saveBulkRequest()">
      <div class="form__block">
        <div class="form__box">
          <lha-single-select
            [options]="vm.bulkTypesList"
            formControlName="bulkTypeId"
            bindValue="bulkTypeId"
            placeholder="Select Bulk Type"
          />
          <div
            *ngIf="
              form.controls.bulkTypeId.invalid &&
              form.controls.bulkTypeId.touched
            "
          >
            <mat-error *ngIf="form.controls.bulkTypeId.hasError('required')">
              Bulk Type is required.
            </mat-error>
          </div>
        </div>
        <div class="form__box">
          <lha-single-select
            [options]="vm.assets"
            formControlName="billedAssetId"
            bindValue="assetId"
            placeholder="Select Billed Asset"
          />
          <div
            *ngIf="
              form.controls.billedAssetId.invalid &&
              form.controls.billedAssetId.touched
            "
          >
            <mat-error *ngIf="form.controls.billedAssetId.hasError('required')">
              Billed Asset is required.
            </mat-error>
          </div>
        </div>
        <div class="form__box">
          <mat-form-field appearance="outline" hideRequiredMarker="true">
            <mat-label>Quantity</mat-label>
            <input
              matInput
              formControlName="quantity"
              type="number"
              [decimal]="true"
            />
            <mat-error *ngIf="form.controls.quantity.hasError('required')">
              Quantity is required.
            </mat-error>
            <mat-error *ngIf="form.controls.quantity.hasError('greaterThan')">
              Quantity must be a number greater than 0.
            </mat-error>
            <mat-error *ngIf="form.controls.quantity.hasError('decimalPoint')"
              >Decimal point should be less than 4</mat-error
            >
          </mat-form-field>
        </div>
      </div>
      <mat-dialog-actions align="end">
        <button mat-raised-button color="primary" type="submit">
          {{ isAdd ? 'Add ' : 'Save ' }}
        </button>
        <button
          mat-raised-button
          color="warn"
          type="button"
          mat-dialog-close=""
        >
          Cancel
        </button>
      </mat-dialog-actions>
    </form>
  </mat-dialog-content>
</div>
