import {
  ChangeDetectionStrategy,
  Component,
  inject,
  Input,
  OnInit,
} from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { Store } from '@ngrx/store';

import { BillingsActions } from '../../../store/actions/billing.actions';
import { BillingHeaderComponent } from '../../../components/billing/billing-header/billing-header.component';

@Component({
  selector: 'lha-billing-details',
  standalone: true,
  imports: [BillingHeaderComponent, RouterOutlet],
  templateUrl: './billing-details.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BillingDetailsComponent implements OnInit {
  @Input() billingPeriodId = '';
  private readonly store = inject(Store);

  ngOnInit(): void {
    this.store.dispatch(BillingsActions.init_Billing_Detail({billingPeriodId: this.billingPeriodId}));
  }
}
