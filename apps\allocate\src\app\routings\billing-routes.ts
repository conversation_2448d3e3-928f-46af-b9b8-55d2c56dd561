import { Route } from '@angular/router';
import { OverviewComponent } from '../components/billing/overview/overview.component';
import { BillingOperatorComponent } from '../components/billing/billing-operator/billing-operator.component';
import { BillingVesselComponent } from '../components/billing/billing-vessel/billing-vessel.component';
import { BillingOperatorDetailsComponent } from '../components/billing/billing-operator-details/billing-operator-details.component';
import { BillingVesselDetailsComponent } from '../components/billing/billing-vessel-details/billing-vessel-details.component';

export const BillingRoutes: Route[] = [
  {
    path: '',
    loadComponent: () =>
      import('../pages/billing-pages/billing-list/billing-list.component').then(
        (c) => c.BillingListComponent
      ),
  },
  {
    path: ':billingPeriodId',
    loadComponent: () =>
      import(
        '../pages/billing-pages/billing-details/billing-details.component'
      ).then((c) => c.BillingDetailsComponent),
    children: [
      { path: '', redirectTo: 'overview', pathMatch: 'full' },
      {
        path: 'overview',
        component: OverviewComponent,
      },
      {
        path: 'operator',
        component: BillingOperatorComponent,
        children: [
          {
            path: ':clientId',
            component: BillingOperatorDetailsComponent,
          },
        ],
      },

      {
        path: 'vessel',
        component: BillingVesselComponent,
        children: [
          {
            path: ':vesselId',
            component: BillingVesselDetailsComponent,
          },
        ],
      },
    ],
  },
];
