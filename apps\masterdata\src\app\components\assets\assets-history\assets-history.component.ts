import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { NgIf } from '@angular/common';

import { Store } from '@ngrx/store';

import { DialogModule } from 'primeng/dialog';
import { TableModule } from 'primeng/table';
import { TabViewModule } from 'primeng/tabview';

import { assetsMDFeature } from '../../../store/features/assets.feature';
import { AssetsMDActions } from '../../../store/actions/assets.action';

import { AssetClientsHistoryTableComponent } from '../asset-clients-history-table/asset-clients-history-table.component';
import { AssetsMobileWellComponent } from '../assets-mobile-well/assets-mobile-well.component';
import { ClustersHistoryTableComponent } from '../clusters-history-table/clusters-history-table.component';

@Component({
  standalone: true,
  selector: 'md-assets-history',
  templateUrl: './assets-history.component.html',
  imports: [
    DialogModule,
    TableModule,
    NgIf,
    TabViewModule,
    AssetClientsHistoryTableComponent,
    AssetsMobileWellComponent,
    ClustersHistoryTableComponent,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AssetsHistoryComponent {
  private readonly store = inject(Store);
  isVisible = this.store.selectSignal(assetsMDFeature.selectIsVisibleHistory);
  asset = this.store.selectSignal(assetsMDFeature.selectAsset);

  hideDialog() {
    this.store.dispatch(
      AssetsMDActions.change_visibility_history_assets_page({
        visible: false,
        asset: null,
      })
    );

    this.store.dispatch(
      AssetsMDActions.change_visibility_history_clusters_page({
        visible: false,
        cluster: null,
      })
    );
  }
}
