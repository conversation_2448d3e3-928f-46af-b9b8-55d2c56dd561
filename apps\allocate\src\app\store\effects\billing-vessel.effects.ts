import { Actions, createEffect, ofType } from '@ngrx/effects';
import { inject } from '@angular/core';
import { catchError, map, mergeMap, of, switchMap } from 'rxjs';
import { HttpErrorResponse } from '@angular/common/http';
import { BillingVesselsActions } from '../actions/billing-vessel.actions';
import { FileService } from 'libs/services/src/lib/services/file.service';
import { BillingVesselService } from '../../shared/services/billing-vessel.service';
import { Vessel } from 'libs/services/src/lib/services/vessels/interfaces/vessel.interface';
import { BillingsActions } from '../actions/billing.actions';

export const loadBillingVesselList = createEffect(
  (actions = inject(Actions), service = inject(BillingVesselService)) => {
    return actions.pipe(
      ofType(
        BillingVesselsActions.load_Billing_Vessels,
        BillingsActions.init_Billing_Detail
      ),
      mergeMap(({ billingPeriodId }) =>
        service.loadBillingVesselList(billingPeriodId).pipe(
          map((res: Vessel[]) =>
            BillingVesselsActions.load_Billing_Vessels_Success({
              billingVessels: res,
            })
          ),
          catchError((error: HttpErrorResponse) =>
            of(
              BillingVesselsActions.load_Billing_Vessels_Failure({
                error: error,
              })
            )
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const loadBillingVesselVoyageList = createEffect(
  (actions = inject(Actions), service = inject(BillingVesselService)) => {
    return actions.pipe(
      ofType(BillingVesselsActions.load_Billing_Vessel_Voyages),
      mergeMap(({ billingPeriodId, vesselId }) =>
        service.loadBillingVesselVoyageList(billingPeriodId, vesselId).pipe(
          map((billingVesselVoyages) =>
            BillingVesselsActions.load_Billing_Vessel_Voyages_Success({
              billingVesselVoyages,
            })
          ),
          catchError((error: HttpErrorResponse) =>
            of(
              BillingVesselsActions.load_Billing_Vessel_Voyages_Failure({
                error,
              })
            )
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const loadBillingVesselVoyageListAfterBillingPeriodChange = createEffect(
  (actions = inject(Actions)) => {
    return actions.pipe(
      ofType(BillingVesselsActions.change_Billing_Period_Success),
      switchMap(({ billingPeriodId, vesselId }) =>
        of(
          BillingVesselsActions.load_Billing_Vessel_Voyages({
            billingPeriodId,
            vesselId,
          })
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const changeVoyageBillingPeriod = createEffect(
  (
    actions = inject(Actions),
    billingOperatorService = inject(BillingVesselService)
  ) => {
    return actions.pipe(
      ofType(BillingVesselsActions.change_Billing_Period),
      mergeMap(({ billingPeriod, voyageId, billingPeriodId, vesselId }) =>
        billingOperatorService
          .changeVoyageBillingPeriod(billingPeriod, voyageId)
          .pipe(
            map(() =>
              BillingVesselsActions.change_Billing_Period_Success({
                successMessage: 'Voyage moved billing period successfully!',
                billingPeriodId,
                vesselId,
              })
            ),
            catchError((error: HttpErrorResponse) =>
              of(
                BillingVesselsActions.change_Billing_Period_Failure({
                  error: error,
                })
              )
            )
          )
      )
    );
  },
  {
    functional: true,
  }
);

export const exportBillingVessel = createEffect(
  (
    actions = inject(Actions),
    service = inject(BillingVesselService),
    fileService = inject(FileService)
  ) => {
    return actions.pipe(
      ofType(BillingVesselsActions.export_Billing_Vessel),

      mergeMap(({ billingPeriodId, vesselId }) =>
        service.exportBillingVessel(billingPeriodId, vesselId).pipe(
          map((res: ArrayBuffer) => {
            fileService.downloadFile(res, 'Billing Period Vessel');
            return BillingVesselsActions.export_Billing_Vessel_Success();
          }),
          catchError((error: HttpErrorResponse) =>
            of(BillingVesselsActions.export_Billing_Vessel_Failure({ error }))
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);
