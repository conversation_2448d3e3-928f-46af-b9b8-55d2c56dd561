import {
  Component,
  computed,
  DestroyRef,
  effect,
  inject,
  Input,
  OnInit,
} from '@angular/core';
import {
  ActivatedRoute,
  NavigationEnd,
  Router,
  RouterOutlet,
} from '@angular/router';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

import { Store } from '@ngrx/store';
import { PanelMenuModule } from 'primeng/panelmenu';
import { MenuItem } from 'primeng/api';

import { billingOperatorsFeature } from '../../../store/features/billing-operator.features';

@Component({
  selector: 'lha-billing-operator',
  standalone: true,
  imports: [PanelMenuModule, RouterOutlet],
  templateUrl: './billing-operator.component.html',
})
export class BillingOperatorComponent implements OnInit {
  @Input() billingPeriodId = '';
  private readonly store = inject(Store);
  private readonly destroyRef = inject(DestroyRef);
  private readonly activatedRoute = inject(ActivatedRoute);
  private readonly router = inject(Router);

  operators = this.store.selectSignal(
    billingOperatorsFeature.selectBillingOperators
  );

  navItems = computed<MenuItem[]>(() => {
    return this.operators().map((operator) => {
      return {
        label: operator.clientName,
        routerLink: `${operator.clientId}`,
        permissions: [],
      };
    });
  });

  constructor() {
    effect(
      () => {
        if (
          !this.activatedRoute.children[0]?.snapshot.params['clientId'] &&
          this.operators().length
        ) {
          this.router.navigate([this.operators()[0].clientId], {
            relativeTo: this.activatedRoute,
          });
        }
      },
      {
        allowSignalWrites: true,
      }
    );
  }

  ngOnInit(): void {
    this.router.events
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((event) => {
        if (event instanceof NavigationEnd) {
          if (
            !this.activatedRoute.children[0]?.snapshot.params['clientId'] &&
            this.operators().length
          ) {
            this.router.navigate([this.operators()[0].clientId], {
              relativeTo: this.activatedRoute,
            });
          }
        }
      });
  }
}
