{
  "extends": "./tsconfig.json",
  "include": [
    "src/**/*.ts", "../../libs/components/src/lib/pages/distances/helper/distances-table.ts", "../../libs/components/src/lib/pages/distances/helper/distances-table-fields.enum.ts", "../../libs/components/src/lib/pages/activity-categories/helper/activity-categories-table.ts", "../../libs/components/src/lib/pages/activity-categories/helper/activity-categories-table-fields.enum.ts", "../../libs/components/src/lib/pages/activities/helper/activities-table.ts", "../../libs/components/src/lib/pages/activities/helper/activities-table-fields.enum.ts", "../../libs/components/src/lib/pages/activities/helper/activity-status.enum.ts", "../allocate/src/app/allocate.routes.ts",
  ],
  "compilerOptions": {},
  "exclude": [
    "jest.config.ts",
    "src/**/*.test.ts",
    "src/**/*.spec.ts"
  ]
}