<div class="mt-10">
  <div class="table__content">
    <p-table
      #table
      [columns]="dynamicColumns()"
      [paginator]="true"
      [loading]="activityState().loading.list"
      [value]="activityState().activities"
      [rows]="10"
      [rowsPerPageOptions]="[5, 10, 20, 50]"
      paginatorDropdownAppendTo="body"
      scrollHeight="530px"
      [scrollable]="true"
      [globalFilterFields]="[
        'activityCode',
        'assetName',
        'billedAssetName',
        'clientName'
      ]"
    >
      <ng-template pTemplate="caption">
        <div class="d-flex justify-content-between align-items-center pb-20">
          <h1>Vessel Activities</h1>
          <div class="d-flex gap-8">
            <span class="p-input-icon-left">
              <em class="pi pi-search"></em>
              <input
                type="text"
                pInputText
                formControlName="search"
                (input)="applyGlobalFilter($event, table, false)"
                placeholder="Search..."
              />
            </span>
            <button
              class="btn-secondary"
              type="button"
              (click)="addEditActivity(null, false)"
              [disabled]="voyage()?.isCompleted"
            >
              Add Activity
            </button>
            <button
              class="btn-secondary"
              type="button"
              (click)="exportVesselActivities()"
            >
              Export
            </button>
          </div>
        </div>
      </ng-template>
      <ng-template pTemplate="header" let-columns>
        <tr>
          <th *ngFor="let column of columns" scope="col">
            <span>{{ column.name }}</span>
          </th>
        </tr>
      </ng-template>

      <ng-template let-index="rowIndex" pTemplate="body" let-item>
        <tr>
          <td>
            {{ item.index }}
          </td>
          <td>
            <i
              class="pi pi-exclamation-circle invalid-icon"
              *ngIf="
                item.invalidErrors ||
                item.timeGapNotOk ||
                item.parallelActivityNotOk
              "
              (click)="showInvalidErrors($event, item, op)"
              (mouseleave)="hideInvalidErrors(op)"
            ></i>
          </td>
          <td>
            <lha-custom-chip
              [size]="140"
              [cssClass]="item.activity.type"
              [text]="item.activityCode"
            ></lha-custom-chip>
          </td>
          <td>{{ item.parallelActivitiesList }}</td>
          <td>{{ item.assetName }}</td>
          <td>{{ item.clientName }}</td>
          <td>{{ item.billedAssetName }}</td>
          <td>{{ item.startDateTime | date : 'dd/MM/yyyy HH:mm' }}</td>
          <td>{{ item.endDateTime | date : 'dd/MM/yyyy HH:mm' }}</td>
          <td>{{ item.duration | number : '1.0-4' }}</td>
          <td>{{ item.createdDate | date : 'dd/MM/yyyy HH:mm' }}</td>
          <td>{{ item.comments }}</td>
          <td *ngIf="!voyage()?.isCompleted">
            <i class="pi pi-pencil" (click)="addEditActivity(item, true)"></i>
            <i
              class="ml-10 pi pi-trash"
              (click)="removeActivity(item, activityState().activities)"
            ></i>
          </td>
        </tr>
      </ng-template>
    </p-table>
    <p-overlayPanel #op (mouseleave)="hideInvalidErrors(op)">
      <div class="p-10">
        <ul *ngIf="invalidErrors?.length">
          <li *ngFor="let error of invalidErrors">
            {{ error.Error }}
          </li>
        </ul>
      </div>
    </p-overlayPanel>
  </div>
</div>

<lha-activity-add-edit></lha-activity-add-edit>
