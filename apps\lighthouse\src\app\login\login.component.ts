import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from '@angular/common';
import { Component, inject } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { AuthActions } from '../../../../../libs/auth/src/lib/store/auth.actions';
import { authFeature } from '../../../../../libs/auth/src/lib/store/auth.feature';
import { Constants } from '../application-config/email-pattern';

@Component({
  selector: 'lha-login',
  standalone: true,
  imports: [
    AsyncPipe,
    NgIf,
    NgSwitch,
    ReactiveFormsModule,
    MatCardModule,
    MatButtonModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    MatProgressSpinnerModule,
  ],
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss'],
})
export class LoginComponent {
  router = inject(Router);
  store = inject(Store);
  constants = inject(Constants);
  loggedIn$ = this.store.select(authFeature.selectIsLoggedIn);

  private orgNameToIdMap: { [key: string]: string } = {
    'peterson': 'org_ft9vbxWc0raosHjb',
    'stcinsiso': 'org_zvfHtiPHBoEACT3Z',
    'lighthousewebdev' : 'org_zvfHtiPHBoEACT3Z',
    'stcinsisodev' : 'org_vQWdKfoHqzcCUzo4',
    'peldev' : 'org_HSWQWMqE7WUzmgLK',
  };

  private orgIdToOrgName(orgMap: { [key: string]: string }): { [key: string]: string } {
    const reversedMap: { [key: string]: string } = {};

    Object.entries(orgMap).forEach(([key, value]) => {
      reversedMap[value] = key;
    });

    return reversedMap;
  }

  getSubdomain(): string | null {
    const hostnameParts = window.location.hostname.split('.');
    return hostnameParts[0];
  }

  loginWithRedirect() {
    const subdomain = this.getSubdomain()?.toLocaleLowerCase();
    const orgId = subdomain ? this.orgNameToIdMap[subdomain] : undefined;
    this.store.dispatch(AuthActions.login({ orgId: orgId}));
  }
}
