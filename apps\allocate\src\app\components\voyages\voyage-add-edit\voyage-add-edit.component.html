<p-dialog
  [draggable]="true"
  [closable]="true"
  [modal]="true"
  [visible]="isVisibleDialog()"
  [style]="{ width: '640px' }"
>
  <ng-template pTemplate="closeicon">
    <button
      class="p-dialog-header-icon p-dialog-header-close p-link"
      (click)="hideDialog()"
    >
      <span class="pi pi-times"></span>
    </button>
  </ng-template>
  <ng-template pTemplate="header">
    <div class="header">{{ isEdit() ? 'Edit' : 'Create' }} voyage</div>
  </ng-template>
  <ng-template pTemplate="content">
    <form [formGroup]="form">
      <div class="d-flex flex-wrap gap-20 mb-16">
        <div class="flex-1 d-flex flex-direction-column gap-16">
          <label class="f-bold">
            Voyage Number <span style="color: red">*</span>
          </label>
          <input pInputText type="text" formControlName="voyageNumber" />
          <small
            class="validation-control-error"
            *ngIf="
              controls.voyageNumber?.invalid && controls.voyageNumber?.touched
            "
          >
            Voyage Number is required.
          </small>
        </div>

        <div class="flex-1 d-flex flex-direction-column gap-16">
          <label class="f-bold">
            Vessel <span style="color: red">*</span>
          </label>
          <p-dropdown
            [options]="voyageState().vessels"
            [filter]="true"
            formControlName="vesselId"
            optionLabel="name"
            optionValue="vesselId"
            [showClear]="true"
            appendTo="body"
            panelStyleClass="new-version-panel"
            styleClass="new-version"
          />
          <small
            class="validation-control-error"
            *ngIf="controls.vesselId?.invalid && controls.vesselId?.touched"
          >
            Vessel is required.
          </small>
        </div>
      </div>

      <div class="d-flex flex-wrap gap-20 mb-16">
        <div class="flex-1 d-flex flex-direction-column gap-16">
          <label class="f-bold">
            Voyage Types <span style="color: red">*</span>
          </label>
          <p-dropdown
            [options]="voyageTypes"
            [filter]="true"
            formControlName="voyageType"
            optionLabel="name"
            optionValue="value"
            [showClear]="true"
            appendTo="body"
            panelStyleClass="new-version-panel"
            styleClass="new-version"
          />
          <small
            class="validation-control-error"
            *ngIf="controls.voyageType?.invalid && controls.voyageType?.touched"
          >
            Voyage Type is required.
          </small>
        </div>

        <div class="flex-1 d-flex flex-direction-column gap-16">
          <label class="f-bold">
            Initial Port <span style="color: red">*</span>
          </label>
          <p-dropdown
            [options]="voyageState().ports"
            [filter]="true"
            formControlName="initialAssetId"
            optionLabel="name"
            optionValue="assetId"
            [showClear]="true"
            appendTo="body"
            panelStyleClass="new-version-panel"
            styleClass="new-version"
          />
          <small
            class="validation-control-error"
            *ngIf="
              controls.initialAssetId?.invalid &&
              controls.initialAssetId?.touched
            "
          >
            Initial Port is required.
          </small>
        </div>
      </div>

      <div class="d-flex flex-wrap gap-20 mb-16">
        <div class="flex-1 d-flex flex-direction-column gap-16">
          <label class="f-bold">
            Deck Percentage Used In <span style="color: red">*</span>
          </label>
          <input
            pInputText
            type="number"
            formControlName="deckPercentageUsedIn"
          />
          <small
            class="validation-control-error"
            *ngIf="
              controls.deckPercentageUsedIn?.invalid &&
              controls.deckPercentageUsedIn?.touched
            "
          >
            Deck Percentage Used In is required.
          </small>
        </div>

        <div class="flex-1 d-flex flex-direction-column gap-16">
          <label class="f-bold">
            Deck Percentage Used Out <span style="color: red">*</span>
          </label>
          <input
            pInputText
            type="number"
            formControlName="deckPercentageUsedOut"
          />
          <small
            class="validation-control-error"
            *ngIf="
              controls.deckPercentageUsedOut?.invalid &&
              controls.deckPercentageUsedOut?.touched
            "
          >
            Deck Percentage Used Out is required.
          </small>
        </div>
      </div>

      <div class="d-flex flex-wrap gap-20 mb-16">
        <div class="flex-1 d-flex flex-direction-column gap-16">
          <label class="f-bold">Total Mileage</label>
          <input pInputText type="number" formControlName="totalMileage" />
          <small
            class="validation-control-error"
            *ngIf="
              controls.totalMileage?.invalid &&
              controls.totalMileage?.touched &&
              form.controls.totalMileage.hasError('min')
            "
          >
            Total Mileage must be greater than or equal to 0
          </small>
        </div>

        <div class="flex-1 d-flex flex-direction-column gap-16">
          <label class="f-bold">Comments</label>
          <input pInputText type="text" formControlName="comments" />
        </div>
      </div>

      <div class="d-flex flex-wrap gap-20 mb-16">
        <div
          *ngFor="let voyageDirection of voyageDirection"
          class="field-checkbox"
        >
          <p-radioButton
            [inputId]="voyageDirection.name"
            [value]="voyageDirection.value"
            formControlName="voyageDirection"
          />
          <label [for]="voyageDirection.name" class="ml-2">
            {{ voyageDirection.name }}
          </label>
        </div>
      </div>
    </form>
  </ng-template>

  <ng-template pTemplate="footer">
    <div class="actions">
      <button class="btn-tertiary" type="button" (click)="hideDialog()">
        Cancel
      </button>
      <button
        [disabled]="loading().createEdit"
        class="btn-primary"
        type="button"
        (click)="saveVoyage()"
      >
        <ng-container *ngIf="loading().createEdit; else saveText">
          <p-progressSpinner
            [styleClass]="'small-spinner-style-btn-white'"
          ></p-progressSpinner>
        </ng-container>
        <ng-template #saveText>Save</ng-template>
      </button>
    </div>
  </ng-template>
</p-dialog>
