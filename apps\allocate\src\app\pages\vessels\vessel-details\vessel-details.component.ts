import { Component } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { VesselHeaderComponent } from '../../../components/vessels/vessel-header/vessel-header.component';
import { PortalDirective } from 'libs/components/src/lib/directives/portal.directive';

@Component({
  selector: 'lha-vessel-details',
  standalone: true,
  imports: [RouterOutlet, VesselHeaderComponent, PortalDirective],
  templateUrl: './vessel-details.component.html',
  styleUrls: ['./vessel-details.component.scss'],
})
export class VesselDetailsComponent {}
