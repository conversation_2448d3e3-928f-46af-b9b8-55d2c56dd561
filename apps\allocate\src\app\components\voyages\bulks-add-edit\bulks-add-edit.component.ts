import {
  Component,
  Destroy<PERSON>ef,
  inject,
  <PERSON><PERSON><PERSON><PERSON>,
  OnInit,
} from '@angular/core';
import {
  MAT_DIALOG_DATA,
  MatDialogModule,
  MatDialogRef,
} from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { AsyncPipe, NgForOf, NgIf } from '@angular/common';
import { LoadingDirective } from 'libs/components/src/lib/directives/loading.directive';
import { Bulk } from 'libs/services/src/lib/services/voyages/interfaces/bulk.interface';
import { Store } from '@ngrx/store';
import { Actions, ofType } from '@ngrx/effects';
import { bulksFeature } from 'libs/services/src/lib/services/voyages/store/features';
import { combineLatest, startWith } from 'rxjs';
import { BulksActions } from 'libs/services/src/lib/services/voyages/store/actions/bulks.actions';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { UtilityService } from 'libs/services/src/lib/services/utility.service';
import { ActivatedRoute } from '@angular/router';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { NgxMatDatetimePickerModule } from '@angular-material-components/datetime-picker';
import {
  MatMomentDateModule,
  MAT_MOMENT_DATE_ADAPTER_OPTIONS,
} from '@angular/material-moment-adapter';
import { decimalPoint } from 'libs/components/src/lib/validators/decimal-point';
import { greaterThan } from 'libs/components/src/lib/validators/greaterThan';
import { OnlyDigitsDirective } from 'libs/components/src/lib/directives/only-digits.directive';
import { SingleSelectComponent } from 'libs/components/src/lib/components/single-select/single-select.component';
import { settingsFeature } from 'libs/services/src/lib/services/settings/shared/store/features';
import { CdkDrag, CdkDragHandle } from '@angular/cdk/drag-drop';
import { voyagesFeature } from 'libs/services/src/lib/services/voyages/store/features';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

@Component({
  selector: 'lha-bulk-transaction-add-edit',
  standalone: true,
  imports: [
    MatDialogModule,
    MatButtonModule,
    ReactiveFormsModule,
    MatInputModule,
    MatSelectModule,
    NgForOf,
    AsyncPipe,
    LoadingDirective,
    NgIf,
    MatSlideToggleModule,
    MatDatepickerModule,
    MatMomentDateModule,
    NgxMatDatetimePickerModule,
    OnlyDigitsDirective,
    SingleSelectComponent,
    CdkDrag,
    CdkDragHandle,
  ],
  providers: [
    { provide: MAT_MOMENT_DATE_ADAPTER_OPTIONS, useValue: { useUtc: true } },
  ],
  templateUrl: './bulks-add-edit.component.html',
  styleUrls: ['./bulks-add-edit.component.scss'],
})
export class BulksAddEditComponent implements OnInit, OnDestroy {
  private readonly destroyRef = inject(DestroyRef);
  dialogRef = inject(MatDialogRef<BulksAddEditComponent>);
  utilityService = inject(UtilityService);
  activatedRoute = inject(ActivatedRoute);
  data: { bulk: Bulk } = inject(MAT_DIALOG_DATA);
  store = inject(Store);
  action = inject(Actions);
  bulksState = this.store.selectSignal(bulksFeature.selectBulksState);
  bulkTransactions = this.store.selectSignal(bulksFeature.selectBulks);
  bulksList: Bulk[] = [];
  bulkTypes = this.store.selectSignal(bulksFeature.selectBulkTypesList);
  bulkTypesList = this.store.selectSignal(
    bulksFeature.selectSelectBulkTypesFilteredList
  );
  assets = this.store.selectSignal(bulksFeature.selectAssetList);
  filteredAssetsList = this.store.selectSignal(
    bulksFeature.selectFilteredAssetList
  );
  isAdd = true;
  voyageId = this.utilityService.getParamsFromRoute(
    this.activatedRoute.snapshot.root
  )['id'];
  voyage = this.store.selectSignal(voyagesFeature.selectActiveVoyage);
  bulk: Bulk = {
    bulkTransactionId: '',
    bulkTransactionNumber: `${this.voyageId}_`,
    completedDateTime: new Date(),
    bulkTypeName: '',
    bulkTypeId: '',
    quantity: 0,
    bulkTypeUnitName: '',
    assetId: '',
    assetName: '',
    clientId: '',
    clientName: '',
    deleted: false,
    transactionTypeDescription: '',
    transactionType: '',
    tankTypeId: '',
    tankTypeName: '',
    comments: '',
    price: 0,
    voyageId: this.voyageId,
    bulkDeliveryNoteFileName: '',
    delTicket: '',
  } as Bulk;
  appSettings$ = this.store.select(settingsFeature.selectAppSettings);
  dep = {
    trLocations: ['DEL', 'DTP'],
    trOperator: ['DEL'],
    trCompletedDateTime: ['DEL', 'LOA'],
  };
  assetType = '';
  form = new FormGroup({
    bulkTransactionNumber: new FormControl<string>('', Validators.required),
    bulkTypeId: new FormControl<string | null>('', Validators.required),
    tankTypeId: new FormControl<string>('', [Validators.required]),
    transactionType: new FormControl<string>('', [Validators.required]),
    quantity: new FormControl<null | number>(null, [
      Validators.required,
      greaterThan(0),
      decimalPoint(3),
    ]),
    dayRatePrice: new FormControl<null | number>(null, []),
    completedDateTime: new FormControl<Date | null>(null, []),
    delTicket: new FormControl<string | null>(null, []),
    assetId: new FormControl<string | null>(null, []),
    clientId: new FormControl<string | null>(
      null,
      this.data?.bulk &&
      this.data?.bulk.transactionType === 'DEL' &&
      this.data?.bulk.assetType === 'POR'
        ? [Validators.required]
        : []
    ),
  });

  ngOnInit(): void {
    this.subCloseDialog();
    this.initAddEdit();
    this.subToTransactionType();
    this.subToAsset();
  }

  ngOnDestroy(): void {
    this.store.dispatch(
      BulksActions.set_Transaction_Type({ transactionType: '' })
    );
  }

  private initAddEdit(): void {
    this.isAdd = !this.data.bulk;
    if (!this.isAdd) {
      this.bulk = this.data.bulk;
      this.pathForm(this.bulk);
      this.store.dispatch(
        BulksActions.set_Transaction_Type({
          transactionType: this.bulk?.transactionType ?? '',
        })
      );
      this.assetType = this.bulk.assetType;
      if (this.bulk.transactionType === 'LOA') {
        this.form.controls.dayRatePrice.setValidators([
          Validators.required,
          decimalPoint(2),
        ]);
      }
    } else {
      this.form.patchValue({
        bulkTransactionNumber: this.voyage()?.voyageNumber + '_',
      });
    }
  }

  private subCloseDialog(): void {
    this.action
      .pipe(
        ofType(BulksActions.add_Bulk_Success, BulksActions.edit_Bulk_Success)
      )
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(() => {
        this.dialogRef.close();
      });
  }

  private subToTransactionType(): void {
    let isSelected = false;
    combineLatest([
      this.bulkTypes(),
      this.form.controls.transactionType.valueChanges,
    ])
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(([bulkTypes, value]) => {
        /** Price validation */
        if (value === 'LOA') {
          this.form.controls.dayRatePrice.setValidators([
            Validators.required,
            Validators.min(0),
            decimalPoint(2),
          ]);
        } else {
          this.form.controls.dayRatePrice.clearValidators();
          this.form.controls.dayRatePrice.patchValue(null);
        }
        this.form.controls.dayRatePrice.updateValueAndValidity({
          onlySelf: true,
          emitEvent: false,
        });

        /** Date validation */
        if (this.dep.trCompletedDateTime.includes(value ?? '')) {
          this.form.controls.completedDateTime.setValidators([
            Validators.required,
          ]);
        } else {
          this.form.controls.completedDateTime.clearValidators();
          this.form.controls.completedDateTime.patchValue(null);
        }
        this.form.controls.completedDateTime.updateValueAndValidity({
          onlySelf: true,
          emitEvent: false,
        });

        /** Location validation */
        if (this.dep.trLocations.includes(value ?? '')) {
          this.form.controls.assetId.setValidators([Validators.required]);
        } else {
          this.form.controls.assetId.clearValidators();
          this.form.controls.assetId.patchValue(null);
        }

        if (this.form.controls.assetId.value) {
          this.form.controls.assetId.patchValue(null);
        }

        this.form.controls.assetId.updateValueAndValidity({
          onlySelf: true,
          emitEvent: false,
        });
      });
  }

  private subToAsset(): void {
    combineLatest([
      this.assets(),
      this.form.controls.assetId.valueChanges.pipe(
        startWith(this.form.controls.assetId.value)
      ),
      this.form.controls.transactionType.valueChanges.pipe(
        startWith(this.form.controls.transactionType.value)
      ),
    ])
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(([loc, locId, transactionType]) => {
        const asset = Array.isArray(loc)
          ? loc.find((item) => item.assetId === locId)
          : undefined;
        this.assetType = asset?.assetType ?? '';
        /** Operator validation */
        if (
          this.dep.trOperator.includes(transactionType ?? '') &&
          this.assetType === 'POR'
        ) {
          this.form.controls.clientId.setValidators([Validators.required]);
        } else {
          this.form.controls.clientId.clearValidators();
          this.form.controls.clientId.patchValue(null);
        }

        this.form.controls.clientId.updateValueAndValidity({
          onlySelf: true,
          emitEvent: false,
        });

        if (this.assetType === 'POR' && this.bulksList.length) {
          this.form.controls.clientId.patchValue(
            this.bulksList[this.bulksList.length - 1].clientId
          );
        }
      });
  }

  private pathForm(bulk: Bulk): void {
    this.form.patchValue({
      ...bulk,
    });
  }

  saveBulk(): void {
    if (this.form.invalid || !this.form.controls.bulkTypeId.value) {
      this.form.markAllAsTouched();
      return;
    }

    const model = {
      ...this.form.getRawValue(),
      voyageId: this.voyageId,
    } as Bulk;

    if (this.isAdd) {
      this.store.dispatch(BulksActions.add_Bulk({ bulk: model }));
    } else {
      this.store.dispatch(
        BulksActions.edit_Bulk({
          bulk: model,
          bulkId: this.bulk.bulkTransactionId,
        })
      );
    }
  }
}
