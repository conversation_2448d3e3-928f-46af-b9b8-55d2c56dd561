import {
  ChangeDetectionStrategy,
  Component,
  computed,
  inject,
  Input,
} from '@angular/core';
import { Ng<PERSON><PERSON>, NgForOf, NgIf } from '@angular/common';

import { Store } from '@ngrx/store';
import { CardModule } from 'primeng/card';
import { BadgeModule } from 'primeng/badge';

import { OperatorCardComponent } from '../operator-card/operator-card.component';
import { BillingsActions } from '../../../store/actions/billing.actions';
import { BillingService } from '../../../shared/services/billing.service';
import { HasPermissionDirective } from '../../../shared/directives/hasPermissions.directive';
import { billingsFeature } from '../../../store/features/billing.features';
import { billingOperatorsFeature } from '../../../store/features/billing-operator.features';
import { BillingCalculationComponent } from '../billing-calculation/billing-calculation.component';
import { settingsFeature } from 'libs/services/src/lib/services/settings/shared/store/features';
import { UserRole } from 'libs/auth/src/lib/constants/userRole.enum';
import { formatDateAsUserTimezone } from 'libs/services/src/lib/services/functions/timezone-conversion.utils';
import { currentUserFeature } from 'libs/auth/src/lib/store/current-user/current-user.features';

@Component({
  selector: 'lha-overview',
  standalone: true,
  imports: [
    OperatorCardComponent,
    NgForOf,
    NgIf,
    HasPermissionDirective,
    CardModule,
    NgClass,
    BadgeModule,
    BillingCalculationComponent,
  ],
  templateUrl: './overview.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class OverviewComponent {
  @Input() billingPeriodId = '';
  private readonly store = inject(Store);
  billingService = inject(BillingService);
  billing = this.store.selectSignal(billingsFeature.selectBilling);
  loading = this.store.selectSignal(billingsFeature.selectLoading);
  billingOperators = this.store.selectSignal(
    billingOperatorsFeature.selectBillingOperators
  );
  appSettings = this.store.selectSignal(settingsFeature.selectAppSettings);
  currentUser = this.store.selectSignal(currentUserFeature.selectUser);
  userRole = UserRole;

  transformedBilling = computed(() => {
    const billing = this.billing();
    const user = this.currentUser();

    if (billing) {
      const userTimezone = user?.locationTimeZoneInfoId || 'UTC';

      return {
        ...billing,
        periodStartDateFormatted: billing.periodStartDate
          ? formatDateAsUserTimezone(
              billing.periodStartDate,
              userTimezone,
              'dd-MMMM-yy HH:mm'
            )
          : '',
        periodEndDateFormatted: billing.periodEndDate
          ? formatDateAsUserTimezone(
              billing.periodEndDate,
              userTimezone,
              'dd-MMMM-yy HH:mm'
            )
          : '',
      };
    }
    return null;
  });

  calcBillingPeriod() {
    this.store.dispatch(
      BillingsActions.change_visibility_calc_billing_period({
        visible: true,
      })
    );
  }
}
