import { BillingInitState } from './billing-init-state.interface';
import { Billing } from './billing.interface';
import { BillingHistory } from './billing-history.interface';

export interface BillingState extends BillingInitState {
  billings: Billing[];
  billingHistory: BillingHistory[];
  billing: Billing | null;
  billingPeriodId: string;
  isVisibleCalcBillingPeriod: boolean;
  isVisibleBillingHistory: boolean;
}
