<div
  *ngIf="vm$ | async as vm"
  cdkDrag
  cdkDragRootElement=".cdk-overlay-pane"
  cdkDragHandle
  cdkDragBoundary=".cdk-overlay-container"
>
  <div mat-dialog-title>
    <h4>{{ isAdd ? 'Add ' : 'Edit ' }} Tank Status</h4>
  </div>
  <mat-dialog-content *lhaLoading="vm.loading.createEdit">
    <form [formGroup]="form">
      <div class="form__block">
        <div class="form__box">
          <lha-single-select
            [options]="vm.bulkTransactions"
            formControlName="bulkTransactionId"
            bindValue="bulkTransactionId"
            bindLabel="bulkTransactionNumber"
            placeholder="Bulk Transaction Number"
          />
          <div
            *ngIf="
              form.controls.bulkTransactionId.invalid &&
              form.controls.bulkTransactionId.touched
            "
          >
            <mat-error
              *ngIf="form.controls.bulkTransactionId.hasError('required')"
            >
              Bulk Transaction Number is required.
            </mat-error>
          </div>
        </div>
        <div class="form__box">
          <mat-form-field appearance="outline" hideRequiredMarker="true">
            <mat-label>Status Date Time</mat-label>
            <input
              matInput
              [ngxMatDatetimePicker]="statusDateTime"
              formControlName="statusDateTime"
            />
            <mat-datepicker-toggle
              matIconSuffix
              [for]="$any(statusDateTime)"
            ></mat-datepicker-toggle>
            <ngx-mat-datetime-picker #statusDateTime></ngx-mat-datetime-picker>
            <mat-error *ngIf="form.controls.statusDateTime.hasError('required')"
              >Status Date Time</mat-error
            >
          </mat-form-field>
        </div>
        <div class="form__box">
          <mat-form-field appearance="outline" hideRequiredMarker="true">
            <mat-label>Discharge Date Time</mat-label>
            <input
              matInput
              [ngxMatDatetimePicker]="dischargeDateTime"
              formControlName="dischargeDateTime"
            />
            <mat-datepicker-toggle
              matIconSuffix
              [for]="$any(dischargeDateTime)"
            ></mat-datepicker-toggle>
            <ngx-mat-datetime-picker
              #dischargeDateTime
            ></ngx-mat-datetime-picker>
            <mat-error
              *ngIf="form.controls.dischargeDateTime.hasError('required')"
              >Discharge Date Time</mat-error
            >
          </mat-form-field>
        </div>
        <div class="form__box">
          <mat-form-field appearance="outline" hideRequiredMarker="true">
            <mat-label>Specific Gravity</mat-label>
            <input matInput type="number" formControlName="specificGravity" />
            <mat-error
              *ngIf="form.controls.specificGravity.hasError('required')"
              >Specific Gravity is required</mat-error
            >
            <mat-error *ngIf="form.controls.specificGravity.hasError('min')"
              >Specific Gravity should be more than 0</mat-error
            >
          </mat-form-field>
        </div>
        <div class="form__box">
          <mat-form-field appearance="outline" hideRequiredMarker="true">
            <mat-label>Quantity On Boarding</mat-label>
            <input
              matInput
              type="number"
              [decimal]="true"
              formControlName="quantityOnBoarding"
            />
            <mat-error
              *ngIf="form.controls.quantityOnBoarding.hasError('required')"
              >Quantity On Boarding is required</mat-error
            >
            <mat-error
              *ngIf="form.controls.quantityOnBoarding.hasError('greaterThan')"
              >Quantity On Boarding should be more than 0</mat-error
            >
            <mat-error
              *ngIf="form.controls.quantityOnBoarding.hasError('decimalPoint')"
              >Decimal point should be less than 4</mat-error
            >
          </mat-form-field>
        </div>
        <div class="form__box">
          <mat-form-field appearance="outline" hideRequiredMarker="true">
            <mat-label>Load Date Time</mat-label>
            <input
              matInput
              [ngxMatDatetimePicker]="loadDateTime"
              formControlName="loadDateTime"
            />
            <mat-datepicker-toggle
              matIconSuffix
              [for]="$any(loadDateTime)"
            ></mat-datepicker-toggle>
            <ngx-mat-datetime-picker #loadDateTime></ngx-mat-datetime-picker>
            <mat-error *ngIf="form.controls.loadDateTime.hasError('required')"
              >Load Date Time</mat-error
            >
          </mat-form-field>
        </div>
        <div class="form__box">
          <mat-form-field appearance="outline" hideRequiredMarker="true">
            <mat-label>Well Number</mat-label>
            <input matInput formControlName="wellNumber" />
            <mat-error *ngIf="form.controls.wellNumber.hasError('required')"
              >Well Number is required</mat-error
            >
          </mat-form-field>
        </div>
        <div class="form__box">
          <mat-form-field appearance="outline" hideRequiredMarker="true">
            <mat-label>Quantity Transferred</mat-label>
            <input
              matInput
              type="number"
              [decimal]="true"
              formControlName="quantityTransferred"
            />
            <mat-error
              *ngIf="form.controls.quantityTransferred.hasError('required')"
              >Quantity Transferred is required</mat-error
            >
            <mat-error
              *ngIf="form.controls.quantityTransferred.hasError('greaterThan')"
              >Quantity Transferred should be more than 0</mat-error
            >
            <mat-error
              *ngIf="form.controls.quantityTransferred.hasError('decimalPoint')"
              >Decimal point should be less than 4</mat-error
            >
          </mat-form-field>
        </div>
        <div class="form__box">
          <mat-form-field appearance="outline" hideRequiredMarker="true">
            <mat-label>Recording Type</mat-label>
            <mat-select formControlName="recordingType">
              <mat-option
                *ngFor="let item of recordingTypeList"
                [value]="item.name"
              >
                {{ item.name }}
              </mat-option>
            </mat-select>
            <mat-error *ngIf="form.controls.recordingType.hasError('required')"
              >Recording Type is required</mat-error
            >
          </mat-form-field>
        </div>
        <div class="form__box">
          <mat-form-field appearance="outline" hideRequiredMarker="true">
            <mat-label>Status</mat-label>
            <input matInput formControlName="status" />
            <mat-error *ngIf="form.controls.status.hasError('required')"
              >Status is required</mat-error
            >
          </mat-form-field>
        </div>
        <div class="form__box form__box--mb20">
          <mat-slide-toggle formControlName="isBackload"
            >Backload</mat-slide-toggle
          >
        </div>
        <div class="form__box form__box--mb20">
          <mat-slide-toggle formControlName="cleaned">Cleaned</mat-slide-toggle>
        </div>
        <div class="form__box form__box--w100">
          <mat-form-field
            appearance="outline"
            hideRequiredMarker="true"
            subscriptSizing="dynamic"
          >
            <mat-label>Comments</mat-label>
            <textarea matInput formControlName="comments"></textarea>
          </mat-form-field>
        </div>
      </div>
    </form>
    <mat-dialog-actions align="end">
      <button
        mat-raised-button
        color="primary"
        type="submit"
        (click)="saveTankManagement()"
      >
        {{ isAdd ? 'Add ' : 'Save ' }}
      </button>
      <button mat-raised-button color="warn" type="button" mat-dialog-close="">
        Cancel
      </button>
    </mat-dialog-actions>
  </mat-dialog-content>
</div>
