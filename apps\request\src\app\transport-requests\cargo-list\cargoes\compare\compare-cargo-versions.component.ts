import { Store } from '@ngrx/store';
import { MenuItem } from 'primeng/api';
import { NgIf } from '@angular/common';
import { Actions } from '@ngrx/effects';
import { CardModule } from 'primeng/card';
import { ActivatedRoute } from '@angular/router';
import { DropdownModule } from 'primeng/dropdown';
import { BreadcrumbModule } from 'primeng/breadcrumb';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ComparePath } from 'libs/components/src/lib/enums/compare-path.enum';
import { NavBar } from 'libs/components/src/lib/interfaces/nav-bar.interface';
import {
  Component,
  computed,
  inject,
  Input,
  OnInit,
  signal,
} from '@angular/core';
import { compareFeature } from 'apps/request/src/app/store/features/compare.feature';
import { lookaheadFeature } from 'libs/services/src/lib/services/lookahead/store/features';
import { HeaderNavComponent } from 'libs/components/src/lib/components/header-nav/header-nav.component';
import { TransportRequestHeaderComponent } from '../../../transport-request-header/transport-request-header.component';
import { transportRequestFeature } from 'libs/services/src/lib/services/transport-requests/store/features/transport-request.feature';
import { TransportRequestCargoSnapshotActions } from 'libs/services/src/lib/services/transport-requests/store/actions/transport-request-cargo-snapshot.actions';
import { transportRequestCargoSnapshotFeature } from 'libs/services/src/lib/services/transport-requests/store/features/transport-request-cargo-snapshot.feature';

@Component({
  selector: 'transport-request-cargoes',
  templateUrl: './compare-cargo-versions.component.html',
  styleUrls: ['./compare-cargo-versions.component.scss'],
  standalone: true,
  imports: [
    NgIf,
    TransportRequestHeaderComponent,
    BreadcrumbModule,
    CardModule,
    DropdownModule,
    HeaderNavComponent,
    ProgressSpinnerModule,
    DropdownModule,
    ReactiveFormsModule,
    FormsModule,
  ],
})
export class CompareCargoVersionsComponent implements OnInit {
  @Input() transportRequestId = '';
  @Input() sailingRequestId = '';
  private readonly route = inject(ActivatedRoute);
  store = inject(Store);
  actions = inject(Actions);
  sailingRequest = this.store.selectSignal(
    lookaheadFeature.selectSailingRequest
  );
  transportRequest = this.store.selectSignal(
    transportRequestFeature.selectTransportRequest
  );
  transportRequestCargoVersionList = this.store.selectSignal(
    transportRequestCargoSnapshotFeature.selectTransportRequestCargoSnapshotVersions
  );
  transportRequestSnapshotId = this.store.selectSignal(
    compareFeature.selectTransportRequestSnapshotId
  );
  childRoute = this.route.snapshot.queryParams['view'] as ComparePath;
  selectedChildRoute = signal<ComparePath>(ComparePath.Cargo);
  selectedChildTile = signal('');

  ngOnInit() {
    this.selectedChildRoute.set(this.childRoute);
    this.selectedChildTile.set(
      this.childRoute == ComparePath.MaterialDetails
        ? 'Material Detail'
        : this.childRoute == ComparePath.Bulks
        ? 'Bulks'
        : 'Cargoes'
    );
    this.store.dispatch(
      TransportRequestCargoSnapshotActions.compare_Transport_Request_Cargo_Version_Page(
        {
          sailingRequestId: this.sailingRequestId,
          transportRequestId: this.transportRequestId,
        }
      )
    );
  }

  onNavItemClicked(item: NavBar) {
    this.selectedChildRoute.set(item.link as ComparePath);
    this.selectedChildTile.set(item.title as ComparePath);
  }

  navItems = computed<NavBar[]>(() => {
    return [
      {
        title: 'Cargo',
        link: `cargo`,
        permissions: [],
      },
      {
        title: 'Bulks',
        link: `bulks`,
        permissions: [],
        disable: !this.sailingRequest()?.isBulkReq,
      },
      {
        title: 'Material Details',
        link: `material-details`,
        permissions: [],
        disable: !(
          this.transportRequest()?.transportRequestBulkCargos.length ||
          this.transportRequest()?.transportRequestCargos.length
        ),
      },
    ];
  });

  breadcrumb = computed<MenuItem[]>(() => {
    const voyageDirection = this.sailingRequest()?.transportRequests.find(
      (tr: any) => tr.transportRequestId === this.transportRequestId
    )?.voyageDirectionName;
    const voyageDirectionName = this.transportRequest()?.voyageDirectionName;
    const clusterName = this.sailingRequest()?.clusterName;

    return [
      {
        label: 'Lookahead',
        routerLink: '/sailing-schedule',
        styleClass: 'active',
      },
      {
        label: clusterName
          ? `${clusterName} - ${voyageDirectionName ?? ''}`
          : voyageDirection ?? '',
        routerLink: `/transport-requests/${this.sailingRequestId}/${
          this.transportRequestId
        }/${this.selectedChildRoute()}`,
        styleClass: 'active',
      },
      {
        label: `Edit Cargo List - ${this.selectedChildTile()}`,
        routerLink: `/transport-requests/${this.sailingRequestId}/${
          this.transportRequestId
        }/cargo-list/create-edit/${this.selectedChildRoute()}`,
        styleClass: 'active',
      },
      {
        label: 'Compare ',
      },
    ];
  });
}
