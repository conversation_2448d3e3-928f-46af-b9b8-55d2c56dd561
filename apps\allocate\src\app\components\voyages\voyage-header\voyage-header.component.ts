import { Component, inject } from '@angular/core';
import { MatCardModule } from '@angular/material/card';
import { Store } from '@ngrx/store';
import { voyagesFeature } from 'libs/services/src/lib/services/voyages/store/features';
import { AsyncPipe, NgIf } from '@angular/common';
import { PropertyNamePipe } from 'libs/components/src/lib/pipes/property-name.pipe';
import { voyageTypes } from 'libs/services/src/lib/services/voyages/voyage-types/voyage-types';

@Component({
  selector: 'lha-voyage-header',
  standalone: true,
  imports: [MatCardModule, NgIf, AsyncPipe, PropertyNamePipe],
  templateUrl: './voyage-header.component.html',
  styleUrls: ['./voyage-header.component.scss'],
})
export class VoyageHeaderComponent {
  store = inject(Store);
  vm$ = this.store.select(voyagesFeature.selectVoyagesState);
  voyageTypes = voyageTypes;
}
