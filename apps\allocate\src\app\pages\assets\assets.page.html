<div class="d-flex justify-content-between mb-40">
  <b class="fs-24">Assets</b>

  <div class="d-flex gap-20">
    <span class="p-input-icon-left">
      <em class="pi pi-search"></em>
      <input
        type="text"
        pInputText
        [(ngModel)]="searchValue"
        placeholder="Search..."
        (input)="table.filterGlobal(searchValue, 'contains')"
      />
    </span>

    <button type="button" class="btn-primary" (click)="createAssets()">
      Create
    </button>
    <button
      type="button"
      class="btn-export align-items-center d-flex"
      (click)="export()"
    >
      <img src="assets/icons/exel.svg" />
      Export
    </button>
  </div>
</div>

<p-table
  #table
  [columns]="listColumns"
  [value]="assets"
  [scrollable]="true"
  scrollHeight="calc(100vh - 273px)"
  [rowsPerPageOptions]="[10, 25, 50]"
  [paginator]="true"
  [rows]="10"
  [filterDelay]="0"
  [globalFilterFields]="[
    tableFields.name,
    tableFields.assetTypeDescription,
    tableFields.currentClientName,
    tableFields.clusterChildren,
    tableFields.locationNames
  ]"
>
  <ng-template pTemplate="header" let-columns>
    <tr>
      <th
        *ngFor="let column of columns"
        [style.min-width.px]="column.width"
        [style.width.%]="(column.width / tableWidth) * 100"
        [pSortableColumn]="column.field"
        [pSortableColumnDisabled]="!column.sortable"
      >
        <span>{{ column.name }}</span>
        <p-sortIcon *ngIf="column.sortable" [field]="column.field" />
      </th>
    </tr>
  </ng-template>
  <ng-template
    let-index="rowIndex"
    pTemplate="body"
    let-row
    let-columns="columns"
  >
    <tr>
      <ng-container *ngFor="let column of columns">
        <ng-container [ngSwitch]="column.field">
          <td *ngSwitchCase="tableFields.isClusterHead">
            <span class="d-flex align-items-center">
              <i
                class="pi fs-16"
                [ngClass]="row.isClusterHead ? 'pi-check' : ''"
                [style.color]="row.isClusterHead ? 'green' : ''"
              >
              </i>
            </span>
          </td>
          <td *ngSwitchCase="tableFields.assetTypeDescription">
            <lha-custom-chip
              [cssClass]="row.assetType"
              [text]="row.assetTypeDescription"
            >
            </lha-custom-chip>
          </td>
          <td *ngSwitchCase="tableFields.clusterChildren">
            <div class="d-flex gap-5 flex-wrap">
              <lha-custom-chip
                *ngFor="let clusterChild of row.clusterChildren"
                cssClass="draft"
                [size]="null"
                [text]="clusterChild"
              />
            </div>
          </td>
          <td *ngSwitchCase="tableFields.locationNames">
            <div class="d-flex gap-5 flex-wrap">
              <lha-custom-chip
                *ngFor="let locationName of row.locationNames"
                cssClass="draft"
                [size]="null"
                [text]="locationName"
              />
            </div>
          </td>
          <td *ngSwitchCase="tableFields.actions">
            <div class="d-flex gap-8 flex-wrap">
              <button
                type="button"
                class="btn-icon-only"
                (click)="editAsset(row)"
              >
                <em class="pi pi-pencil"></em>
              </button>
              <button
                type="button"
                class="btn-icon-only"
                (click)="openMobileWell(row)"
                [disabled]="row.assetType !== 'MOB'"
              >
                <em class="pi pi-table"></em>
              </button>
              <button
                type="button"
                class="btn-icon-only"
                (click)="openAssetHistory(row.clientAssets)"
                [disabled]="row.assetType === 'MOB' || row.assetType === 'POR'"
              >
                <em class="pi pi-history"></em>
              </button>
            </div>
          </td>
          <td *ngSwitchDefault>{{ row[column.field] }}</td>
        </ng-container>
      </ng-container>
    </tr>
  </ng-template>
  <ng-template pTemplate="emptymessage">
    <tr>
      <td colspan="7" style="text-align: center">No results found</td>
    </tr>
  </ng-template>
</p-table>

<allocate-assets-mobile-well *ngIf="isVisibleMobileWell()" />
<allocate-assets-history />
<allocate-assets-add-edit *ngIf="isVisibleAddEdit()" />
