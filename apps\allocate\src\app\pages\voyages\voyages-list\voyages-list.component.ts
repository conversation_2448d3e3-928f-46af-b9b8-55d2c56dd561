import { Component, inject, OnInit, ViewChild } from '@angular/core';
import { Store } from '@ngrx/store';
import { VoyagesActions } from 'libs/services/src/lib/services/voyages/store/actions/voyages.actions';
import { voyagesFeature } from 'libs/services/src/lib/services/voyages/store/features';
import {
  AsyncPipe,
  NgIf,
  NgFor,
  DatePipe,
  NgStyle,
  DecimalPipe,
  NgClass,
} from '@angular/common';
import { RouterLink } from '@angular/router';
import { Voyage } from 'libs/services/src/lib/services/voyages/interfaces/voyage.interface';
import { View } from '../../../shared/types/view.type';
import { VoyageFlatComponent } from '../../../components/voyages/voyage-flat/voyage-flat.component';
import { FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { CustomChipComponent } from 'libs/components/src/lib/components/custom-chip/custom-chip.component';
import { ColumnModel } from 'libs/components/src/lib/models/column.model';
import { SortOrder } from 'libs/components/src/lib/enums/sort-order.enum';
import { Table, TableModule } from 'primeng/table';
import { CalendarModule } from 'primeng/calendar';
import { InputTextModule } from 'primeng/inputtext';
import { PaginatorModule } from 'primeng/paginator';
import { VoyageAddEditComponent } from '../../../components/voyages/voyage-add-edit/voyage-add-edit.component';
import { MatIconModule } from '@angular/material/icon';
import { stripTimezoneOffset } from 'libs/services/src/lib/services/functions/convert-date.utils';
@Component({
  selector: 'lha-voyages-list',
  standalone: true,
  imports: [
    AsyncPipe,
    NgIf,
    DatePipe,
    MatIconModule,
    NgStyle,
    NgClass,
    VoyageFlatComponent,
    DecimalPipe,
    ReactiveFormsModule,
    CustomChipComponent,
    TableModule,
    NgFor,
    CalendarModule,
    InputTextModule,
    PaginatorModule,
    VoyageAddEditComponent,
    RouterLink,
  ],
  templateUrl: './voyages-list.component.html',
  styleUrls: ['./voyages-list.component.scss'],
})
export class VoyagesListComponent implements OnInit {
  @ViewChild('table') table!: Table;
  store = inject(Store);
  voyagesState = this.store.selectSignal(voyagesFeature.selectVoyagesState);
  voyages = this.store.selectSignal(voyagesFeature.selectFilteredVoyages);
  dateRangeControl = new FormGroup({
    startDate: new FormControl<Date | null>(null),
    endDate: new FormControl<Date | null>(null),
    search: new FormControl<string>(''),
  });
  controls = {
    startDate: this.dateRangeControl.get('startDate'),
    endDate: this.dateRangeControl.get('endDate'),
    search: this.dateRangeControl.get('search'),
  };
  viewMode = localStorage.getItem('viewMode') ?? 'list';
  columns: ColumnModel[] = [
    new ColumnModel('voyageNotValid', '', 100, {}),
    new ColumnModel('voyageNumber', 'Voyage Number', 120, {
      sortable: true,
      sortOrder: SortOrder.Asc,
    }),
    new ColumnModel('displayVesselName', 'Vessel', 120, {}),
    new ColumnModel('voyageTypeName', 'VoyageType', 120, {}),
    new ColumnModel('voyageStartDateTime', 'Start Date', 100, {}),
    new ColumnModel('voyageEndDateTime', 'End Date', 100, {}),
    new ColumnModel('initialAssetName', 'Initial Port', 120),
    new ColumnModel('totalMileage', 'Total Mileage', 100),
    new ColumnModel('deckPercentageUsedIn', 'Deck % Used In', 100),
    new ColumnModel('deckPercentageUsedOut', 'Deck % Used Out', 100),
    new ColumnModel('isCompleted', 'Completed', 150),
    new ColumnModel('voyageId', '', 120),
  ];
  tableWidth = 3235;

  ngOnInit(): void {
    this.getDateFilters();
    this.loadVoyages();
  }

  ngAfterViewInit(): void {
    this.getSearchFilter();
  }

  setFilters() {
    const filters = {
      startDate: this.controls.startDate?.value,
      endDate: this.controls.endDate?.value,
      search: this.controls.search?.value,
    };
    sessionStorage.setItem('voyageFilters', JSON.stringify(filters));
  }

  getDateFilters() {
    const stored = sessionStorage.getItem('voyageFilters');

    if (stored) {
      const filters = JSON.parse(stored);
      if (filters.startDate) {
        this.controls.startDate?.setValue(new Date(filters.startDate));
      }
      if (filters.endDate) {
        this.controls.endDate?.setValue(new Date(filters.endDate));
      }
    }
  }

  getSearchFilter() {
    const stored = sessionStorage.getItem('voyageFilters');

    if (stored) {
      const filters = JSON.parse(stored);
      if (filters.search) {
        if (filters.search) {
          this.controls.search?.setValue(filters.search);
          this.applyGlobalFilter(null, this.table, true);
        }
      }
    }
  }

  applyGlobalFilter(event: any, table: any, onInit: boolean): void {
    if (!onInit) {
      const inputElement = event.target as HTMLInputElement;
      if (inputElement) {
        table.filterGlobal(inputElement.value, 'contains');
        this.setFilters();
      }
    } else {
      table.filterGlobal(this.controls.search!.value, 'contains');
    }
  }

  loadVoyages(): void {
    this.store.dispatch(
      VoyagesActions.load_Voyage_Lists({
        startDate:
          this.controls.startDate?.value !== null
            ? stripTimezoneOffset(this.controls.startDate?.value!)
            : null,
        endDate: this.controls.endDate?.value
          ? stripTimezoneOffset(this.controls.endDate?.value!)
          : null,
      })
    );
  }

  exportTemplate(): void {
    this.store.dispatch(VoyagesActions.export_Voyage_Template());
  }

  exportVoyages(): void {
    this.store.dispatch(VoyagesActions.export_Voyages());
  }

  importFromTemplate(event: Event): void {
    this.store.dispatch(
      VoyagesActions.import_Voyage_Template({ voyageTemplate: event })
    );
    (event.target as HTMLInputElement).value = '';
  }

  addEditVoyage(voyage: Voyage | null, isEdit: boolean): void {
    this.store.dispatch(
      VoyagesActions.open_Voyage_Add_Edit_Dialog({
        isVisible: true,
        isEdit: isEdit,
        voyage: voyage!,
      })
    );
  }

  completeIncompleteVoyage(voyage: Voyage): void {
    if (voyage.isCompleted) {
      this.store.dispatch(
        VoyagesActions.incomplete_Voyage({ voyageId: voyage.voyageId })
      );
    } else {
      this.store.dispatch(
        VoyagesActions.complete_Voyage({
          voyageId: voyage.voyageId,
        })
      );
    }
  }

  setViewMode(mode: View): void {
    this.viewMode = mode;
    localStorage.setItem('viewMode', mode);
  }
}
