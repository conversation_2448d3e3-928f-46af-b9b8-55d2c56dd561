.sidebar-navigation {
  position: relative;
  .p-panelmenu {
    padding: 12px 0;
  }
  .p-panelmenu-header-action,
  .p-menuitem-link {
    padding: 9px 0 9px 23px;
    display: flex;
    gap: 8px;
    font-weight: 400;
    .p-menuitem-icon {
      width: 22px;
      height: 22px;
      background-color: var(--gray-600);
      mask-repeat: no-repeat;
    }

    &.p-menuitem-link-active {
      color: var(--blue-700);
      font-weight: 500;
      .p-menuitem-icon {
        background-color: var(--blue-700);
      }

      &:after {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 2px;
        height: 100%;
        background-color: var(--blue-700);
      }

      .p-menuitem-text {
        color: var(--blue-700);
        font-weight: 500;
      }
    }
  }

  .p-panelmenu
    .p-panelmenu-content
    .p-menuitem:not(.p-highlight):not(.p-disabled).p-focus
    > .p-menuitem-content {
    background: transparent;
  }
  .push-pin {
    position: absolute;
    right: 8px;
    bottom: 54px;

    &.pinned {
      color: var(--blue-700);
    }
  }

  &.unPinned {
    .p-panelmenu-header-action {
      .p-menuitem-text {
        display: none;
      }

      .p-icon-wrapper {
        display: none;
      }
    }

    .p-panelmenu-content {
      display: none;
    }
  }

  .p-icon-wrapper {
    position: absolute;
    right: 0;
  }

  .p-panelmenu-content {
    background-color: transparent;
    .p-submenu-list {
      padding-left: 31px;
    }
  }

  .p-menuitem-text {
    font-size: 14px;
  }

  .p-panelmenu-header-content {
    background-color: transparent;
  }
}
