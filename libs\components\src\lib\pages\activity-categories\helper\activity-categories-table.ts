import { ColumnModel } from 'libs/components/src/lib/models/column.model';
import { ActivityCategoriesTableFields } from './activity-categories-table-fields.enum';

export function InitializeActivityCategoriesTable(): ColumnModel[] {
  const columns = [
    new ColumnModel(
      ActivityCategoriesTableFields.name,
      'Activity Category',
      170,
      {
        sortable: true,
        sortOrder: null,
      }
    ),
    new ColumnModel(
      ActivityCategoriesTableFields.activityCategoryTypes,
      'Activity Category Types',
      150,
      {
        sortable: true,
        sortOrder: null,
      }
    ),
    new ColumnModel(
      ActivityCategoriesTableFields.createdByName,
      'Created Date',
      150,
      {
        sortable: true,
      }
    ),
    new ColumnModel(ActivityCategoriesTableFields.actions, '', 130),
  ];
  return columns;
}
