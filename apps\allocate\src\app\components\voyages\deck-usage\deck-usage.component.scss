.deck-usage {
  display: flex;
  flex-direction: column;
  height: 100%;

  &__toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
  }

  &__content {
    display: flex;
    flex-direction: row;
    gap: 1rem;
    flex: 1;
  }

  &__column {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
  }

  &__table-header {
    font-weight: bold;
    margin: 0;
  }

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  &__title {
    font-weight: bold;
    margin: 0;
    color: var(--theme-primary);
  }

  &__toolbar-actions {
    display: flex;
    gap: 0.5rem;
  }

  &__search-container {
    position: relative;
    display: flex;
    align-items: center;

    i {
      position: absolute;
      left: 0.75rem;
      color: #787f90;
      z-index: 1;
    }

    input {
      padding-left: 2.5rem;
    }
  }

  &__table-container {
    position: relative;
    width: 100%;
  }

  &__actions-column {
    display: flex;
    gap: 0.5rem;
  }
}
