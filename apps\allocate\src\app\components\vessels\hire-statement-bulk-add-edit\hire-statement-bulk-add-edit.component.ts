import { Component, inject, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import {
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { LoadingDirective } from 'libs/components/src/lib/directives/loading.directive';
import { MatButtonModule } from '@angular/material/button';
import {
  MAT_DIALOG_DATA,
  MatDialogModule,
  MatDialogRef,
} from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatOptionModule } from '@angular/material/core';
import { MatSelectModule } from '@angular/material/select';
import { AsyncPipe, NgForOf, NgIf } from '@angular/common';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { NgxMatDatetimePickerModule } from '@angular-material-components/datetime-picker';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { HireStatementBulk } from 'libs/services/src/lib/services/vessels/interfaces/hire-statement-bulk.interface';
import { HireStatement } from 'libs/services/src/lib/services/vessels/interfaces/hire-statement.interface';
import { ActivatedRoute } from '@angular/router';
import { UtilityService } from 'libs/services/src/lib/services/utility.service';
import { Store } from '@ngrx/store';
import { Actions, ofType } from '@ngrx/effects';
import { hireStatementFeature } from 'libs/services/src/lib/services/vessels/store/features';
import { Subject, takeUntil } from 'rxjs';
import { HireStatementBulkActions } from 'libs/services/src/lib/services/vessels/store/actions/hire-statement-bulk.actions';
import { greaterThan } from 'libs/components/src/lib/validators/greaterThan';
import { decimalPoint } from 'libs/components/src/lib/validators/decimal-point';
import { OnlyDigitsDirective } from 'libs/components/src/lib/directives/only-digits.directive';
import { SingleSelectComponent } from 'libs/components/src/lib/components/single-select/single-select.component';
import { CdkDrag, CdkDragHandle } from '@angular/cdk/drag-drop';
import { timeAfter } from 'libs/components/src/lib/functions/utility.functions';

@Component({
  selector: 'lha-hire-statement-bulk-add-edit',
  standalone: true,
  imports: [
    FormsModule,
    LoadingDirective,
    MatButtonModule,
    MatDialogModule,
    MatFormFieldModule,
    MatInputModule,
    MatOptionModule,
    MatSelectModule,
    NgForOf,
    ReactiveFormsModule,
    AsyncPipe,
    NgIf,
    MatDatepickerModule,
    NgxMatDatetimePickerModule,
    MatSlideToggleModule,
    OnlyDigitsDirective,
    SingleSelectComponent,
    CdkDrag,
    CdkDragHandle,
  ],
  templateUrl: './hire-statement-bulk-add-edit.component.html',
  styleUrls: ['./hire-statement-bulk-add-edit.component.scss'],
})
export class HireStatementBulkAddEditComponent implements OnInit, OnDestroy {
  dialogRef = inject(MatDialogRef<HireStatementBulkAddEditComponent>);
  data: { hireStatementBulk: HireStatementBulk } = inject(MAT_DIALOG_DATA);
  activatedRoute = inject(ActivatedRoute);
  utilityService = inject(UtilityService);
  store = inject(Store);
  action = inject(Actions);
  vm$ = this.store.select(hireStatementFeature.selectHireStatementsState);
  vesselId = this.utilityService.getParamsFromRoute(
    this.activatedRoute.snapshot.root
  )['id'];
  hireStatementId = this.utilityService.getParamsFromRoute(
    this.activatedRoute.snapshot.root
  )['itemId'];
  hireStatements$ = this.store.select(
    hireStatementFeature.selectHireStatements
  );
  hireStatementList: HireStatement[] = [];
  isAdd = true;
  unsubscribe: Subject<boolean> = new Subject();
  hireStatementBulk!: HireStatementBulk;
  form = new FormGroup({
    bulkTypeId: new FormControl<string>('', [Validators.required]),
    startQuantity: new FormControl<number | null>(null, [
      Validators.required,
      greaterThan(0),
      decimalPoint(3),
    ]),
    price: new FormControl<number | null>(null, [
      Validators.required,
      Validators.min(0),
      decimalPoint(2),
    ]),
    dateLoaded: new FormControl<Date | null>(null, [Validators.required]),
  });
  timeAfter: Date | null = null;

  ngOnInit(): void {
    this.subCloseDialog();
    this.initAddEdit();
    this.subToHireStatements();
  }

  ngOnDestroy(): void {
    this.unsubscribe.next(true);
    this.unsubscribe.complete();
  }

  private subToHireStatements(): void {
    this.hireStatements$.pipe(takeUntil(this.unsubscribe)).subscribe((res) => {
      this.hireStatementList = res;
      const index = this.hireStatementList.findIndex(
        (x) => x.hireStatementId === this.hireStatementId
      );
      this.timeAfter = timeAfter(this.hireStatementList[index].deliveryDate, {
        min: 1,
      });
      if (this.isAdd && res.length) {
        this.form.controls.dateLoaded.patchValue(
          timeAfter(this.hireStatementList[index].deliveryDate, { min: 60 })
        );
      }
    });
  }

  private initAddEdit(): void {
    this.isAdd = !this.data.hireStatementBulk;
    if (!this.isAdd) {
      this.hireStatementBulk = this.data.hireStatementBulk;
      this.pathForm(this.hireStatementBulk);
    }
  }

  private subCloseDialog(): void {
    this.action
      .pipe(
        ofType(
          HireStatementBulkActions.add_Hire_Statement_Bulk_Success,
          HireStatementBulkActions.edit_Hire_Statement_Bulk_Success
        )
      )
      .pipe(takeUntil(this.unsubscribe))
      .subscribe(() => {
        this.dialogRef.close();
      });
  }

  private pathForm(hireStatementBulk: HireStatementBulk): void {
    this.form.patchValue({
      ...hireStatementBulk,
    });
  }

  saveHireStatementBulk(): void {
    if (this.form.invalid) {
      this.form.markAllAsTouched();
      return;
    }

    const model = {
      ...this.form.value,
      hireStatementId: this.hireStatementId,
    } as HireStatementBulk;

    if (this.isAdd) {
      this.store.dispatch(
        HireStatementBulkActions.add_Hire_Statement_Bulk({
          hireStatementBulk: model,
        })
      );
    } else {
      this.store.dispatch(
        HireStatementBulkActions.edit_Hire_Statement_Bulk({
          hireStatementBulkId: this.hireStatementBulk.hireStatementBulkId,
          hireStatementBulk: model,
        })
      );
    }
  }
}
