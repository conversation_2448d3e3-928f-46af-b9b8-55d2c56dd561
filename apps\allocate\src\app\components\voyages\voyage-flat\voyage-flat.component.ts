import {
  Component,
  EventEmitter,
  Input,
  OnChanges,
  Output,
} from '@angular/core';
import { PageContainerComponent } from 'libs/components/src/lib/components/page-container/page-container.component';
import { Voyage } from 'libs/services/src/lib/services/voyages/interfaces/voyage.interface';
import { VoyageCardComponent } from '../../../components/voyages/voyage-card/voyage-card.component';
import { NgForOf, NgIf } from '@angular/common';
import { SearchComponent } from 'libs/components/src/lib/components/search/search.component';
import { LoadingDirective } from 'libs/components/src/lib/directives/loading.directive';
import { SearchQuery } from 'libs/components/src/lib/interfaces/search-query.interface';
import { MatPaginatorModule, PageEvent } from '@angular/material/paginator';
import { Column } from 'libs/components/src/lib/components/table/column.model';

@Component({
  selector: 'lha-voyage-flat',
  standalone: true,
  imports: [
    PageContainerComponent,
    VoyageCardComponent,
    NgForOf,
    NgIf,
    SearchComponent,
    LoadingDirective,
    MatPaginatorModule,
  ],
  templateUrl: './voyage-flat.component.html',
  styleUrls: ['./voyage-flat.component.scss'],
})
export class VoyageFlatComponent implements OnChanges {
  @Input() voyageList: Voyage[] = [];
  @Input() loading = false;
  @Input() searchQuery!: SearchQuery;
  @Input() searchColumns: Column<Voyage>[] = [];
  @Output() queryChange = new EventEmitter<SearchQuery>();
  voyageListCopy: Voyage[] = [];
  voyageViewList: Voyage[] = [];

  ngOnChanges(): void {
    this.mapData();
  }

  applyFilter(filterValue: string): void {
    this.searchQuery = {
      ...this.searchQuery,
      pageIndex: 0,
      searchTerm: filterValue,
    };
    this.queryChange.emit(this.searchQuery);
    this.mapData();
  }

  changePage(event: PageEvent): void {
    this.searchQuery = {
      ...this.searchQuery,
      ...event,
    };
    this.queryChange.emit(this.searchQuery);
    this.mapData();
  }

  mapData(): void {
    this.mapSearchData();
    this.sortData();
    this.mapPage();
  }

  mapSearchData(): void {
    this.voyageListCopy = this.voyageList.filter((item) => {
      if (!this.searchQuery.searchTerm) {
        return true;
      }
      return Object.entries(item).find(([key, value]) => {
        return (
          (typeof value === 'string' || typeof value === 'number') &&
          !key.includes('Id') &&
          !key.includes('Date') &&
          this.searchColumns.find((item) => item.property === key) &&
          String(value)
            .toLowerCase()
            .includes(this.searchQuery.searchTerm.toLowerCase())
        );
      });
    });
  }

  sortData(): void {
    if (!this.searchQuery?.active && !this.searchQuery?.direction) {
      return;
    }

    this.voyageListCopy = this.voyageListCopy.sort((a: any, b: any) => {
      if (this.searchQuery.direction === 'asc') {
        return a[this.searchQuery.active] < b[this.searchQuery.active] ? -1 : 1;
      }
      if (this.searchQuery.direction === 'desc') {
        return a[this.searchQuery.active] > b[this.searchQuery.active] ? -1 : 1;
      }
      return 0;
    });
  }

  mapPage(): void {
    const startIndex = this.searchQuery.pageIndex
      ? this.searchQuery.pageIndex * this.searchQuery.pageSize
      : this.searchQuery.pageIndex;
    let endIndex = startIndex + this.searchQuery.pageSize;
    endIndex =
      endIndex > this.voyageListCopy.length
        ? this.voyageListCopy.length
        : endIndex;
    this.voyageViewList = this.voyageListCopy.slice(startIndex, endIndex);
  }
}
