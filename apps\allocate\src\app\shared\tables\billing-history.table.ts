import { ColumnModel } from 'libs/components/src/lib/models/column.model';
import { BillingHistoryTableFields } from '../enums/billing-history-table-fields.enum';

export function InitializeBillingHistoryTable(): ColumnModel[] {
  const columns = [
    new ColumnModel(BillingHistoryTableFields.tableCheckbox, '', 80),
    new ColumnModel(BillingHistoryTableFields.fileName, 'File Name', 150, {
      sortable: true,
    }),
    new ColumnModel(
      BillingHistoryTableFields.createdByName,
      'Created By',
      150,
      {
        sortable: true,
      }
    ),
    new ColumnModel(BillingHistoryTableFields.comments, 'Comments', 150),
  ];
  return columns;
}
