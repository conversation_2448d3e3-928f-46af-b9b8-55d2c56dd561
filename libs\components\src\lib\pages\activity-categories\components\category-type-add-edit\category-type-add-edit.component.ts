import { Date<PERSON>ip<PERSON>, Ng<PERSON><PERSON>, NgIf } from '@angular/common';
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  DestroyRef,
  effect,
  inject,
  OnInit,
  ViewChild,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormsModule } from '@angular/forms';

import { Store } from '@ngrx/store';
import { Actions, ofType } from '@ngrx/effects';

import { DialogModule } from 'primeng/dialog';
import { DropdownModule } from 'primeng/dropdown';
import { InputSwitchModule } from 'primeng/inputswitch';
import { InputTextModule } from 'primeng/inputtext';
import { Table, TableModule } from 'primeng/table';

import { ActivityCategoryTypeActions } from 'libs/services/src/lib/services/maintenance/store/actions/activity-category-type.actions';
import { activityCategoryFeature, activityCategoryTypeFeature } from 'libs/services/src/lib/services/maintenance/store/features';
import { ActivityType } from 'libs/services/src/lib/services/maintenance/enums/activity-type.enum';
import { ConfirmService } from 'libs/components/src/lib/services/confirm.service';
import { ActivityCategoryType } from 'libs/services/src/lib/services/maintenance/interfaces/activity-category-type.interface';
import { ActivityCategoryActions } from 'libs/services/src/lib/services/maintenance/store/actions/activity-category.actions';

@Component({
  selector: 'md-category-type-add-edit',
  standalone: true,
  templateUrl: './category-type-add-edit.component.html',
  imports: [
    DialogModule,
    TableModule,
    NgIf,
    DropdownModule,
    FormsModule,
    InputTextModule,
    InputSwitchModule,
    NgClass,
    DatePipe,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CategoryTypeAddEditComponent implements OnInit {
  @ViewChild('dt') table!: Table;
  private readonly store = inject(Store);
  private readonly confirmService = inject(ConfirmService);
  private readonly cdr = inject(ChangeDetectorRef);
  private readonly actions = inject(Actions);
  private destroyRef = inject(DestroyRef);

  isVisible = this.store.selectSignal(
    activityCategoryFeature.selectIsVisibleAddCategoryType
  );
  activityCategory = this.store.selectSignal(
    activityCategoryFeature.selectActivityCategory
  );

  activityCategoryTypes$ = this.store.selectSignal(
    activityCategoryTypeFeature.selectActivityCategoryTypes
  );

  activityCategoryTypes: ActivityCategoryType[] = [];
  activityType = ActivityType;
  tableWidth = 752;
  searchTerm = '';
  clonedActivityCategoryTypes: { [s: string]: ActivityCategoryType } = {};

  constructor() {
    effect(
      () => {
        if (this.activityCategoryTypes$()) {
          this.activityCategoryTypes = [...this.activityCategoryTypes$()];
        }
      },
      {
        allowSignalWrites: true,
      }
    );

    effect(
      () => {
        if (this.activityCategory() && this.isVisible()) {
          this.store.dispatch(
            ActivityCategoryTypeActions.set_Activity_Category_Type_Id({
              id: this.activityCategory()?.activityCategoryId!,
            })
          );
        }
      },
      {
        allowSignalWrites: true,
      }
    );
  }

  ngOnInit(): void {
    this.handleFailure();
  }

  handleFailure() {
    this.actions
      .pipe(
        ofType(
          ActivityCategoryTypeActions.add_Activity_Category_Type_Failure,
          ActivityCategoryTypeActions.edit_Activity_Category_Type_Failure
        ),
        takeUntilDestroyed(this.destroyRef)
      )
      .subscribe(({ error }) => {
        this.activityCategoryTypes = [...this.activityCategoryTypes$()];
        this.clonedActivityCategoryTypes = {};
        if (this.table) {
          this.table.reset();
        }
      });
  }

  hideDialog() {
    this.store.dispatch(
      ActivityCategoryActions.change_visibility_add_category_type({
        visible: false,
        activityCategory: null,
      })
    );
  }

  onRowEditCancel(activityCategoryType: ActivityCategoryType, index: number) {
    if (activityCategoryType.activityCategoryTypeId === 'add') {
      this.activityCategoryTypes.shift();
    } else {
      this.activityCategoryTypes[index] =
        this.clonedActivityCategoryTypes[
          activityCategoryType.activityCategoryTypeId as string
        ];
    }
    delete this.clonedActivityCategoryTypes[
      activityCategoryType.activityCategoryTypeId as string
    ];
  }

  onRowEditInit(activityCategoryType: ActivityCategoryType, rowIndex: number) {
    this.clonedActivityCategoryTypes[
      activityCategoryType.activityCategoryTypeId as string
    ] = {
      ...activityCategoryType,
    };

    this.activityCategoryTypes[rowIndex] = {
      ...activityCategoryType,
    };
  }

  addNewRow() {
    if (
      this.table &&
      this.table.editingRowKeys &&
      Object.keys(this.table.editingRowKeys).length > 0
    ) {
      return;
    }

    const newRow = {
      activityCategoryTypeId: 'add',
      activityCategoryId: this.activityCategory()?.activityCategoryId!,
      createOutOfPortActivity: false,
      isInboundLifting: false,
      name: '',
    } as ActivityCategoryType;

    this.clonedActivityCategoryTypes['add'] = newRow;

    this.activityCategoryTypes.unshift(newRow);
    setTimeout(() => {
      if (this.table) {
        this.table.initRowEdit(newRow);
        this.cdr.markForCheck();
      }
    }, 0);
  }

  remove(activityCategoryType: ActivityCategoryType) {
    this.confirmService
      .confirm({
        header: 'Delete',
        message: 'Do you want to remove this Activity Category Type?',
        acceptLabel: 'Delete',
        rejectLabel: 'No',
      })
      .then((value) => {
        if (value) {
          this.store.dispatch(
            ActivityCategoryTypeActions.remove_Activity_Category_Type({
              id: activityCategoryType.activityCategoryTypeId,
            })
          );
        }
      });
  }

  onRowEditSave(activityCategoryType: ActivityCategoryType) {
    const model = {
      activityCategoryTypeId: activityCategoryType.activityCategoryTypeId,
      activityCategoryId: this.activityCategory()?.activityCategoryId!,
      createOutOfPortActivity: activityCategoryType.createOutOfPortActivity,
      isInboundLifting: activityCategoryType.isInboundLifting,
      name: activityCategoryType.name,
    } as ActivityCategoryType;

    if (activityCategoryType.activityCategoryTypeId === 'add') {
      this.store.dispatch(
        ActivityCategoryTypeActions.add_Activity_Category_Type({
          activityCategoryType: model,
        })
      );
    } else {
      this.store.dispatch(
        ActivityCategoryTypeActions.edit_Activity_Category_Type({
          id: activityCategoryType.activityCategoryTypeId,
          activityCategoryType: model,
        })
      );
    }
  }

  changeInboundLifting(value: boolean, index: number) {
    if (value) {
      this.activityCategoryTypes[index].createOutOfPortActivity = false;
    }
  }
}
