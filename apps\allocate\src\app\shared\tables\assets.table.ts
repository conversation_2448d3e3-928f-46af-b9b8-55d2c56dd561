import { ColumnModel } from 'libs/components/src/lib/models/column.model';
import { AssetsTableFields } from '../enums/assets-table-fields.enum';

export function InitializeAssetsTable(): ColumnModel[] {
  const columns = [
    new ColumnModel(AssetsTableFields.name, 'Asset Name', 170, {
      sortable: true,
      sortOrder: null,
    }),
    new ColumnModel(AssetsTableFields.isClusterHead, 'Cluster Head ', 150, {
      sortable: true,
      sortOrder: null,
    }),
    new ColumnModel(AssetsTableFields.assetTypeDescription, 'Asset Type', 150, {
      sortable: true,
    }),
    new ColumnModel(AssetsTableFields.currentClientName, 'Operator', 150, {
      sortable: true,
    }),
    new ColumnModel(AssetsTableFields.clusterChildren, 'Cluster Assets', 250),
    new ColumnModel(AssetsTableFields.locationNames, 'Locations', 250),
    new ColumnModel(AssetsTableFields.actions, '', 130),
  ];
  return columns;
}
