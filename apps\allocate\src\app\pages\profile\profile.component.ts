import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Ng<PERSON><PERSON>, Ng<PERSON><PERSON> } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  effect,
  inject,
} from '@angular/core';
import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { Store } from '@ngrx/store';
import { Subject } from 'rxjs';
import { User } from 'libs/auth/src/lib/interfaces/user.interface';
import { UserActions } from 'libs/services/src/lib/services/maintenance/store/actions/users.actions';
import { currentUserFeature } from 'libs/auth/src/lib/store/current-user/current-user.features';

@Component({
  selector: 'lha-home',
  standalone: true,
  imports: [
    Async<PERSON>ipe,
    NgIf,
    NgSwitch,
    ReactiveFormsModule,
    MatCardModule,
    MatButtonModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    MatProgressSpinnerModule,
  ],
  templateUrl: './profile.component.html',
  styleUrls: ['./profile.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ProfileComponent {
  store = inject(Store);
  currentUser = this.store.selectSignal(currentUserFeature.selectUser);
  loading$ = this.store.select(currentUserFeature.selectLoading);
  unsubscribe: Subject<boolean> = new Subject();
  form = new FormGroup({
    lastname: new FormControl<string>('', [Validators.required]),
    firstname: new FormControl<string>('', [Validators.required]),
    emailAddress: new FormControl('', [Validators.required, Validators.email]),
  });

  constructor() {
    effect(
      () => {
        if (this.currentUser()) {
          this.form.patchValue({ ...this.currentUser() });
        }
      },
      {
        allowSignalWrites: true,
      }
    );
  }

  // ngOnInit(): void {
  //   this.store.dispatch(CurrentUserActions.load_Current_User());
  // }

  saveUser(): void {
    if (this.form.invalid) {
      this.form.markAllAsTouched();
      return;
    }
    const user = {
      ...this.currentUser(),
      ...this.form.value,
    } as User;
    this.store.dispatch(
      UserActions.edit_User_Details({ user, userId: this.currentUser().userId })
    );
  }
}
