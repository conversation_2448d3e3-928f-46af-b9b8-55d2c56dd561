import { ColumnModel } from 'libs/components/src/lib/models/column.model';
import { ActivitiesTableFields } from './activities-table-fields.enum';

export function InitializeActivitiesTable(): ColumnModel[] {
  const columns = [
    new ColumnModel(ActivitiesTableFields.name, 'Activity', 150, {
      sortable: true,
      sortOrder: null,
    }),
    new ColumnModel(ActivitiesTableFields.code, 'Code', 150, {
      sortable: true,
      sortOrder: null,
    }),
    new ColumnModel(ActivitiesTableFields.type, 'Activity Type', 170, {
      sortable: true,
    }),
    new ColumnModel(
      ActivitiesTableFields.displayChargeability,
      'Chargeability',
      150,
      {
        sortable: true,
      }
    ),
    new ColumnModel(ActivitiesTableFields.actions, '', 80),
  ];
  return columns;
}
