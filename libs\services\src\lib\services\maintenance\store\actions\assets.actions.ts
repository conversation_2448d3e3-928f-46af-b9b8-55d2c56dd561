import { createActionGroup, emptyProps, props } from '@ngrx/store';

import { Asset } from '../../interfaces/asset.interface';
import { Constant } from 'libs/services/src/lib/services/shared/constant.interface';

import { AssetHistory } from '../../interfaces/asset-history.interface';
import { SearchQuery } from 'libs/components/src/lib/interfaces/search-query.interface';
import { errorProps } from 'libs/components/src/lib/functions/utility.functions';

export const AssetActions = createActionGroup({
  source: 'Asset',
  events: {
    update_Asset_Queries: props<{ query: SearchQuery }>(),

    load_Assets: emptyProps(),
    load_Assets_Success: props<{ assets: Asset[] }>(),
    load_Assets_Failure: errorProps(),

    load_Ports_Page: emptyProps(),
    load_Installations_Wells_Pages: emptyProps(),
    load_Asset_Types: emptyProps(),
    load_Asset_Types_Success: props<{ assetTypes: Constant[] }>(),
    load_Asset_Types_Failure: errorProps(),
    load_Offshore_Assets_By_Location: emptyProps(),
    load_Offshore_Assets_By_Location_Success: props<{
      offshoreAssets: Asset[];
    }>(),
    load_Offshore_Assets_By_Location_Failure: errorProps(),
    load_Cluster_Heads: props<{ id: string }>(),
    load_Cluster_Heads_Success: props<{ assetsHead: Asset[] }>(),
    load_Cluster_Heads_Failure: errorProps(),
    remove_Asset: props<{ id: string }>(),
    remove_Asset_Success: props<{
      asset: Asset;
      successMessage: string;
    }>(),
    remove_Asset_Failure: errorProps(),
    add_Asset: props<{ asset: Asset }>(),
    add_Asset_Success: props<{
      asset: Asset;
      successMessage: string;
    }>(),
    add_Asset_Failure: errorProps(),
    edit_Asset: props<{
      assetId: string;
      asset: Asset;
      addNewHistory: boolean;
    }>(),
    edit_Asset_Success: props<{
      asset: Asset;
      successMessage: string;
    }>(),
    edit_Asset_Failure: errorProps(),

    export_Assets: emptyProps(),
    export_Assets_Success: emptyProps(),
    export_Assets_Failure: errorProps(),

    load_Assets_By_Location: emptyProps(),
    load_Assets_By_Location_Success: props<{ assets: Asset[] }>(),
    load_Assets_By_Location_Failure: errorProps(),
  },
});
