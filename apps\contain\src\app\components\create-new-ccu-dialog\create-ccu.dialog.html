<p-dialog
  [draggable]="false"
  [closable]="false"
  header="Create New CCU"
  [modal]="true"
  [visible]="createCcuDialogVisible()"
  [style]="{ width: '1040px' }"
>
  <ng-template pTemplate="content">
    <div class="cargo-hire-create-dialog">
      <div class="grid-container">
        <form class="mt-10" [formGroup]="cargoForm" (ngSubmit)="submit()">
          <div class="d-flex flex-wrap gap-16">
            <div class="flex-column p-10" style="flex: 1">
              <span class="required-field">CCU ID</span>
              <input
                type="text"
                placeholder="Input CCU ID"
                pInputText
                formControlName="ccuId"
              />
              <small
                class="validation-control-error ml-5"
                *ngIf="cargoForm.controls['ccuId'].hasError('required') && cargoForm.controls['ccuId'].touched"
                >CCU ID is required</small
              >
              <small
                class="validation-control-error ml-5"
                *ngIf="cargoForm.controls['ccuId'].hasError('maxlength') && cargoForm.controls['ccuId'].touched"
                >CCU ID cannot have more than 40 characters</small
              >
              <small
                class="validation-control-error ml-5"
                *ngIf="cargoForm.controls['ccuId'].hasError('pattern') && cargoForm.controls['ccuId'].touched"
              >
                CCU ID can only contain letters, numbers, dashes and hyphens
              </small>
            </div>
            <div class="flex-column p-10" style="flex: 1">
              <span class="required-field">Cargo Description</span>
              <p-dropdown
                [options]="cargoDescriptions()"
                styleClass="new-version"
                [filter]="true"
                placeholder="Select"
                formControlName="cargoDescriptionId"
                optionLabel="description"
                optionValue="cargoDescriptionId"
                inputId="cargoDescriptionId"
                [showClear]="true"
                appendTo="body"
              />
              <small
                class="validation-control-error ml-5"
                *ngIf="cargoForm.controls['cargoDescriptionId'].hasError('required') && cargoForm.controls['cargoDescriptionId'].touched"
                >Cargo Description is required</small
              >
            </div>
          </div>

          <div class="d-flex flex-wrap gap-16">
            <div class="flex-column p-10" style="flex: 1">
              <span class="required-field">Location</span>
              <p-dropdown
                [options]="locations()"
                styleClass="new-version"
                [filter]="true"
                [disabled]="true"
                [readonly]="true"
                formControlName="locationId"
                optionLabel="name"
                optionValue="locationId"
                inputId="locationId"
                appendTo="body"
              />
            </div>
            <div class="flex-column p-10" style="flex: 1">
              <span class="required-field">Family</span>
              <p-dropdown
                [options]="cargoFamilies()"
                styleClass="new-version"
                [filter]="true"
                placeholder="Select"
                formControlName="familyId"
                optionLabel="name"
                optionValue="cargoFamilyId"
                inputId="cargoFamilyId"
                (onChange)="changeFamily($event)"
                [showClear]="true"
                appendTo="body"
              />
              <small
                class="validation-control-error ml-5"
                *ngIf="cargoForm.controls['familyId'].hasError('required') && cargoForm.controls['familyId'].touched"
                >Family is required</small
              >
            </div>
          </div>

          <div class="d-flex flex-wrap gap-16">
            <div class="flex-column p-10" style="flex: 1">
              <span class="required-field">Size</span>
              <p-dropdown
                [options]="cargoSizes()"
                styleClass="new-version"
                [filter]="true"
                formControlName="sizeId"
                placeholder="Select"
                optionLabel="name"
                optionValue="cargoSizeId"
                inputId="cargoSizeId"
                [showClear]="true"
                appendTo="body"
              />
              <small
                class="validation-control-error ml-5"
                *ngIf="cargoForm.controls['sizeId'].hasError('required') && cargoForm.controls['sizeId'].touched"
                >Size is required</small
              >
            </div>

            <div class="flex-column p-10" style="flex: 1">
              <span>Cargo type</span>
              <p-dropdown
                [options]="cargoTypes()"
                styleClass="new-version"
                [filter]="true"
                formControlName="typeId"
                placeholder="Select"
                optionLabel="name"
                optionValue="cargoTypeId"
                inputId="cargoTypeId"
                [showClear]="true"
                appendTo="body"
              />
            </div>
          </div>

          <div class="d-flex flex-wrap gap-16">
            <div class="flex-column p-10" style="flex: 1">
              <span class="required-field">Owner</span>
              <p-dropdown
                [options]="vendors()"
                styleClass="new-version"
                [filter]="true"
                formControlName="vendorId"
                placeholder="Select"
                optionLabel="vendorName"
                optionValue="vendorId"
                inputId="vendor"
                [showClear]="true"
                appendTo="body"
              />
              <small
                class="validation-control-error ml-5"
                *ngIf="cargoForm.controls['vendorId'].hasError('required') && cargoForm.controls['vendorId'].touched"
                >Owner is required</small
              >
            </div>

            <div class="flex-column p-10" style="flex: 1">
              <span class="required-field">Category</span>
              <p-dropdown
                [options]="cargoCategories"
                styleClass="new-version"
                [filter]="true"
                formControlName="category"
                optionLabel="name"
                placeholder="Select"
                optionValue="value"
                inputId="category"
                [showClear]="true"
                appendTo="body"
              />
              <small
                class="validation-control-error ml-5"
                *ngIf="cargoForm.controls['category'].hasError('required') && cargoForm.controls['category'].touched"
                >Category is required</small
              >
            </div>
          </div>

          <div class="d-flex flex-wrap gap-16">
            <div class="flex-column p-10" style="flex: 1">
              <span class="required-field">Length (mm)</span>
              <p-inputNumber
                mode="decimal"
                formControlName="length"
                placeholder="Input length"
                [inputId]="'length'"
                [minFractionDigits]="0"
                [maxFractionDigits]="2"
                placeholder="Input Length"
                [tabindex]="0"
                [min]="cargoSizesMinMax.length.mm.min"
                [max]="cargoSizesMinMax.length.mm.max"
              ></p-inputNumber>
              <small
                class="validation-control-error ml-5"
                *ngIf="cargoForm.controls['length'].hasError('required') && cargoForm.controls['length'].touched"
                >Length is required</small
              >
              <small
                class="validation-control-error ml-5"
                *ngIf="cargoForm.controls['length'].hasError('min') && cargoForm.controls['length'].touched"
                >Length should be greater than
                {{cargoSizesMinMax.length.mm.min}}</small
              >
              <small
                class="validation-control-error ml-5"
                *ngIf="cargoForm.controls['length'].hasError('max') && cargoForm.controls['length'].touched"
                >Length too long. Max 100ft
                ({{cargoSizesMinMax.length.mm.max}}mm).</small
              >
            </div>

            <div class="flex-column p-10" style="flex: 1">
              <span class="required-field">Width (mm)</span>
              <p-inputNumber
                mode="decimal"
                formControlName="width"
                placeholder="Input width"
                [inputId]="'width'"
                [minFractionDigits]="0"
                [maxFractionDigits]="2"
                placeholder="Input width"
                [tabindex]="0"
                [min]="cargoSizesMinMax.width.mm.min"
                [max]="cargoSizesMinMax.width.mm.max"
              ></p-inputNumber>
              <small
                class="validation-control-error ml-5"
                *ngIf="cargoForm.controls['width'].hasError('required') && cargoForm.controls['width'].touched"
                >Width is required</small
              >
              <small
                class="validation-control-error ml-5"
                *ngIf="cargoForm.controls['width'].hasError('min') && cargoForm.controls['width'].touched"
                >Width should be greater than {{cargoSizesMinMax.width.mm.min}}
              </small>
              <small
                class="validation-control-error ml-5"
                *ngIf="cargoForm.controls['width'].hasError('max') && cargoForm.controls['width'].touched"
                >Width too wide. Max 40ft ({{cargoSizesMinMax.width.mm.max}}mm).
              </small>
            </div>
          </div>

          <div class="d-flex flex-wrap gap-16">
            <div class="flex-column p-10" style="flex: 1">
              <span>Heigth (mm)</span>
              <p-inputNumber
                mode="decimal"
                formControlName="height"
                placeholder="Input height"
                [inputId]="'height'"
                [minFractionDigits]="0"
                [maxFractionDigits]="2"
                placeholder="Input height"
                [tabindex]="0"
              ></p-inputNumber>
              <small
                class="validation-control-error ml-5"
                *ngIf="cargoForm.controls['height'].hasError('min') && cargoForm.controls['height'].touched"
                >Height should be greater than 0.1
              </small>
              <small
                class="validation-control-error ml-5"
                *ngIf="cargoForm.controls['height'].hasError('max') && cargoForm.controls['height'].touched"
                >Max allowed value is 9 digits
              </small>
            </div>

            <div class="flex-column p-10" style="flex: 1">
              <span>Tare Mass (Kg)</span>
              <p-inputNumber
                mode="decimal"
                formControlName="tareMass"
                placeholder="Input tare mass"
                [inputId]="'tareMass'"
                [minFractionDigits]="0"
                [maxFractionDigits]="2"
                placeholder="Input tare mass"
                [tabindex]="0"
              ></p-inputNumber>
              <small
                class="validation-control-error ml-5"
                *ngIf="cargoForm.controls['tareMass'].hasError('max') && cargoForm.controls['tareMass'].touched"
                >Max allowed value is 9 digits
              </small>
            </div>
          </div>

          <div class="d-flex flex-wrap gap-16">
            <div class="flex-column p-10" style="flex: 1">
              <span>Max Gross Weight (Kg)</span>
              <p-inputNumber
                mode="decimal"
                formControlName="maxGrossWeight"
                [inputId]="'maxGrossWeight'"
                placeholder="Input max gross weight"
                [minFractionDigits]="0"
                [maxFractionDigits]="2"
                placeholder="Input max gross weight"
                [tabindex]="0"
              ></p-inputNumber>
              <small
                class="validation-control-error ml-5"
                *ngIf="cargoForm.controls['maxGrossWeight'].hasError('max') && cargoForm.controls['maxGrossWeight'].touched"
                >Max allowed value is 9 digits
              </small>
            </div>

            <div class="flex-column p-10" style="flex: 1">
              <span>Certificate Test Date</span>
              <p-calendar
                [inputId]="'certificateTestDate'"
                [tabindex]="0"
                placeholder="Select"
                [showIcon]="true"
                [showTime]="false"
                [showSeconds]="false"
                dateFormat="dd/mm/yy"
                formControlName="certificateTestDate"
                (keydown)="$event.stopPropagation()"
                appendTo="body"
              >
              </p-calendar>
            </div>
          </div>
          <div class="d-flex flex-wrap gap-16">
            <div class="flex-column p-10" style="flex: 1">
              <div class="label-color d-flex align-items-center">
                <p-inputSwitch formControlName="isDeckCargo" />
                <span class="base-color ml-8 mb-3 fs-13">Is Deck Cargo</span>
              </div>
            </div>
          </div>
          <div class="d-flex flex-wrap gap-16">
            <div class="flex-column p-10" style="flex: 1">
              <div class="label-color d-flex align-items-center">
                <p-inputSwitch formControlName="isPool" />
                <span class="base-color ml-8 mb-3 fs-13">Is Pool</span>
              </div>
            </div>

            <div
              class="flex-column p-10"
              style="flex: 1"
              *ngIf="cargoForm.get('isPool')?.value === true"
            >
              <span class="required-field">Pool</span>
              <p-dropdown
                [options]="pools()"
                styleClass="new-version"
                [filter]="true"
                formControlName="poolId"
                placeholder="Select"
                optionLabel="name"
                optionValue="poolId"
                inputId="vendor"
                [showClear]="true"
                appendTo="body"
              />
              <small
                class="validation-control-error ml-5"
                *ngIf="cargoForm.controls['poolId'].hasError('required') && cargoForm.get('isPool')?.value === true && cargoForm.controls['isPool'].touched"
                >Pool is required</small
              >
            </div>
          </div>
        </form>
      </div>
    </div>
  </ng-template>
  <ng-template pTemplate="footer">
    <button class="btn-tertiary" type="button" (click)="hideDialog()">
      Cancel
    </button>
    <button
      class="btn-primary"
      type="button"
      (click)="submit()"
      [disabled]="!cargoForm.valid || cargoForm.pristine"
    >
      Add
    </button>
  </ng-template>
</p-dialog>
