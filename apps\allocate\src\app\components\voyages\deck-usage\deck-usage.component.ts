import { Component, OnInit, ViewChild, computed, inject } from '@angular/core';
import { NgForOf, NgIf } from '@angular/common';
import { FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { Store } from '@ngrx/store';
import {
  deckUsagesFeature,
  voyagesFeature,
} from 'libs/services/src/lib/services/voyages/store/features';
import { SearchQuery } from 'libs/components/src/lib/interfaces/search-query.interface';
import { vesselsInitialState } from 'libs/services/src/lib/services/vessels/store/state/vessels.state';
import { DeckUsage } from 'libs/services/src/lib/services/voyages/interfaces/deck-usage.interface';
import { DeckUsageActions } from 'libs/services/src/lib/services/voyages/store/actions/deck-usage.actions';
import { IsVoyageLockedDisableDirective } from '../../../shared/directives/is-voyage-locked-disable.directive';
import { VoyageStatus } from 'libs/components/src/lib/enums/voyage-status.enum';
import { Table, TableModule } from 'primeng/table';
import { ButtonModule } from 'primeng/button';
import { CardModule } from 'primeng/card';
import { DialogService, DynamicDialogRef } from 'primeng/dynamicdialog';
import { DeckUsageAddEditComponent } from '../deck-usage-add-edit/deck-usage-add-edit.component';
import { ConfirmationService } from 'primeng/api';
import { TooltipModule } from 'primeng/tooltip';
import { InputTextModule } from 'primeng/inputtext';
import { ColumnModel } from 'libs/components/src/lib/models/column.model';
import { FieldType } from 'libs/components/src/lib/enums';
import { ConfirmDialogModule } from 'primeng/confirmdialog';

@Component({
  selector: 'lha-deck-usage',
  standalone: true,
  imports: [
    NgIf,
    NgForOf,
    TableModule,
    ButtonModule,
    CardModule,
    TooltipModule,
    InputTextModule,
    ReactiveFormsModule,
    IsVoyageLockedDisableDirective,
    ConfirmDialogModule,
    DeckUsageAddEditComponent,
  ],
  templateUrl: './deck-usage.component.html',
  styleUrls: ['./deck-usage.component.scss'],
  providers: [DialogService, ConfirmationService],
})
export class DeckUsageComponent implements OnInit {
  store = inject(Store);
  dialogService = inject(DialogService);
  confirmationService = inject(ConfirmationService);

  searchInForm = new FormGroup({
    searchString: new FormControl(''),
  });

  searchOutForm = new FormGroup({
    searchString: new FormControl(''),
  });

  deckUsageState = this.store.selectSignal(
    deckUsagesFeature.selectDeckUsagesState
  );
  voyage = this.store.selectSignal(voyagesFeature.selectActiveVoyage);
  query: SearchQuery = vesselsInitialState.query;
  ref: DynamicDialogRef | undefined;

  // Mutable copies of deck usage data for sorting
  deckUsagesInMutable = computed(() => [...this.deckUsageState().deckUsagesIn]);
  deckUsagesOutMutable = computed(() => [...this.deckUsageState().deckUsagesOut]);

  columns: ColumnModel[] = [
    new ColumnModel('assetName', 'Asset', 250, {
      sortable: true,
      fieldType: FieldType.text,
    }),
    new ColumnModel('numberOfLifts', 'Lifts', 100, {
      sortable: true,
      fieldType: FieldType.number,
    }),
    new ColumnModel('totalWeight', 'Kg Total', 120, {
      sortable: true,
      fieldType: FieldType.number,
    }),
    new ColumnModel('clientName', 'Operator', 180, {
      sortable: true,
      fieldType: FieldType.text,
    }),
  ];
  voyageStatus = VoyageStatus;

  ngOnInit(): void {
    this.loadDeckUsageLists();
  }

  loadDeckUsageLists(): void {
    this.store.dispatch(DeckUsageActions.load_Deck_Usage_Lists());
  }

  exportDeckIn(): void {
    this.store.dispatch(
      DeckUsageActions.export_Deck_Usages({ deckUsageType: 'In' })
    );
  }

  exportDeckOut(): void {
    this.store.dispatch(
      DeckUsageActions.export_Deck_Usages({ deckUsageType: 'Out' })
    );
  }

  onChangeQuery(query: SearchQuery): void {
    this.query = {
      ...this.query,
      ...query,
    };
    this.store.dispatch(
      DeckUsageActions.update_Deck_Usage_Queries({ query: this.query })
    );
  }
  addEditDeckUsage(deckUsage?: DeckUsage): void {
    this.store.dispatch(
      DeckUsageActions.open_Deck_Usage_Add_Edit_Dialog({
        isVisible: true,
        isEdit: !!deckUsage,
        deckUsage: deckUsage || null,
      })
    );
  }

  removeDeckUsage(item: DeckUsage): void {
    this.confirmationService.confirm({
      message: 'Do you want to remove this Cargo?',
      header: 'Delete',
      icon: 'pi pi-exclamation-triangle',
      acceptLabel: 'Delete',
      rejectLabel: 'Cancel',
      acceptIcon: 'none',
      rejectIcon: 'none',
      acceptButtonStyleClass: 'btn-negative-primary',
      rejectButtonStyleClass: 'btn-tertiary',
      accept: () => {
        this.store.dispatch(
          DeckUsageActions.remove_Deck_Usage({ id: item.deckUsageId })
        );
      },
    });
  }

  @ViewChild('dtIn') dtIn!: Table;
  @ViewChild('dtOut') dtOut!: Table;

  onSearchInChange(): void {
    const searchValue = this.searchInForm.get('searchString')?.value || '';
    this.dtIn.filterGlobal(searchValue, 'contains');
  }

  onSearchOutChange(): void {
    const searchValue = this.searchOutForm.get('searchString')?.value || '';
    this.dtOut.filterGlobal(searchValue, 'contains');
  }
}
