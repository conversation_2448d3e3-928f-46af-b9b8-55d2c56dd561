import { Component, computed, inject, OnInit } from '@angular/core';
import { TableComponent } from 'libs/components/src/lib/components/table/table.component';
import { AsyncPipe, NgIf, NgStyle } from '@angular/common';
import { CellTemplateDirective } from 'libs/components/src/lib/components/table/cell-template.directive';
import { MatButtonModule } from '@angular/material/button';
import { IsVoyageLockedDirective } from '../../../shared/directives/is-voyage-locked.directive';
import { Store } from '@ngrx/store';
import { ActivatedRoute } from '@angular/router';
import { bulkRequestFeature, voyagesFeature } from 'libs/services/src/lib/services/voyages/store/features';
import { SearchQuery } from 'libs/components/src/lib/interfaces/search-query.interface';
import { bulkInitialState } from 'libs/services/src/lib/services/voyages/store/states/bulk.state';
import { Column } from 'libs/components/src/lib/components/table/column.model';
import { BulkRequest } from 'libs/services/src/lib/services/voyages/interfaces/bulk-request.interface';
import { BulkRequestActions } from 'libs/services/src/lib/services/voyages/store/actions/bulk-request.actions';
import { confirmActions } from 'libs/components/src/lib/store/confirm.actions';
import { MatIconModule } from '@angular/material/icon';
import { IsVoyageLockedDisableDirective } from '../../../shared/directives/is-voyage-locked-disable.directive';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { VoyageStatus } from 'libs/components/src/lib/enums/voyage-status.enum';

@Component({
  selector: 'lha-bulk-request',
  standalone: true,
  imports: [
    TableComponent,
    AsyncPipe,
    CellTemplateDirective,
    MatButtonModule,
    NgIf,
    NgStyle,
    IsVoyageLockedDirective,
    MatIconModule,
    IsVoyageLockedDisableDirective,
    MatProgressSpinnerModule,
  ],
  templateUrl: './bulk-request.component.html',
  styleUrls: ['./bulk-request.component.scss'],
})
export class BulkRequestComponent implements OnInit {
  store = inject(Store);
  route = inject(ActivatedRoute);
  bulkRequestsState = this.store.selectSignal(bulkRequestFeature.selectBulkRequestState);
  voyage = this.store.selectSignal(voyagesFeature.selectActiveVoyage);
  query: SearchQuery = bulkInitialState.query;
  columns: Column<BulkRequest>[] = [
    new Column('bulkType', 'Bulk Type', { sortHeader: true }),
    new Column('client', 'Operator', { sortHeader: true }),
    new Column('billedAsset', 'Asset', { sortHeader: true }),
    new Column('quantity', 'Quantity', { sortHeader: true }),
  ];
  voyageStatus = VoyageStatus;

  dynamicColumns = computed(() => {
    if (this.voyage()?.voyageStatus !== this.voyageStatus.Completed) {
      return [
        ...this.columns,
        new Column('bulkRequestId', 'Actions', {
          attributeData: [{ name: 'data-exclude', value: 'true' }],
          attributeHeaderData: [{ name: 'data-exclude', value: 'true' }],
        }),
      ];
    }
    return this.columns;
  });

  ngOnInit(): void {
    this.loadLists();
  }

  loadLists(): void {
    this.store.dispatch(BulkRequestActions.load_Bulk_Request_Lists());
  }

  exportBulkRequests(): void {
    this.store.dispatch(BulkRequestActions.export_Bulk_Requests());
  }

  onChangeQuery(query: SearchQuery): void {
    this.query = {
      ...this.query,
      ...query,
    };
    this.store.dispatch(
      BulkRequestActions.update_Bulk_Request_Queries({ query: this.query })
    );
  }

  addEditBulkRequest(bulkRequest?: BulkRequest): void {
    this.store.dispatch(
      BulkRequestActions.open_Bulk_Request_Dialog({ bulkRequest })
    );
  }

  removeBulkRequest(item: BulkRequest): void {
    this.store.dispatch(
      confirmActions.request_Confirmation({
        titles: {
          title:
            'Warning, Deleting this Bulk Request may affect other parts of Allocate. Do you want to remove this Bulk Request?',
          btnConfirm: 'Yes Delete',
        },
        confirm: BulkRequestActions.remove_Bulk_Request({
          id: item.bulkRequestId,
        }),
      })
    );
  }
}
