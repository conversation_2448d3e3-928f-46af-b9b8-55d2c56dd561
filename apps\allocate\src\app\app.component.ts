import { Component, computed, inject } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { NgIf } from '@angular/common';

import { filter, take } from 'rxjs';
import { Store } from '@ngrx/store';

import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { ConfirmationService, MenuItem, MessageService } from 'primeng/api';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { ToastModule } from 'primeng/toast';

import { ToastService } from 'libs/components/src/lib/services/toast.service';
import { ConfirmService } from 'libs/components/src/lib/services/confirm.service';

import { authFeature } from 'libs/auth/src/lib/store/auth.feature';
import { currentUserFeature } from 'libs/auth/src/lib/store/current-user/current-user.features';
import { clientLocationsFeature } from 'libs/services/src/lib/services/client-locations/store/features';
import { locationsFeature } from 'libs/services/src/lib/services/maintenance/store/features';
import { UserRole } from 'libs/auth/src/lib/constants/userRole.enum';
import { HeaderComponent } from 'libs/components/src/lib/components/header/header.component';
import { SidebarNavigationComponent } from 'libs/components/src/lib/components/sidebar-navigation/sidebar-navigation.component';
import { AuthActions } from 'libs/auth/src/lib/store/auth.actions';
import { AppActions } from './store/actions/app.actions';

@Component({
  selector: 'lha-allocate-web',
  templateUrl: './app.component.html',
  standalone: true,
  imports: [
    RouterOutlet,
    NgIf,
    HeaderComponent,
    SidebarNavigationComponent,
    ConfirmDialogModule,
    ProgressSpinnerModule,
    ToastModule,
  ],
  providers: [
    ConfirmationService,
    ConfirmService,
    MessageService,
    ToastService,
  ],
})
export class AllocateWebComponent {
  private readonly store = inject(Store);

  currentUser = this.store.selectSignal(currentUserFeature.selectUser);
  hasAllocatePermissions = this.store.selectSignal(
    currentUserFeature.selectHasAllocatePermissions
  );
  isLoggedIn = this.store.selectSignal(authFeature.selectIsLoggedIn);
  currentUserLoading = this.store.selectSignal(
    currentUserFeature.selectLoading
  );
  clientLocations = this.store.selectSignal(
    clientLocationsFeature.selectClientLocations
  );
  locations = this.store.selectSignal(locationsFeature.selectLocations);
  url = window.location.origin;
  userRole = UserRole;
  showPin: boolean = false;
  navItems: MenuItem[] = [
    {
      label: 'Voyages',
      routerLink: '/voyages',
      icon: 'voyage-icon',
      permissions: [],
    },
    {
      label: 'Billing',
      routerLink: '/billing',
      icon: 'billing-icon',
      permissions: [],
    },
    {
      label: 'Maintenance',
      routerLink: '/maintenance',
      icon: 'maintenance-icon',
      permissions: [UserRole.Admin, UserRole.SupportUser],
      items: [
        {
          label: 'Operators',
          routerLink: 'maintenance/operators',
          permissions: [],
        },
        {
          label: 'Assets',
          routerLink: 'maintenance/assets',
          permissions: [],
        },
        {
          label: 'Distances',
          routerLink: 'maintenance/distances',
          permissions: [],
        },
        {
          label: 'Activity Categories',
          routerLink: 'maintenance/activity-categories',
          permissions: [],
        },
        {
          label: 'Activity',
          routerLink: 'maintenance/activity',
          permissions: [],
        },
      ]
    },
    {
      label: 'Vessels',
      routerLink: '/vessels',
      icon: 'vessels-icon',
      permissions: [],
    },
    {
      label: 'Settings',
      routerLink: '/settings',
      icon: 'settings-icon',
      permissions: [UserRole.SupportUser],
    },
  ];

  title = computed(() => {
    if (this.hasAllocatePermissions()) {
      return 'Allocate';
    }
    return '';
  });

  ngOnInit() {
    this.store
      .select(authFeature.selectIsLoggedIn)
      .pipe(filter(Boolean), take(1))
      .subscribe(() => {
        this.store.dispatch(AppActions.application_init());
        if (!this.currentUser()) {
          this.store.dispatch(AuthActions.logout());
        }
      });
  }
}
