import { Actions, createEffect, ofType } from '@ngrx/effects';
import { inject } from '@angular/core';
import { VesselsService } from '../../vessels.service';
import { catchError, map, mergeMap, of } from 'rxjs';
import { HttpErrorResponse } from '@angular/common/http';
import { VesselActions } from '../actions/vessels.actions';
import { Vessel } from '../../interfaces/vessel.interface';
import { routerNavigationAction } from '@ngrx/router-store';
import { filter } from 'rxjs/operators';
import { Router } from '@angular/router';
import { UtilityService } from '../../../utility.service';
import { FileService } from '../../../file.service';
import { Unit } from '../../../interfaces/unit.interface';

export const loadVessels = createEffect(
  (
    actions = inject(Actions),
    vesselsService = inject(VesselsService)
  ) => {
    return actions.pipe(
      ofType(
        VesselActions.load_Vessels,
        VesselActions.remove_Vessel_Success
      ),
      mergeMap(() =>
        vesselsService.loadVesselList().pipe(
          map((res: Vessel[]) =>
            VesselActions.load_Vessels_Success({ vessels: res })
          ),
          catchError((error: HttpErrorResponse) =>
            of(VesselActions.load_Vessels_Failure({ error }))
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const loadVessel = createEffect(
  (
    actions = inject(Actions),
    vesselsService = inject(VesselsService),
    utilityService = inject(UtilityService)
  ) => {
    return actions.pipe(
      ofType(routerNavigationAction),
      filter(
        ({ payload }) =>
          payload.routerState.url.includes('vessels') &&
          utilityService.urlIncludesId(payload.routerState.url)
      ),
      map(({ payload }) =>
        utilityService.getParamsFromRoute(payload.routerState.root)
      ),
      mergeMap((params) =>
        vesselsService.loadVessel(params['id']).pipe(
          map((res: Vessel) =>
            VesselActions.load_Vessel_Success({ vessel: res })
          ),
          catchError((error: HttpErrorResponse) =>
            of(VesselActions.load_Vessel_Failure({ error }))
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const loadVesselById = createEffect(
  (actions = inject(Actions), service = inject(VesselsService)) => {
    return actions.pipe(
      ofType(VesselActions.load_Vessel),
      mergeMap(({ id }) =>
        service.loadVessel(id).pipe(
          map((res: Vessel) =>
            VesselActions.load_Vessel_Success({ vessel: res })
          ),
          catchError((error: HttpErrorResponse) =>
            of(VesselActions.load_Vessel_Failure({ error }))
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const loadVesselUnits = createEffect(
  (
    actions = inject(Actions),
    vesselsService = inject(VesselsService)
  ) => {
    return actions.pipe(
      ofType(routerNavigationAction),
      filter(
        ({ payload }) =>
          payload.routerState.url.includes('vessels') &&
          (payload.routerState.url.includes('create') ||
            payload.routerState.url.includes('details'))
      ),
      mergeMap(() =>
        vesselsService.loadVesselUnits().pipe(
          map((res: Unit[]) =>
            VesselActions.load_Vessel_Units_Success({ units: res })
          ),
          catchError((error: HttpErrorResponse) =>
            of(VesselActions.load_Vessel_Units_Failure({ error }))
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const removeVessel = createEffect(
  (actions = inject(Actions), vesselsService = inject(VesselsService)) => {
    return actions.pipe(
      ofType(VesselActions.remove_Vessel),
      mergeMap((action) =>
        vesselsService.removeVessel(action.id).pipe(
          map((res: Vessel) => {
            return VesselActions.remove_Vessel_Success({
              vessel: res,
              successMessage: 'Vessel removed successfully!',
            });
          }),
          catchError((error: HttpErrorResponse) =>
            of(VesselActions.remove_Vessel_Failure({ error: error }))
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const addVessel = createEffect(
  (
    actions = inject(Actions),
    vesselsService = inject(VesselsService),
    router = inject(Router),
    fileService = inject(FileService)
  ) => {
    return actions.pipe(
      ofType(VesselActions.add_Vessel),
      mergeMap((action) =>
        vesselsService
          .addVessel(
            fileService.createFormData(action.vessel, action.img, [
              'construction',
            ])
          )
          .pipe(
            map((res: Vessel) => {
              router.navigate(['vessels', res.vesselId, 'details']);
              return VesselActions.add_Vessel_Success({
                vessel: res,
                successMessage: 'Vessel added successfully!',
              });
            }),
            catchError((error: HttpErrorResponse) =>
              of(VesselActions.add_Vessel_Failure({ error: error }))
            )
          )
      )
    );
  },
  {
    functional: true,
  }
);

export const editVessel = createEffect(
  (
    actions = inject(Actions),
    vesselsService = inject(VesselsService),
    fileService = inject(FileService)
  ) => {
    return actions.pipe(
      ofType(VesselActions.edit_Vessel),
      mergeMap((action) =>
        vesselsService
          .editVessel(
            action.vesselId,
            fileService.createFormData(action.vessel, action.img, [
              'construction',
            ])
          )
          .pipe(
            map((res: Vessel) =>
              VesselActions.edit_Vessel_Success({
                vessel: res,
                successMessage: 'Vessel edited successfully!',
              })
            ),
            catchError((error: HttpErrorResponse) =>
              of(VesselActions.edit_Vessel_Failure({ error: error }))
            )
          )
      )
    );
  },
  {
    functional: true,
  }
);

export const loadVesselPicture = createEffect(
  (actions = inject(Actions), vesselsService = inject(VesselsService),) => {
    return actions.pipe(
      ofType(VesselActions.load_Vessel_Picture),
      mergeMap(({ vesselPictureId }) =>
        vesselsService.loadVesselPicture(vesselPictureId).pipe(
          map((vesselPicture: Blob) =>
            VesselActions.load_Vessel_Picture_Success({
              vesselPicture,
            })
          ),
          catchError((error: HttpErrorResponse) =>
            of(VesselActions.load_Vessel_Picture_Failure({ error: error }))
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);
