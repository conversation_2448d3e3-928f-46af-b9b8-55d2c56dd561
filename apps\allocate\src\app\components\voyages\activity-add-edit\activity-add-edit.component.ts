import { Component, DestroyRef, inject, Input, OnInit } from '@angular/core';
import {
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { NgForOf, NgIf, CommonModule } from '@angular/common';
import { Activity } from 'libs/services/src/lib/services/voyages/interfaces/activity.interface';
import { ActivatedRoute } from '@angular/router';
import { UtilityService } from 'libs/services/src/lib/services/utility.service';
import { Store } from '@ngrx/store';
import { Actions, ofType } from '@ngrx/effects';
import { activitiesFeature } from 'libs/services/src/lib/services/voyages/store/features';
import { ActivityActions } from 'libs/services/src/lib/services/voyages/store/actions/activity.actions';
import {
  getTime,
  makeDateRangeCheck,
} from 'libs/components/src/lib/functions/utility.functions';
import { decimalPoint } from 'libs/components/src/lib/validators/decimal-point';
import { ActivityType } from 'libs/services/src/lib/services/maintenance/interfaces/activity-type.interface';
import { Asset } from 'libs/services/src/lib/services/maintenance/interfaces/asset.interface';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { DialogModule } from 'primeng/dialog';
import { DropdownModule } from 'primeng/dropdown';
import { InputNumberModule } from 'primeng/inputnumber';
import { InputTextModule } from 'primeng/inputtext';
import { CalendarModule } from 'primeng/calendar';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { greaterThan } from 'libs/components/src/lib/validators/greaterThan';
import { combineLatest, startWith } from 'rxjs';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { stripTimezoneOffset } from 'libs/services/src/lib/services/functions/convert-date.utils';

@Component({
  selector: 'lha-activity-add-edit',
  standalone: true,
  imports: [
    FormsModule,
    NgForOf,
    ReactiveFormsModule,
    NgIf,
    MatProgressSpinnerModule,
    CommonModule,
    DialogModule,
    DropdownModule,
    InputNumberModule,
    InputTextModule,
    CalendarModule,
    ProgressSpinnerModule,
  ],
  templateUrl: './activity-add-edit.component.html',
  styleUrls: ['./activity-add-edit.component.scss'],
})
export class ActivityAddEditComponent implements OnInit {
  @Input() id!: string;
  private readonly destroyRef = inject(DestroyRef);
  activatedRoute = inject(ActivatedRoute);
  utilityService = inject(UtilityService);
  store = inject(Store);
  action = inject(Actions);
  isVisibleDialog = this.store.selectSignal(activitiesFeature.selectIsVisible);
  isEdit = this.store.selectSignal(activitiesFeature.selectIsEdit);
  activity = this.store.selectSignal(activitiesFeature.selectActivity);
  activitiesState = this.store.selectSignal(
    activitiesFeature.selectActivitiesState
  );
  loading = this.store.selectSignal(activitiesFeature.selectLoading);
  assets = this.store.selectSignal(activitiesFeature.selectAssets);
  filteredAssets = this.store.selectSignal(
    activitiesFeature.selectFilteredAssets
  );
  activities = this.store.selectSignal(activitiesFeature.selectActivities);
  activityTypes = this.store.selectSignal(
    activitiesFeature.selectActivityTypes
  );
  voyageId = this.utilityService.getParamsFromRoute(
    this.activatedRoute.snapshot.root
  )['id'];
  dependencies = {
    activityNonChargBilledAsset: ['NCH'],
    activityChargBilledAsset: ['DED', 'CHA'],
    activityTypeHCCBilledAssset: ['HCL', 'HCD'],
    activityTypeBilledAsset: ['HCL', 'HCD', 'PASS', 'INT'],
  };
  activityType = '';
  isShowBilledAsset = false;
  form = new FormGroup({
    activityId: new FormControl<string>('', [Validators.required]),
    assetId: new FormControl<string>('', [Validators.required]),
    billedAssetId: new FormControl<string | null>(null, []),
    startDateTime: new FormControl<Date | null>(null, [Validators.required]),
    endDateTime: new FormControl<Date | null>(null, [Validators.required]),
    deckSpaceUsed: new FormControl<number | null>(null, [
      decimalPoint(2),
      greaterThan(0),
    ]),
    comments: new FormControl<string | null>(null, []),
  });
  controls = {
    activityId: this.form.get('activityId'),
    assetId: this.form.get('assetId'),
    billedAssetId: this.form.get('billedAssetId'),
    startDateTime: this.form.get('startDateTime'),
    endDateTime: this.form.get('endDateTime'),
    deckSpaceUsed: this.form.get('deckSpaceUsed'),
    comments: this.form.get('comments'),
  };

  ngOnInit(): void {
    this.action
      .pipe(ofType(ActivityActions.open_Activity_Add_Edit_Dialog))
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((x) => {
        if (!x.isEdit) {
          this.subToActivities();
        }
        if (x.activity !== null) {
          this.pathForm({
            ...x.activity!,
            startDateTime: new Date(x.activity.startDateTime),
            endDateTime: new Date(x.activity.endDateTime),
          });
        }
      });

    this.action
      .pipe(
        ofType(
          ActivityActions.add_Activity_Success,
          ActivityActions.edit_Activity_Success
        )
      )
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(() => {
        this.hideDialog();
      });
    this.subToAsset();
    this.subToActivityChange();
  }

  hideDialog() {
    this.store.dispatch(
      ActivityActions.close_Activity_Add_Edit_Dialog({
        isVisible: false,
        isEdit: false,
        activity: null,
      })
    );
    this.form.reset();
    this.isShowBilledAsset = false;
  }

  private subToActivities(): void {
    if (!this.isEdit() && this.activitiesState().activities.length) {
      this.form.controls.startDateTime.patchValue(
        new Date(
          this.activitiesState().activities[
            this.activitiesState().activities.length - 1
          ].endDateTime
        )
      );
      const startDate = new Date(this.form.controls.startDateTime.value!);
      const endDate = new Date(startDate);
      endDate.setMinutes(endDate.getMinutes() + 1);
      this.form.controls.endDateTime.patchValue(endDate);
    }
  }

  updateEndDateTime(event: Date): void {
    if (!this.isEdit() && this.form.get('endDateTime')?.value === null) {
      const endDate = new Date(event);
      endDate.setMinutes(endDate.getMinutes() + 1);
      this.form.get('endDateTime')?.setValue(endDate);
    }
  }

  private subToAsset(): void {
    combineLatest([
      this.form.controls.assetId.valueChanges.pipe(
        startWith(this.form.controls.assetId.value)
      ),
      this.form.controls.activityId.valueChanges.pipe(
        startWith(this.form.controls.activityId.value)
      ),
    ])
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(([assetId, activityId]) => {
        if (!assetId && !activityId) return;

        const assets = this.filteredAssets();
        if (!assets || assets.length === 0) return;

        const asset = assets.find((item) => item?.assetId === assetId);

        if (!asset && assetId) {
          this.form.controls.assetId.patchValue(null, {
            emitEvent: false,
            onlySelf: true,
          });
        }

        const activityTypes = this.activityTypes();
        if (!activityTypes || activityTypes.length === 0) return;

        const activityType = activityTypes.find(
          (item) => item?.activityId === activityId
        );

        this.changeBilledAssetValidation(activityType, asset);
      });
  }

  clearAsset() {
    this.form.controls.assetId.patchValue(null);
    this.form.controls.assetId.markAsTouched();
  }

  private subToActivityChange(): void {
    this.form.controls.activityId.valueChanges
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((activityId) => {
        const activity = this.activityTypes().find(
          (item) => item.activityId === activityId
        );
        this.activityType = activity?.type || '';

        this.store.dispatch(
          ActivityActions.set_Activity_Type({ activityType: this.activityType })
        );

        if (
          this.activitiesState().activities.length &&
          this.activityType &&
          this.activityType !== 'PASS' &&
          this.activityType !== 'INT'
        ) {
          if (!this.isEdit()) {
            this.form.controls.assetId.patchValue(
              this.activitiesState().activities[
                this.activitiesState().activities.length - 1
              ].assetId
            );
          }
        }

        if (activityId) {
          this.form.controls.assetId.enable();
        } else {
          this.form.controls.assetId.disable();
        }
      });
  }

  clearActivity() {
    this.form.controls.activityId.patchValue(null);
    this.form.controls.activityId.markAsTouched();
    this.form.controls.assetId.patchValue(null);
    this.form.controls.billedAssetId.patchValue(null);
    this.form.controls.assetId.enable();
    this.form.controls.assetId.markAsTouched();
  }

  private pathForm(activity: Activity): void {
    this.form.patchValue({
      ...activity,
    });
  }

  saveActivity(): void {
    if (this.form.invalid) {
      this.form.markAllAsTouched();
      return;
    }

    const value = this.form.value;

    const model = {
      ...value,
      voyageId: this.voyageId,
      startDateTime: stripTimezoneOffset(value.startDateTime!),
      endDateTime: stripTimezoneOffset(value.endDateTime!),
    } as Activity;

    if (!this.isEdit()) {
      this.store.dispatch(ActivityActions.add_Activity({ activity: model }));
    } else {
      this.store.dispatch(
        ActivityActions.edit_Activity({
          activityId: this.activity()?.vesselActivityId!,
          activity: model,
        })
      );
    }
  }

  changeBilledAssetValidation(
    activityType: ActivityType | undefined,
    asset: Asset | undefined
  ): void {
    if (!activityType && !asset) {
      this.isShowBilledAsset = false;
      return;
    }

    if (
      (asset?.assetType === 'POR' &&
        (activityType?.chargeability === 'CHA' ||
          activityType?.chargeability === 'DED') &&
        (activityType.type === 'POR' || activityType?.type === 'POF')) ||
      (asset?.assetType === 'POR' &&
        activityType?.chargeability === 'DED' &&
        activityType.type === 'PASS')
    ) {
      this.form.controls.billedAssetId.setValidators([Validators.required]);
      this.isShowBilledAsset = true;
      if (this.activitiesState().activities.length) {
        this.form.controls.billedAssetId.patchValue(
          this.activitiesState().activities[
            this.activitiesState().activities.length - 1
          ].billedAssetId
        );
      }
    } else {
      this.form.controls.billedAssetId.patchValue(null);
      this.form.controls.billedAssetId.clearValidators();
      this.isShowBilledAsset = false;
    }

    this.form.controls.billedAssetId.updateValueAndValidity({
      onlySelf: true,
      emitEvent: false,
    });
  }

  clearBilledAsset() {
    this.form.controls.billedAssetId.patchValue(null);
    this.form.controls.billedAssetId.markAsTouched();
  }

  checkDateTime(
    startDateTime: Date,
    endDateTime: Date,
    activityId: string
  ): boolean {
    return this.activitiesState().activities.some((item) => {
      const isDateInRange = makeDateRangeCheck(
        new Date(item.startDateTime).toString(),
        new Date(item.endDateTime).toString(),
        false,
        false
      );
      if (activityId) {
        return (
          activityId !== item.vesselActivityId &&
          (isDateInRange(new Date(startDateTime).toString()) ||
            isDateInRange(new Date(endDateTime).toString()))
        );
      }
      return (
        isDateInRange(new Date(startDateTime).toString()) ||
        isDateInRange(new Date(endDateTime).toString())
      );
    });
  }

  checkDateTimeGap(currActivity: Activity, id: string): boolean {
    if (!this.isEdit()) {
      if (!this.activitiesState().activities.length) {
        return false;
      }

      const diffArr = this.activitiesState().activities.map(
        (item) =>
          getTime(item.endDateTime) - getTime(currActivity.startDateTime)
      );

      const currIndex = diffArr
        .map((it) => Math.abs(it))
        .reduce((index, value, i, arr) => {
          return value < arr[index] ? i : index;
        }, 0);

      const isFirst = diffArr.every((item) => item > 0) && diffArr[0] !== 0;
      const isLast = diffArr.every((item) => item <= 0);

      if (isFirst) {
        return (
          getTime(currActivity.endDateTime) !==
          getTime(this.activitiesState().activities[0].startDateTime)
        );
      }

      if (isLast) {
        return (
          getTime(currActivity.startDateTime) !==
          getTime(
            this.activitiesState().activities[
              this.activitiesState().activities.length - 1
            ].endDateTime
          )
        );
      }

      return (
        getTime(currActivity.endDateTime) !==
          getTime(
            this.activitiesState().activities[currIndex + 1].startDateTime
          ) ||
        getTime(currActivity.startDateTime) !==
          getTime(this.activitiesState().activities[currIndex].endDateTime)
      );
    } else {
      if (this.activitiesState().activities.length === 1) {
        return false;
      }
      const currIndex = this.activitiesState().activities.findIndex(
        (item) => item.vesselActivityId === id
      );
      const isHasNext =
        currIndex + 1 !== this.activitiesState().activities.length;
      const isHasPrev = currIndex !== 0;
      return (
        (isHasNext
          ? getTime(currActivity.endDateTime) !==
            getTime(
              this.activitiesState().activities[currIndex + 1].startDateTime
            )
          : false) ||
        (isHasPrev
          ? getTime(currActivity.startDateTime) !==
            getTime(
              this.activitiesState().activities[currIndex - 1].endDateTime
            )
          : false)
      );
    }
  }
}
