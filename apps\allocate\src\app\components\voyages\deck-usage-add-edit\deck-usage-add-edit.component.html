<p-dialog
  [draggable]="true"
  [closable]="true"
  [modal]="true"
  [visible]="isVisibleDialog()"
  [style]="{ width: '640px' }"
>
  <ng-template pTemplate="closeicon">
    <button
      class="p-dialog-header-icon p-dialog-header-close p-link"
      (click)="hideDialog()"
    >
      <span class="pi pi-times"></span>
    </button>
  </ng-template>
  <ng-template pTemplate="header">
    <div class="header">{{ isAdd ? 'Add' : 'Edit' }} Cargo</div>
  </ng-template>

  <ng-template pTemplate="content">
    <div *ngIf="vm() as vm" class="p-fluid">
      <div
        *lhaLoading="vm.loading?.createEdit || false"
        class="deck-usage-add-edit"
      >
        <form [formGroup]="form">
          <div class="d-flex flex-wrap gap-20 mb-16">
            <div class="flex-1 d-flex flex-direction-column gap-4">
              <label class="f-bold"
                >Asset <span style="color: red">*</span></label
              >
              <p-dropdown
                [options]="vm.assets"
                [filter]="true"
                [showClear]="true"
                formControlName="assetId"
                optionValue="assetId"
                optionLabel="name"
                placeholder="Select"
                styleClass="new-version"
                panelStyleClass="new-version-panel"
                appendTo="body"
                filterBy="name"
              ></p-dropdown>
              <small
                class="validation-control-error"
                *ngIf="
                  form.controls.assetId.invalid && form.controls.assetId.touched
                "
              >
                Asset is required
              </small>
            </div>
          </div>

          <div class="d-flex flex-wrap gap-20 mb-16">
            <div class="flex-1 d-flex flex-direction-column gap-4">
              <label class="f-bold"
                >Number Of Lifts <span style="color: red">*</span></label
              >
              <input
                matInput
                formControlName="numberOfLifts"
                type="number"
                [min]="1"
                class="p-inputtext-sm p-d-block p-inputtext"
                placeholder="Number Of Lifts"
              />
              <small
                class="validation-control-error"
                *ngIf="
                  form.controls.numberOfLifts.invalid &&
                  form.controls.numberOfLifts.touched
                "
              >
                <span *ngIf="form.controls.numberOfLifts.hasError('required')"
                  >Number Of Lifts is required</span
                >
                <span
                  *ngIf="form.controls.numberOfLifts.hasError('greaterThan')"
                  >Number Of Lifts should be more than 0</span
                >
              </small>
            </div>

            <div class="flex-1 d-flex flex-direction-column gap-4">
              <label class="f-bold"
                >Total Weight <span style="color: red">*</span></label
              >
              <input
                matInput
                formControlName="totalWeight"
                type="number"
                [min]="1"
                class="p-inputtext-sm p-d-block p-inputtext"
                placeholder="Total Weight"
              />
              <small
                class="validation-control-error"
                *ngIf="
                  form.controls.totalWeight.invalid &&
                  form.controls.totalWeight.touched
                "
              >
                <span *ngIf="form.controls.totalWeight.hasError('required')"
                  >Total Weight is required</span
                >
                <span *ngIf="form.controls.totalWeight.hasError('greaterThan')"
                  >Total Weight should be more than 0</span
                >
              </small>
            </div>
          </div>

          <div class="d-flex flex-wrap gap-20 mb-16">
            <div class="flex-1 d-flex align-items-center">
              <div class="label-color d-flex align-items-center">
                <p-inputSwitch formControlName="isCargoIn"></p-inputSwitch>
                <span class="base-color ml-8 mb-3 fs-13">{{
                  form.get('isCargoIn')?.value ? 'Cargo In' : 'Cargo Out'
                }}</span>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </ng-template>

  <ng-template pTemplate="footer">
    <div class="actions">
      <button class="btn-tertiary" (click)="closeDialog()">Cancel</button>
      <button
        class="btn-primary"
        type="button"
        [disabled]="form.invalid"
        (click)="saveDeckUsage()"
      >
        <ng-container *ngIf="vm() as vm">
          <ng-container *ngIf="vm.loading?.createEdit; else saveText">
            <p-progressSpinner
              styleClass="small-spinner-style"
            ></p-progressSpinner>
          </ng-container>
        </ng-container>
        <ng-template #saveText>{{ isAdd ? 'Add' : 'Save' }}</ng-template>
      </button>
    </div>
  </ng-template>
</p-dialog>
