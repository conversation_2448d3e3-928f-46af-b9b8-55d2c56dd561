import { MaintenanceState } from './maintenance-state.interface';
import { Activity } from './activity.interface';
import { Constant } from 'libs/services/src/lib/services/shared/constant.interface';

export interface ActivityConfigState extends MaintenanceState {
  activities: Activity[];
  activityTypeList: Constant[];
  chargeabilityList: Constant[];
  activityType: string;
  isVisibleAddEdit: boolean;
  activity: Activity | null;
}
