import { Actions, concatLatestFrom, createEffect, ofType } from '@ngrx/effects';
import { inject } from '@angular/core';
import { DatePipe } from '@angular/common';
import { ActivatedRoute } from '@angular/router';

import { catchError, map, mergeMap, of } from 'rxjs';
import { HttpErrorResponse } from '@angular/common/http';
import { BillingsActions } from '../actions/billing.actions';
import { filter, tap } from 'rxjs/operators';
import { Store } from '@ngrx/store';
import { billingsFeature } from '../features/billing.features';
import { BillingService } from '../../shared/services/billing.service';
import { Billing } from '../../shared/interfaces/billing.interface';
import { UtilityService } from 'libs/services/src/lib/services/utility.service';
import { FileService } from 'libs/services/src/lib/services/file.service';
import { BillingHistory } from '../../shared/interfaces/billing-history.interface';

export const loadBillingList = createEffect(
  (
    actions = inject(Actions),
    billingService = inject(BillingService),
    store = inject(Store),
    utilityService = inject(UtilityService),
    activatedRoute = inject(ActivatedRoute)
  ) => {
    return actions.pipe(
      ofType(
        BillingsActions.load_Billings,
        BillingsActions.update_Billing_Queries,
        BillingsActions.lock_Billing_Period_Success,
        BillingsActions.unlock_Billing_Period_Success
      ),
      filter(
        () =>
          !utilityService.getParamsFromRoute(activatedRoute.snapshot.root)['id']
      ),
      concatLatestFrom(() => store.select(billingsFeature.selectQuery)),
      map(([, res]) => res),
      mergeMap(() =>
        billingService.getBillingList().pipe(
          map((res: Billing[]) =>
            BillingsActions.load_Billings_Success({ billings: res })
          ),
          catchError((error: HttpErrorResponse) =>
            of(BillingsActions.load_Billings_Failure({ error: error }))
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const loadBilling = createEffect(
  (actions = inject(Actions), service = inject(BillingService)) => {
    return actions.pipe(
      ofType(
        BillingsActions.load_Billing,
        BillingsActions.calc_Billing_Period_Success,
        BillingsActions.init_Billing_Detail
      ),
      mergeMap(({ billingPeriodId }) =>
        service.getBillingById(billingPeriodId).pipe(
          map((res: Billing) =>
            BillingsActions.load_Billing_Success({ billing: res })
          ),
          catchError((error: HttpErrorResponse) =>
            of(BillingsActions.load_Billings_Failure({ error }))
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const lockBillingPeriod = createEffect(
  (actions = inject(Actions), billingService = inject(BillingService)) => {
    return actions.pipe(
      ofType(BillingsActions.lock_Billing_Period),
      mergeMap((action) =>
        billingService.lockBillingPeriod(action.id).pipe(
          map((res: Billing) =>
            BillingsActions.lock_Billing_Period_Success({
              billing: res,
              successMessage: 'Billing Period locked successfully!',
            })
          ),
          catchError((error: HttpErrorResponse) =>
            of(BillingsActions.load_Billing_Failure({ error: error }))
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const unlockBillingPeriod = createEffect(
  (actions = inject(Actions), billingService = inject(BillingService)) => {
    return actions.pipe(
      ofType(BillingsActions.unlock_Billing_Period),
      mergeMap((action) =>
        billingService.unlockBillingPeriod(action.id).pipe(
          map((res: Billing) =>
            BillingsActions.unlock_Billing_Period_Success({
              billing: res,
              successMessage: 'Billing Period unlocked successfully!',
            })
          ),
          catchError((error: HttpErrorResponse) =>
            of(BillingsActions.unlock_Billing_Period_Failure({ error: error }))
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const calcBillingPeriod = createEffect(
  (actions = inject(Actions), billingService = inject(BillingService)) => {
    return actions.pipe(
      ofType(BillingsActions.calc_Billing_Period),
      mergeMap(({ billingPeriodId, message }) =>
        billingService.calcBillingPeriod(billingPeriodId, message).pipe(
          map(() =>
            BillingsActions.calc_Billing_Period_Success({
              successMessage: 'Billing Period calculated successfully!',
              billingPeriodId,
            })
          ),
          catchError((error: HttpErrorResponse) =>
            of(BillingsActions.calc_Billing_Period_Failure({ error: error }))
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const exportBilling = createEffect(
  (
    actions = inject(Actions),
    billingService = inject(BillingService),
    fileService = inject(FileService)
  ) => {
    return actions.pipe(
      ofType(BillingsActions.export_Billing),
      mergeMap(() =>
        billingService.exportBillingPeriods().pipe(
          map((res: ArrayBuffer) => {
            fileService.downloadFile(res, 'Billing Periods');
            return BillingsActions.export_Billing_Success();
          }),
          catchError((error: HttpErrorResponse) =>
            of(BillingsActions.export_Billing_Failure({ error }))
          )
        )
      )
    );
  },
  {
    // You can omit this part if not needed
    // This indicates that the effect is a pure function and can be tree-shaken during optimization
    functional: true,
  }
);

export const loadBillingHistory = createEffect(
  (actions = inject(Actions), billingService = inject(BillingService)) => {
    return actions.pipe(
      ofType(BillingsActions.load_Billing_History),
      mergeMap((action) =>
        billingService.loadBillingHistory(action.id).pipe(
          map((billingHistory: BillingHistory[]) =>
            BillingsActions.load_Billing_History_Success({ billingHistory })
          ),
          catchError((error: HttpErrorResponse) =>
            of(BillingsActions.load_Billing_History_Failure({ error }))
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const compareBillingHistory = createEffect(
  (
    actions = inject(Actions),
    billingService = inject(BillingService),
    fileService = inject(FileService),
    datePipe = inject(DatePipe)
  ) => {
    return actions.pipe(
      ofType(BillingsActions.compare_Billing_History),
      mergeMap((action) =>
        billingService
          .compareBillingHistory(
            action.billingHistory.map((item) => item.documentId)
          )
          .pipe(
            map((res: ArrayBuffer) => {
              fileService.downloadFile(
                res,
                action.billingHistory[0].fileName +
                  ' - ' +
                  action.billingHistory[1].fileName +
                  ' - ' +
                  datePipe.transform(new Date(), 'dd-MM-yyyy HH.mm')
              );
              return BillingsActions.compare_Billing_History_Success();
            }),
            catchError((error: HttpErrorResponse) =>
              of(BillingsActions.compare_Billing_History_Failure({ error }))
            )
          )
      )
    );
  },
  {
    functional: true,
  }
);

export const downloadBillingHistory = createEffect(
  (
    actions = inject(Actions),
    billingService = inject(BillingService),
    fileService = inject(FileService),
    datePipe = inject(DatePipe)
  ) => {
    return actions.pipe(
      ofType(BillingsActions.download_Billing_History),
      mergeMap((action) =>
        billingService
          .downloadBillingHistory(action.billingHistory.documentId)
          .pipe(
            map((res: ArrayBuffer) => {
              fileService.downloadFile(
                res,
                action.billingHistory.fileName +
                  ' - ' +
                  datePipe.transform(new Date(), 'dd-MM-yyyy HH.mm')
              );
              return BillingsActions.download_Billing_History_Success();
            }),
            catchError((error: HttpErrorResponse) =>
              of(BillingsActions.download_Billing_History_Failure({ error }))
            )
          )
      )
    );
  },
  {
    functional: true,
  }
);
