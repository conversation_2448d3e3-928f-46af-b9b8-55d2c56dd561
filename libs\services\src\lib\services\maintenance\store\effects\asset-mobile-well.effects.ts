import { Actions, concatLatestFrom, createEffect, ofType } from '@ngrx/effects';
import { inject } from '@angular/core';
import { HttpErrorResponse } from '@angular/common/http';

import { filter } from 'rxjs/operators';
import { catchError, map, mergeMap, of } from 'rxjs';
import { Store } from '@ngrx/store';

import { AssetMobileWellService } from '../../asset-mobile-well.service';
import { AssetMobileWellActions } from '../actions/assets-mobile-well.actions';
import { assetMobileWellFeature } from '../features';
import { AssetMobileWell } from '../../interfaces/asset-mobile-well.interface';

export const loadAssetMobileWells = createEffect(
  (
    actions = inject(Actions),
    store = inject(Store),
    assetMobileWellService = inject(AssetMobileWellService)
  ) => {
    return actions.pipe(
      ofType(
        AssetMobileWellActions.set_Asset_Mobile_Well_Id,
        AssetMobileWellActions.load_Asset_Mobile_Wells,
        AssetMobileWellActions.remove_Asset_Mobile_Well_Success,
        AssetMobileWellActions.add_Asset_Mobile_Well_Success,
        AssetMobileWellActions.add_Asset_Mobile_Well_Failure,
        AssetMobileWellActions.edit_Asset_Mobile_Well_Success,
        AssetMobileWellActions.edit_Asset_Mobile_Well_Failure
      ),
      concatLatestFrom(() =>
        store.select(assetMobileWellFeature.selectAssetMobileWellId)
      ),
      map(([, res]) => res),
      filter((res) => !!res),
      mergeMap((action) =>
        assetMobileWellService.loadAssetMobileWells(action).pipe(
          map((res: AssetMobileWell[]) =>
            AssetMobileWellActions.load_Asset_Mobile_Wells_Success({
              assetMobileWells: res,
            })
          ),
          catchError((error: HttpErrorResponse) =>
            of(
              AssetMobileWellActions.load_Asset_Mobile_Wells_Failure({
                error,
              })
            )
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const loadMobileWellsByWellId = createEffect(
  (
    actions = inject(Actions),
    assetMobileWellService = inject(AssetMobileWellService)
  ) => {
    return actions.pipe(
      ofType(AssetMobileWellActions.load_Asset_Mobile_Wells_By_Well_Id),
      mergeMap(({ wellId }) =>
        assetMobileWellService.loadAssetMobileWellsByWellId(wellId).pipe(
          map((res: AssetMobileWell[]) =>
            AssetMobileWellActions.load_Asset_Mobile_Wells_By_Well_Id_Success({
              assetMobileWells: res,
            })
          ),
          catchError((error: HttpErrorResponse) =>
            of(
              AssetMobileWellActions.load_Asset_Mobile_Wells_By_Well_Id_Failure(
                {
                  error: error,
                }
              )
            )
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const removeAssetMobileWell = createEffect(
  (
    actions = inject(Actions),
    assetMobileWellService = inject(AssetMobileWellService)
  ) => {
    return actions.pipe(
      ofType(AssetMobileWellActions.remove_Asset_Mobile_Well),
      mergeMap((action) =>
        assetMobileWellService.removeAssetMobileWell(action.id).pipe(
          map((res: AssetMobileWell) =>
            AssetMobileWellActions.remove_Asset_Mobile_Well_Success({
              assetMobileWell: res,
              successMessage: 'Asset removed successfully!',
            })
          ),
          catchError((error: HttpErrorResponse) =>
            of(
              AssetMobileWellActions.remove_Asset_Mobile_Well_Failure({
                error: error,
              })
            )
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const addAssetMobileWell = createEffect(
  (
    actions = inject(Actions),
    assetMobileWellService = inject(AssetMobileWellService)
  ) => {
    return actions.pipe(
      ofType(AssetMobileWellActions.add_Asset_Mobile_Well),
      mergeMap((action) =>
        assetMobileWellService.addAssetMobileWell(action.assetMobileWell).pipe(
          map((res: AssetMobileWell) =>
            AssetMobileWellActions.add_Asset_Mobile_Well_Success({
              assetMobileWell: res,
              successMessage: 'Asset Mobile added successfully!',
            })
          ),
          catchError((error: HttpErrorResponse) =>
            of(
              AssetMobileWellActions.add_Asset_Mobile_Well_Failure({
                error: error,
              })
            )
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const editAssetMobileWell = createEffect(
  (
    actions = inject(Actions),
    assetMobileWellService = inject(AssetMobileWellService)
  ) => {
    return actions.pipe(
      ofType(AssetMobileWellActions.edit_Asset_Mobile_Well),
      mergeMap((action) =>
        assetMobileWellService
          .editAssetMobileWell(action.id, action.assetMobileWell)
          .pipe(
            map((res: AssetMobileWell) =>
              AssetMobileWellActions.edit_Asset_Mobile_Well_Success({
                assetMobileWell: res,
                successMessage: 'Asset Mobile edited successfully!',
              })
            ),
            catchError((error: HttpErrorResponse) =>
              of(
                AssetMobileWellActions.edit_Asset_Mobile_Well_Failure({
                  error: error,
                })
              )
            )
          )
      )
    );
  },
  {
    functional: true,
  }
);