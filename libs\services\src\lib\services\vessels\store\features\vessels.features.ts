import { createFeature, createReducer, createSelector } from '@ngrx/store';
import { immerOn } from 'ngrx-immer/store';
import { VesselActions } from '../actions/vessels.actions';
import { vesselInitialState } from '../state/vessel-init.state';
import { routerNavigationAction } from '@ngrx/router-store';
import { Vessel } from '../../interfaces/vessel.interface';

export const vesselsReducer = createReducer(
  vesselInitialState,
  immerOn(VesselActions.remove_Vessel, (state) => {
    state.loading.list = true;
  }),
  immerOn(VesselActions.load_Vessels, (state) => {
    state.loading.list = true;
  }),

  immerOn(VesselActions.load_Vessels_Success, (state, { vessels }) => {
    state.vessels = vessels;
    state.loading.list = false;
  }),
  immerOn(VesselActions.load_Vessel_Units_Success, (state, { units }) => {
    state.units = units;
  }),
  immerOn(
    VesselActions.load_Vessel_Success,
    VesselActions.edit_Vessel_Success,
    (state, { vessel }) => {
      state.vessel = vessel;
    }
  ),
  immerOn(
    VesselActions.load_Vessels_Failure,
    VesselActions.remove_Vessel_Success,
    VesselActions.remove_Vessel_Failure,
    (state) => {
      state.loading.list = false;
    }
  ),
  immerOn(
    VesselActions.add_Vessel,
    VesselActions.edit_Vessel,
    VesselActions.load_Vessel,
    routerNavigationAction,
    (state) => {
      state.loading.createEdit = true;
    }
  ),
  immerOn(routerNavigationAction, (state) => {
    state.vessel = {} as Vessel;
  }),
  immerOn(
    VesselActions.add_Vessel_Success,
    VesselActions.add_Vessel_Failure,
    VesselActions.edit_Vessel_Success,
    VesselActions.edit_Vessel_Failure,
    VesselActions.load_Vessel_Success,
    VesselActions.load_Vessel_Failure,
    VesselActions.load_Vessel_Units_Success,
    VesselActions.load_Vessel_Units_Failure,
    (state) => {
      state.loading.createEdit = false;
    }
  )
);

export const vesselsFeature = createFeature({
  name: 'vessels',
  reducer: vesselsReducer,
  extraSelectors: ({ selectLoading }) => ({
    selectListLoader: createSelector(
      selectLoading,
      (selectLoading) => selectLoading.list
    ),
    selectCreateEditLoader: createSelector(
      selectLoading,
      (selectLoading) => selectLoading.createEdit
    ),
  }),
});
