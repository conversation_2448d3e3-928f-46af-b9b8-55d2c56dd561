import {
  ChangeDetectionStrategy,
  Component,
  computed,
  inject,
} from '@angular/core';
import {
  Date<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Ng<PERSON><PERSON>Case,
  NgSwitchDefault,
} from '@angular/common';
import { FormsModule } from '@angular/forms';

import { Store } from '@ngrx/store';

import { DialogModule } from 'primeng/dialog';
import { TableModule } from 'primeng/table';
import { InputTextModule } from 'primeng/inputtext';

import { CustomChipComponent } from 'libs/components/src/lib/components/custom-chip/custom-chip.component';
import { TabViewModule } from 'primeng/tabview';

import { AssetClientsHistoryTableComponent } from '../../../../../../../../apps/masterdata/src/app/components/assets/asset-clients-history-table/asset-clients-history-table.component';
import { ClientsNameHistoryTableComponent } from '../operators-name-history/operators-name-history-table';
import { operatorsFeature } from 'libs/services/src/lib/services/maintenance/store/features/operators.features';
import { OperatorActions } from 'libs/services/src/lib/services/maintenance/store/actions/operators.actions';

@Component({
  standalone: true,
  selector: 'md-operator-history',
  templateUrl: './operators-history-dialog.html',
  imports: [
    DialogModule,
    TableModule,
    InputTextModule,
    NgIf,
    NgFor,
    NgSwitch,
    NgSwitchCase,
    NgSwitchDefault,
    FormsModule,
    CustomChipComponent,
    DatePipe,
    TabViewModule,
    AssetClientsHistoryTableComponent,
    ClientsNameHistoryTableComponent,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class OperatorsHistoryDialogComponent {
  private readonly store = inject(Store);
  isVisible = this.store.selectSignal(
    operatorsFeature.selectIsVisibleHistory
  );
  operator = this.store.selectSignal(operatorsFeature.selectOperator);

  clusterClientAssets = computed(() => {
    const operator = this.operator();
    if (operator) {
      return operator.clientAssets.filter((x) => x.isClusterHead);
    }
    return [];
  });

  installationsClientAssets = computed(() => {
    const operator = this.operator();
    if (operator) {
      return operator.clientAssets.filter(
        (x) => x.assetType === 'OFF' || x.assetType === 'MOB'
      );
    }
    return [];
  });

  wellsClientAssets = computed(() => {
    const operator = this.operator();
    if (operator) {
      return operator.clientAssets.filter((x) => x.assetType === 'WEL');
    }
    return [];
  });

  hideDialog() {
    this.store.dispatch(
      OperatorActions.change_visibility_history({
        visible: false,
        operator: null,
      })
    );
  }
}
