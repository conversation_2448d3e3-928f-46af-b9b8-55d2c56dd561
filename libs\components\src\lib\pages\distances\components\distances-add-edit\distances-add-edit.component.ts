import { NgIf } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  DestroyRef,
  inject,
  OnInit,
} from '@angular/core';
import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

import { Actions, ofType } from '@ngrx/effects';
import { Store } from '@ngrx/store';

import { DialogModule } from 'primeng/dialog';
import { DropdownModule } from 'primeng/dropdown';
import { InputNumberModule } from 'primeng/inputnumber';

import { fieldsMustNotMatch } from 'libs/components/src/lib/validators/fieldsMustNotMatch';
import { greaterThan } from 'libs/components/src/lib/validators/greaterThan';
import { Distance } from 'libs/services/src/lib/services/maintenance/interfaces/distance.interface';
import { DistanceActions } from 'libs/services/src/lib/services/maintenance/store/actions/distance.actions';
import {
  assetsFeature,
  distancesFeature,
} from 'libs/services/src/lib/services/maintenance/store/features';

@Component({
  standalone: true,
  selector: 'md-distances-add-edit',
  templateUrl: './distances-add-edit.component.html',
  imports: [
    ReactiveFormsModule,
    NgIf,
    DialogModule,
    DropdownModule,
    InputNumberModule,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DistancesAddEditComponent implements OnInit {
  private readonly destroyRef = inject(DestroyRef);
  private readonly actions = inject(Actions);
  private readonly store = inject(Store);

  isVisible = this.store.selectSignal(distancesFeature.selectIsVisibleAddEdit);
  distance = this.store.selectSignal(distancesFeature.selectDistance);
  assets = this.store.selectSignal(assetsFeature.selectAssetsForDistances);
  form = new FormGroup(
    {
      baseAssetId: new FormControl<string>('', [Validators.required]),
      toAssetId: new FormControl<string>('', [Validators.required]),
      distanceInMiles: new FormControl<null | number>(null, [
        Validators.required,
        greaterThan(0),
      ]),
    },
    fieldsMustNotMatch('baseAssetId', 'toAssetId')
  );

  controls = {
    baseAssetId: this.form.get('baseAssetId'),
    toAssetId: this.form.get('toAssetId'),
    distanceInMiles: this.form.get('distanceInMiles'),
  };

  ngOnInit() {
    this.actions
      .pipe(ofType(DistanceActions.change_visibility_add_edit))
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(({ distance }) => {
        this.form.reset();
        this.form.markAsUntouched();
        if (distance != null) {
          this.form.patchValue({
            ...distance,
          });
        }
      });
  }

  hideDialog() {
    this.store.dispatch(
      DistanceActions.change_visibility_add_edit({
        visible: false,
        distance: null,
      })
    );
  }

  onSubmit() {
    this.form.markAllAsTouched();

    if (this.form.valid) {
      if (this.distance()) {
        const model = {
          ...this.distance(),
          ...this.form.value,
        } as Distance;
        this.store.dispatch(
          DistanceActions.edit_Distance({
            distance: model,
            distanceId: this.distance()!.distanceId,
          })
        );
      } else {
        this.store.dispatch(
          DistanceActions.add_Distance({
            distance: this.form.value as Distance,
          })
        );
      }
    }
  }
}
