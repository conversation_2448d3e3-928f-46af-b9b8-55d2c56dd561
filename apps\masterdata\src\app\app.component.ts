import { Component, OnInit, computed, inject } from '@angular/core';
import { NgIf } from '@angular/common';
import { RouterOutlet } from '@angular/router';

import { filter, take } from 'rxjs';
import { Store } from '@ngrx/store';

import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { ConfirmationService, MenuItem, MessageService } from 'primeng/api';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { ToastModule } from 'primeng/toast';

import { authFeature } from 'libs/auth/src/lib/store/auth.feature';
import { UserRole } from 'libs/auth/src/lib/constants/userRole.enum';
import { AuthActions } from 'libs/auth/src/lib/store/auth.actions';
import { currentUserFeature } from 'libs/auth/src/lib/store/current-user/current-user.features';
import { clientLocationsFeature } from 'libs/services/src/lib/services/client-locations/store/features';
import { locationsFeature } from 'libs/services/src/lib/services/maintenance/store/features';
import { SidebarNavigationComponent } from 'libs/components/src/lib/components/sidebar-navigation/sidebar-navigation.component';
import { AppActions } from './store/actions/app.actions';
import { ConfirmService } from 'libs/components/src/lib/services/confirm.service';
import { HeaderComponent } from 'libs/components/src/lib/components/header/header.component';
import { ToastService } from 'libs/components/src/lib/services/toast.service';

@Component({
  selector: 'mater-data-web',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss'],
  standalone: true,
  imports: [
    RouterOutlet,
    NgIf,
    HeaderComponent,
    SidebarNavigationComponent,
    ConfirmDialogModule,
    ProgressSpinnerModule,
    ToastModule,
  ],
  providers: [
    ConfirmationService,
    ConfirmService,
    MessageService,
    ToastService,
  ],
})
export class MasterDataWebComponent implements OnInit {
  private readonly store = inject(Store);
  currentUser = this.store.selectSignal(currentUserFeature.selectUser);
  hasMasterDataPermissions = this.store.selectSignal(
    currentUserFeature.selectHasMasterdataPermissions
  );
  isLoggedIn = this.store.selectSignal(authFeature.selectIsLoggedIn);
  currentUserLoading = this.store.selectSignal(
    currentUserFeature.selectLoading
  );
  clientLocations = this.store.selectSignal(
    clientLocationsFeature.selectClientLocations
  );
  locations = this.store.selectSignal(locationsFeature.selectLocations);
  url = window.location.origin;
  userRole = UserRole;
  showPin: boolean = false;
  navItems: MenuItem[] = [
    {
      label: 'Users',
      routerLink: '/users',
      icon: 'users-icon',
      permissions: [],
    },
    {
      label: 'Locations',
      routerLink: null,
      icon: 'locations-icon',
      permissions: [],
      items: [
        {
          label: 'Locations',
          routerLink: '/locations',
          icon: '',
          permissions: [],
        },
        {
          label: 'Assets',
          routerLink: '/assets',
          icon: '',
          permissions: [],
          items: [
            {
              label: 'Clusters',
              routerLink: '/assets/clusters',
              icon: '',
              permissions: [],
            },
            {
              label: 'Ports',
              routerLink: '/assets/ports',
              icon: '',
              permissions: [],
            },
            {
              label: 'Installations',
              routerLink: '/assets/installations',
              icon: '',
              permissions: [],
            },
            {
              label: 'Wells',
              routerLink: '/assets/wells',
              icon: '',
              permissions: [],
            },
          ]
        },
        {
          label: 'Distances',
          routerLink: '/distances',
          icon: '',
          permissions: [],
        },
      ],
    },
    {
      label: 'Operators & Vendors',
      routerLink: null,
      icon: 'operators-vendors-icon',
      permissions: [],
      items: [
        {
          label: 'Operators',
          routerLink: '/operators',
          icon: '',
          permissions: [],
        },
        {
          label: 'Vendors',
          routerLink: '/vendors',
          icon: '',
          permissions: [],
        },
      ],
    },
    {
      label: 'Cargos',
      routerLink: '/cargoes',
      icon: 'cargos-icon',
      permissions: [],
      items: [
        {
          label: 'Cargos',
          routerLink: '/cargoes/list',
          icon: '',
          permissions: [],
        },
        {
          label: 'Descriptions',
          routerLink: '/cargoes/descriptions',
          icon: '',
          permissions: [],
        },
        {
          label: 'Families',
          routerLink: '/cargoes/families',
          icon: '',
          permissions: [],
        },
        {
          label: 'Types',
          routerLink: '/cargoes/types',
          icon: '',
          permissions: [],
        },
        {
          label: 'Sizes',
          routerLink: '/cargoes/sizes',
          icon: '',
          permissions: [],
        },
        {
          label: 'Pools',
          routerLink: '/cargoes/pools',
          icon: '',
          permissions: [],
        },
      ],
    },
    {
      label: 'Transport & Equipment',
      routerLink: null,
      icon: 'transport-equipment-icon',
      permissions: [],
      items: [
        {
          label: 'Cranes',
          routerLink: '/cranes',
          icon: '',
          permissions: [],
        },
        {
          label: 'Vehicles',
          routerLink: '/vehicles',
          icon: '',
          permissions: [],
        },
        {
          label: 'Trailers',
          routerLink: '/trailers',
          icon: '',
          permissions: [],
        },
        {
          label: 'Drivers',
          routerLink: '/drivers',
          icon: '',
          permissions: [],
        },
        {
          label: 'Load cells',
          routerLink: '/load-cells',
          icon: '',
          permissions: [],
        },
      ],
    },
    {
      label: 'Staff',
      routerLink: null,
      icon: 'staff-icon',
      permissions: [],
      items: [
        {
          label: 'Employees',
          routerLink: '/employees',
          icon: '',
          permissions: [],
        },
        {
          label: 'Squads',
          routerLink: '/squads',
          icon: '',
          permissions: [],
        },
      ],
    },
    {
      label: 'Other',
      routerLink: null,
      icon: 'other-icon',
      permissions: [],
      items: [
        {
          label: 'Report Types',
          routerLink: 'report-types',
          icon: '',
          permissions: [],
        },
        {
          label: 'Lifting Pause Reasons',
          routerLink: 'lifting-pause-reasons',
          icon: '',
          permissions: [],
        },
        {
          label: 'Weight Categories',
          routerLink: 'weight-categories',
          icon: '',
          permissions: [],
        },
        {
          label: 'Dangerous Goods',
          routerLink: 'dangerous-goods',
          icon: '',
          permissions: [],
        },
        {
          label: 'Blocking Activities',
          routerLink: 'blocking-activities',
          icon: '',
          permissions: [],
        },
        {
          label: 'Activities',
          routerLink: 'activities',
          icon: '',
          permissions: [],
        },
        {
          label: 'Activity Categories',
          routerLink: 'activity-categories',
          icon: '',
          permissions: [],
        },
      ],
    },
    {
      label: 'Tanks & Bulks',
      routerLink: null,
      icon: 'tanks-bulks-icon',
      permissions: [],
      items: [
        {
          label: 'Tank Types',
          routerLink: '/tank-types',
          icon: '',
          permissions: [],
        },
        {
          label: 'Bulk Types',
          routerLink: '/bulk-types',
          icon: '',
          permissions: [],
        },
        {
          label: 'Units',
          routerLink: '/units',
          icon: '',
          permissions: [],
        },
      ],
    },
  ];

  title = computed(() => {
    if (this.hasMasterDataPermissions()) {
      return 'Master Data';
    }
    return '';
  });

  ngOnInit() {
    this.store
      .select(authFeature.selectIsLoggedIn)
      .pipe(filter(Boolean), take(1))
      .subscribe(() => {
        this.store.dispatch(AppActions.application_init());
        if (!this.currentUser()) {
          this.store.dispatch(AuthActions.logout());
        }
      });
  }
}
