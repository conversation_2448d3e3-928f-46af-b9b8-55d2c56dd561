import { Component, computed, inject, OnInit } from '@angular/core';
import { TableComponent } from 'libs/components/src/lib/components/table/table.component';
import { NgClass, NgIf } from '@angular/common';
import { CellTemplateDirective } from 'libs/components/src/lib/components/table/cell-template.directive';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { Store } from '@ngrx/store';
import {
  tankManagementsFeature,
  voyagesFeature,
} from 'libs/services/src/lib/services/voyages/store/features';
import { SearchQuery } from 'libs/components/src/lib/interfaces/search-query.interface';
import { vesselsInitialState } from 'libs/services/src/lib/services/vessels/store/state/vessels.state';
import { Column } from 'libs/components/src/lib/components/table/column.model';
import { TankManagement } from 'libs/services/src/lib/services/voyages/interfaces/tank-management.interface';
import { TankManagementActions } from 'libs/services/src/lib/services/voyages/store/actions/tank-management.actions';
import { confirmActions } from 'libs/components/src/lib/store/confirm.actions';
import { CustomChipComponent } from 'libs/components/src/lib/components/custom-chip/custom-chip.component';
import { IsVoyageLockedDirective } from '../../../shared/directives/is-voyage-locked.directive';
import { PropertyNamePipe } from 'libs/components/src/lib/pipes/property-name.pipe';
import { IsVoyageLockedDisableDirective } from '../../../shared/directives/is-voyage-locked-disable.directive';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { VoyageStatus } from 'libs/components/src/lib/enums/voyage-status.enum';

@Component({
  selector: 'lha-tank-management',
  standalone: true,
  imports: [
    TableComponent,
    CellTemplateDirective,
    MatButtonModule,
    MatIconModule,
    NgIf,
    NgClass,
    CustomChipComponent,
    IsVoyageLockedDirective,
    PropertyNamePipe,
    IsVoyageLockedDisableDirective,
    MatProgressSpinnerModule,
  ],
  templateUrl: './tank-management.component.html',
  styleUrls: ['./tank-management.component.scss'],
})
export class TankManagementComponent implements OnInit {
  store = inject(Store);
  tankManagementState = this.store.selectSignal(
    tankManagementsFeature.selectTankManagementsState
  );
  voyage = this.store.selectSignal(voyagesFeature.selectActiveVoyage);
  query: SearchQuery = vesselsInitialState.query;

  columns: Column<TankManagement>[] = [
    new Column('bulkTransactionBulkTypeFluidType', 'Type', {
      sortHeader: true,
    }),
    new Column('bulkTransactionTankTypeName', 'Tank', { sortHeader: true }),
    new Column('bulkTransactionAssetName', 'Asset', { sortHeader: true }),
    new Column('bulkTransactionBulkTypeName', 'Bulk', { sortHeader: true }),
    new Column('quantityOnBoarding', 'Quantity', { sortHeader: true }),
    new Column('bulkTransactionBulkTypeUnitName', 'Unit', { sortHeader: true }),
    new Column('status', 'Status', { sortHeader: true }),
    new Column('cleaned', 'Cleaned', { sortHeader: true }),
  ];
  voyageStatus = VoyageStatus;

  dynamicColumns = computed(() => {
    if (this.voyage()?.voyageStatus !== this.voyageStatus.Completed) {
      return [
        ...this.columns,
        new Column('tankStatusId', 'Actions', {
          attributeData: [{ name: 'data-exclude', value: 'true' }],
          attributeHeaderData: [{ name: 'data-exclude', value: 'true' }],
        }),
      ];
    }
    return this.columns;
  });

  ngOnInit(): void {
    this.loadTankManagementsLists();
  }

  loadTankManagementsLists(): void {
    this.store.dispatch(TankManagementActions.load_Tank_Managements_Lists());
  }

  exportTankStatuses(): void {
    this.store.dispatch(TankManagementActions.export_Tank_Statuses());
  }

  onChangeQuery(query: SearchQuery): void {
    this.query = {
      ...this.query,
      ...query,
    };
    this.store.dispatch(
      TankManagementActions.update_Tank_Management_Queries({
        query: this.query,
      })
    );
  }

  addEditTankManagement(tankManagement: TankManagement | null): void {
    this.store.dispatch(
      TankManagementActions.open_Tank_Management_Dialog({ tankManagement })
    );
  }

  removeTankManagement(item: TankManagement): void {
    this.store.dispatch(
      confirmActions.request_Confirmation({
        titles: {
          title: 'Do you want to remove this Tank Status?',
          btnConfirm: 'Yes Delete',
        },
        confirm: TankManagementActions.remove_Tank_Management({
          id: item.tankStatusId,
        }),
      })
    );
  }
}
