<div class="p-50">
    <div class="d-flex justify-content-between align-items-center gap-4">
        <h2>CCUs</h2>
        <button
                class="btn-primary"
                type="button"
                (click)="openCreateCCUDialog()"><em class="pi pi-plus"></em>Create new CCU</button>
    </div>

    <contain-ccu-list-filters [vendorIds]="vendorIds()"></contain-ccu-list-filters>

    <p-table 
             class="mt-10" 
             #ccuTable 
             [paginator]="true" 
             [rows]="20" 
             [rowsPerPageOptions]="[20, 25, 50]"
             [columns]="listColumns()"
             [value]="rows()!"
             [scrollable]="true"
             selectionMode="single">
        <ng-template pTemplate="header" let-columns>
            <tr>
                <th *ngFor="let column of columns" scope="col" [style.min-width.px]="column.width"
                    [style.width.%]="(column.width / tableWidth) * 100">
                    <span>{{ column.name }}</span>
                </th>
            </tr>
        </ng-template>
        <ng-template pTemplate="body" let-item>
            <tr [ngClass]="item.canBeActionedInContainCcuList ? '' : 'disabled_row'"
                (click)="setDialogDetailsVisible(item)">
                <td>{{item.ccuId}}</td>
                <td>{{item.poolName}}</td>
                <td>{{item.groupType}}</td>
                <td>{{item.cargoDescriptionName}}</td>
                <td>{{item.vendorVendorName}}</td>
                <td>{{item.certificateExpireDate | date:'dd/MM/yyyy' }}</td>
                <td>{{item.certificateTestDate | date:'dd/MM/yyyy' }}</td>
                <td>{{item.ccuHireStatus | propertyName : cargoHireStatuses : 'value' : 'name'}}</td>
                <td>{{item.cargoStatus | propertyName : cargoStatuses : 'value' : 'name'}}</td>
                <td (click)="$event.stopPropagation()">
                    <i
                       *ngIf="item.canBeActionedInContainCcuList && item.isCargoHireCreated"
                       (click)="openCargoHireDetailsDialog(item.hireRequestCargoId)"
                       title="Show Hire"
                       class="pi pi-list">
                    </i>
                    <i
                       *ngIf="item.canBeActionedInContainCcuList && !item.isCargoHireCreated && (item.cargoStatus === cargoStatus.InStock || !item.cargoStatus)"
                       (click)="openCreateCargoHireDialog(item, item.hireRequestCargoId)"
                       title="Create Hire"
                       class="pi pi-plus">
                    </i>
                    <i
                       *ngIf="item.canBeActionedInContainCcuList"
                       (click)="openSetCertificateTestDateDialog(item)"
                       title="Set Test Date"
                       class="pi pi-calendar set_test_date_icon ml-10">
                    </i>
                </td>
            </tr>
        </ng-template>
        <ng-template pTemplate="emptymessage" let-columns>
            <tr>
                <td [attr.colspan]="columns.length">
                    No result found that match your current filter or search item
                </td>
            </tr>
        </ng-template>
    </p-table>

    <contain-create-new-ccu-dialog *ngIf="createCCUDialogVisible">
    </contain-create-new-ccu-dialog>

    <contain-cargo-hire-details-dialog
    *ngIf="cargoHireDetailsDialogVisible"
    [dialogVisible]="cargoHireDetailsDialogVisible"
    (dialogToggle)="setCargoHireDetailsDialogVisible()">
    </contain-cargo-hire-details-dialog>

    <contain-create-cargo-hire-dialog
    *ngIf="createCargoHireDialogVisible && selectedCcu"
    [cargo]="selectedCcu"
    [isCcuList]="true"
    [dialogVisible]="createCargoHireDialogVisible"
    (dialogToggle)="setCreateCargoHireDialogVisible()">
    </contain-create-cargo-hire-dialog>

    <contain-ccu-details
    *ngIf="ccuDetailsDialogVisible()">
    </contain-ccu-details>

    <contain-set-certificate-test-date-dialog
    [cargo]="selectedCcu"
    *ngIf="certificateTestDateDialogVisible() && selectedCcu">
    </contain-set-certificate-test-date-dialog>
</div>