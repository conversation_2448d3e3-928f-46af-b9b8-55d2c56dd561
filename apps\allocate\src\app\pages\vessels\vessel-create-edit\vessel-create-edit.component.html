<ng-template #header>
  <div class="vessel__img">
    <lha-upload-img
      [viewMode]="!isView"
      [isAdd]="isAdd"
      [hidePreview]="true"
      [maxImgSize]="maxImgSize"
      [formControl]="imgControl"
    ></lha-upload-img>
    <div class="vessel__img_error" *ngIf="imgControl.touched && !isView">
      <mat-error *ngIf="imgControl.hasError('required')"
        >Image is required</mat-error
      >
      <mat-error *ngIf="imgControl.hasError('maxImageSize')"
        >Maximum size of image 2mb</mat-error
      >
      <mat-error *ngIf="imgControl.hasError('fileTypes')"
        >You can only upload an image of the following formats: .jpg,
        .png</mat-error
      >
    </div>
  </div>
  <div class="form__action">
    <lha-toggle-state
      *ngIf="!isAdd"
      [disabled]="isView"
      (valueChange)="toggleActiveState($event)"
      [(ngModel)]="isVesselActive"
    ></lha-toggle-state>
    <a
      mat-raised-button
      color="warn"
      *ngIf="!isView && isAdd"
      [routerLink]="['/vessels']"
      >Cancel</a
    >
    <button
      mat-raised-button
      color="warn"
      *ngIf="!isView && !isAdd"
      (click)="cancelEdit()"
    >
      Cancel
    </button>
    <button
      mat-raised-button
      color="primary"
      *ngIf="!isView"
      (click)="saveVessel()"
    >
      Save
    </button>
    <button
      mat-raised-button
      color="primary"
      *ngIf="isView"
      (click)="changeMode(false)"
    >
      Edit
    </button>
  </div>
</ng-template>
<lha-page-container [contentRelative]="true" [headerTemplate]="header">
  <form
    [formGroup]="form"
    *lhaLoading="(loading$ | async)!"
    class="vessel__form"
  >
    <div class="form__block" *ngIf="vm$ | async as vm">
      <div class="form__box">
        <mat-label>IMO</mat-label>
        <mat-form-field appearance="outline" hideRequiredMarker="true">
          <span matPrefix class="input-prefix">IMO</span>
          <input
            matInput
            type="number"
            formControlName="imo"
            placeholder="Input text"
          />
          <mat-error *ngIf="form.controls.imo.hasError('required')"
            >IMO is required</mat-error
          >
        </mat-form-field>
      </div>
      <div class="form__box">
        <mat-label>Vessel Name</mat-label>
        <mat-form-field appearance="outline" hideRequiredMarker="true">
          <input
            matInput
            type="text"
            formControlName="name"
            placeholder="Input text"
          />
          <mat-error *ngIf="form.controls.name.hasError('required')"
            >Vessel Name is required</mat-error
          >
        </mat-form-field>
      </div>
      <div class="form__box">
        <mat-label>Flag State</mat-label>
        <div class="flex-1">
          <lha-single-select
            [options]="countryList"
            [hideLabelAfterSelect]="true"
            placeholder="Select..."
            formControlName="country"
            bindValue="name"
          />
          <div
            *ngIf="
              form.controls.country.invalid && form.controls.country.touched
            "
          >
            <mat-error *ngIf="form.controls.country.hasError('required')">
              Country is required.
            </mat-error>
          </div>
        </div>
      </div>
      <div class="form__box">
        <mat-label>Construction Date</mat-label>
        <mat-form-field appearance="outline" hideRequiredMarker="true">
          <input
            matInput
            [matDatepickerFilter]="dateFutureFilter"
            [matDatepicker]="picker"
            formControlName="construction"
            placeholder="Select..."
          />
          <mat-datepicker-toggle
            matIconSuffix
            [for]="picker"
          ></mat-datepicker-toggle>
          <mat-datepicker #picker></mat-datepicker>
          <mat-error *ngIf="form.controls.construction.hasError('required')"
            >Construction Date is required</mat-error
          >
        </mat-form-field>
      </div>
      <div class="form__box">
        <mat-label>MMSI</mat-label>
        <mat-form-field appearance="outline" hideRequiredMarker="true">
          <input
            matInput
            type="number"
            formControlName="mmsi"
            placeholder="Input text"
          />
          <mat-error *ngIf="form.controls.mmsi.hasError('required')"
            >MMSI is required</mat-error
          >
          <mat-error *ngIf="form.controls.mmsi.hasError('greaterThan')"
            >MMSI should be more than 0</mat-error
          >
          <mat-error *ngIf="form.controls.mmsi.hasError('maxLength')"
            >MMSI should be no longer than 9 digits</mat-error
          >
        </mat-form-field>
      </div>
      <div class="form__box">
        <mat-label>Vessel Owner</mat-label>
        <mat-form-field appearance="outline" hideRequiredMarker="true">
          <input
            matInput
            type="text"
            formControlName="vesselOwner"
            placeholder="Input text"
          />
          <mat-error *ngIf="form.controls.vesselOwner.hasError('required')"
            >Vessel Owner is required</mat-error
          >
        </mat-form-field>
      </div>
      <div class="form__box">
        <mat-label>DP Class</mat-label>
        <div class="flex-1">
          <lha-single-select
            [options]="dpClassList"
            [hideLabelAfterSelect]="true"
            formControlName="dpClass"
            placeholder="DP Class"
          />
          <div
            *ngIf="
              form.controls.dpClass.invalid && form.controls.dpClass.touched
            "
          >
            <mat-error *ngIf="form.controls.dpClass.hasError('required')">
              DP Class is required
            </mat-error>
          </div>
        </div>
      </div>
      <div class="form__box">
        <mat-label>Fire Fight Class</mat-label>
        <div class="flex-1">
          <lha-single-select
            [options]="firefightClassList"
            [hideLabelAfterSelect]="true"
            formControlName="fireFightClass"
            placeholder="Select..."
          />
          <div
            *ngIf="
              form.controls.fireFightClass.invalid &&
              form.controls.fireFightClass.touched
            "
          >
            <mat-error
              *ngIf="form.controls.fireFightClass.hasError('required')"
            >
              Fire Fight Class is required
            </mat-error>
          </div>
        </div>
      </div>
      <div class="form__box">
        <mat-label>EORI</mat-label>
        <mat-form-field appearance="outline" hideRequiredMarker="true">
          <input
            matInput
            type="number"
            formControlName="eori"
            placeholder="Input text"
          />
          <mat-error *ngIf="form.controls.eori.hasError('required')"
            >EORI is required</mat-error
          >
          <mat-error *ngIf="form.controls.eori.hasError('greaterThan')"
            >EORI should be more than 0</mat-error
          >
        </mat-form-field>
      </div>
      <div class="form__box">
        <div class="form__box_left">
          <mat-label class="mat-label--plain">Rounded Safe Haven</mat-label>
          <mat-slide-toggle
            formControlName="roundedSafeHaven"
          ></mat-slide-toggle>
        </div>
        <div class="form__box_right">
          <mat-label class="mat-label--plain">ERRV</mat-label>
          <mat-slide-toggle formControlName="errv"></mat-slide-toggle>
        </div>
      </div>
      <div class="form__box">
        <div class="form__box_left">
          <mat-label>Length Overall (LOA)</mat-label>
          <mat-form-field appearance="outline" hideRequiredMarker="true">
            <input
              matInput
              type="number"
              formControlName="length"
              [decimal]="true"
              placeholder="Input text"
            />
            <mat-error *ngIf="form.controls.length.hasError('required')"
              >Length is required</mat-error
            >
            <mat-error *ngIf="form.controls.length.hasError('greaterThan')"
              >Length should be more than 0</mat-error
            >
            <mat-error *ngIf="form.controls.length.hasError('decimalPoint')"
              >Decimal point should be less than 2</mat-error
            >
          </mat-form-field>
        </div>
        <div class="form__box_right">
          <mat-form-field appearance="outline" hideRequiredMarker="true">
            <mat-select formControlName="lengthUnitName" placeholder="Select Unit">
              <mat-option *ngFor="let unit of vm.units" [value]="unit.name">
                {{ unit.name }}
              </mat-option>
            </mat-select>
            <mat-error *ngIf="form.controls.lengthUnitName.hasError('required')"
              >Length Unit is required</mat-error
            >
          </mat-form-field>
        </div>
      </div>
      <div class="form__box">
        <div class="form__box_left">
          <mat-label>Deck Length</mat-label>
          <mat-form-field appearance="outline" hideRequiredMarker="true">
            <input
              matInput
              type="number"
              formControlName="deckLength"
              placeholder="Input text"
              [decimal]="true"
            />
            <mat-error *ngIf="form.controls.deckLength.hasError('required')"
              >Deck Length is required</mat-error
            >
            <mat-error *ngIf="form.controls.deckLength.hasError('greaterThan')"
              >Deck Length should be more than 0</mat-error
            >
            <mat-error *ngIf="form.controls.deckLength.hasError('decimalPoint')"
              >Decimal point should be less than 2</mat-error
            >
          </mat-form-field>
        </div>
        <div class="form__box_right">
          <mat-form-field appearance="outline" hideRequiredMarker="true">
            <mat-select
              formControlName="deckLengthUnitName"
              placeholder="Select Unit"
            >
              <mat-option *ngFor="let unit of vm.units" [value]="unit.name">
                {{ unit.name }}
              </mat-option>
            </mat-select>
            <mat-error *ngIf="form.controls.deckLengthUnitName.hasError('required')"
              >Deck Length Unit is required</mat-error
            >
          </mat-form-field>
        </div>
      </div>
      <div class="form__box">
        <div class="form__box_left">
          <mat-label>Width</mat-label>
          <mat-form-field appearance="outline" hideRequiredMarker="true">
            <input
              matInput
              type="number"
              formControlName="width"
              placeholder="Input text"
              [decimal]="true"
            />
            <mat-error *ngIf="form.controls.width.hasError('required')"
              >Width is required</mat-error
            >
            <mat-error *ngIf="form.controls.width.hasError('greaterThan')"
              >Width should be more than 0</mat-error
            >
            <mat-error *ngIf="form.controls.width.hasError('decimalPoint')"
              >Decimal point should be less than 2</mat-error
            >
          </mat-form-field>
        </div>
        <div class="form__box_right">
          <mat-form-field appearance="outline" hideRequiredMarker="true">
            <mat-select formControlName="widthUnitName" placeholder="Select Unit">
              <mat-option *ngFor="let unit of vm.units" [value]="unit.name">
                {{ unit.name }}
              </mat-option>
            </mat-select>
            <mat-error *ngIf="form.controls.widthUnitName.hasError('required')"
              >Width Unit is required</mat-error
            >
          </mat-form-field>
        </div>
      </div>
      <div class="form__box">
        <div class="form__box_left">
          <mat-label>Deck Width</mat-label>
          <mat-form-field appearance="outline" hideRequiredMarker="true">
            <input
              matInput
              type="number"
              formControlName="deckWidth"
              placeholder="Input text"
              [decimal]="true"
            />
            <mat-error *ngIf="form.controls.deckWidth.hasError('required')"
              >Deck Width is required</mat-error
            >
            <mat-error *ngIf="form.controls.deckWidth.hasError('greaterThan')"
              >Deck Width should be more than 0</mat-error
            >
            <mat-error *ngIf="form.controls.deckWidth.hasError('decimalPoint')"
              >Decimal point should be less than 2</mat-error
            >
          </mat-form-field>
        </div>
        <div class="form__box_right">
          <mat-form-field appearance="outline" hideRequiredMarker="true">
            <mat-select
              formControlName="deckWidthUnitName"
              placeholder="Select Unit"
            >
              <mat-option *ngFor="let unit of vm.units" [value]="unit.name">
                {{ unit.name }}
              </mat-option>
            </mat-select>
            <mat-error *ngIf="form.controls.deckWidthUnitName.hasError('required')"
              >Deck Width Unit is required</mat-error
            >
          </mat-form-field>
        </div>
      </div>
      <div class="form__box">
        <div class="form__box_left">
          <mat-label>Draft</mat-label>
          <mat-form-field appearance="outline" hideRequiredMarker="true">
            <input
              matInput
              type="number"
              formControlName="draft"
              placeholder="Input text"
              [decimal]="true"
            />
            <mat-error *ngIf="form.controls.draft.hasError('required')"
              >Draft is required</mat-error
            >
            <mat-error *ngIf="form.controls.draft.hasError('greaterThan')"
              >Draft should be more than 0</mat-error
            >
            <mat-error *ngIf="form.controls.draft.hasError('decimalPoint')"
              >Decimal point should be less than 2</mat-error
            >
          </mat-form-field>
        </div>
        <div class="form__box_right">
          <mat-form-field appearance="outline" hideRequiredMarker="true">
            <mat-select formControlName="draftUnitName" placeholder="Select Unit">
              <mat-option *ngFor="let unit of vm.units" [value]="unit.name">
                {{ unit.name }}
              </mat-option>
            </mat-select>
            <mat-error *ngIf="form.controls.draftUnitName.hasError('required')"
              >Draft Unit is required</mat-error
            >
          </mat-form-field>
        </div>
      </div>
      <div class="form__box">
        <div class="form__box_left">
          <mat-label>Deck Capacity</mat-label>
          <mat-form-field appearance="outline" hideRequiredMarker="true">
            <input
              matInput
              type="number"
              formControlName="deckCapacity"
              placeholder="Input text"
              [decimal]="true"
            />
            <mat-error *ngIf="form.controls.deckCapacity.hasError('required')"
              >Deck Capacity is required</mat-error
            >
            <mat-error
              *ngIf="form.controls.deckCapacity.hasError('greaterThan')"
              >Deck Capacity should be more than 0</mat-error
            >
            <mat-error
              *ngIf="form.controls.deckCapacity.hasError('decimalPoint')"
              >Decimal point should be less than 2</mat-error
            >
          </mat-form-field>
        </div>
        <div class="form__box_right">
          <mat-form-field appearance="outline" hideRequiredMarker="true">
            <mat-select
              formControlName="deckCapacityUnitName"
              placeholder="Select Unit"
            >
              <mat-option *ngFor="let unit of vm.units" [value]="unit.name">
                {{ unit.name }}
              </mat-option>
            </mat-select>
            <mat-error
              *ngIf="form.controls.deckCapacityUnitName.hasError('required')"
              >Deck Capacity Unit is required</mat-error
            >
          </mat-form-field>
        </div>
      </div>
      <div class="form__box">
        <div class="form__box_left">
          <mat-label>Gross Tonnage</mat-label>
          <mat-form-field appearance="outline" hideRequiredMarker="true">
            <input
              matInput
              type="number"
              formControlName="grossTonnage"
              placeholder="Input text"
              [decimal]="true"
            />
            <mat-error *ngIf="form.controls.grossTonnage.hasError('required')"
              >Gross Tonnage is required</mat-error
            >
            <mat-error
              *ngIf="form.controls.grossTonnage.hasError('greaterThan')"
              >Gross Tonnage should be more than 0</mat-error
            >
            <mat-error
              *ngIf="form.controls.grossTonnage.hasError('decimalPoint')"
              >Decimal point should be less than 2</mat-error
            >
          </mat-form-field>
        </div>
        <div class="form__box_right">
          <mat-form-field appearance="outline" hideRequiredMarker="true">
            <mat-select
              formControlName="grossTonnageUnitName"
              placeholder="Select Unit"
            >
              <mat-option *ngFor="let unit of vm.units" [value]="unit.name">
                {{ unit.name }}
              </mat-option>
            </mat-select>
            <mat-error
              *ngIf="form.controls.grossTonnageUnitName.hasError('required')"
              >Gross Tonnage Unit is required</mat-error
            >
          </mat-form-field>
        </div>
      </div>
      <div class="form__box">
        <div class="form__box_left">
          <mat-label>Net Tonnage</mat-label>
          <mat-form-field appearance="outline" hideRequiredMarker="true">
            <input
              matInput
              type="number"
              formControlName="netTonnage"
              placeholder="Input text"
              [decimal]="true"
            />
            <mat-error *ngIf="form.controls.netTonnage.hasError('required')"
              >Net Tonnage is required</mat-error
            >
            <mat-error *ngIf="form.controls.netTonnage.hasError('greaterThan')"
              >Net Tonnage should be more than 0</mat-error
            >
            <mat-error *ngIf="form.controls.netTonnage.hasError('decimalPoint')"
              >Decimal point should be less than 2</mat-error
            >
          </mat-form-field>
        </div>
        <div class="form__box_right">
          <mat-form-field appearance="outline" hideRequiredMarker="true">
            <mat-select
              formControlName="netTonnageUnitName"
              placeholder="Select Unit"
            >
              <mat-option *ngFor="let unit of vm.units" [value]="unit.name">
                {{ unit.name }}
              </mat-option>
            </mat-select>
            <mat-error *ngIf="form.controls.netTonnageUnitName.hasError('required')"
              >Net Tonnage Unit is required</mat-error
            >
          </mat-form-field>
        </div>
      </div>
      <div class="form__box">
        <div class="form__box_left">
          <mat-label>Dead Weight</mat-label>
          <mat-form-field appearance="outline" hideRequiredMarker="true">
            <input
              matInput
              type="number"
              formControlName="deadWeight"
              placeholder="Input text"
              [decimal]="true"
            />
            <mat-error *ngIf="form.controls.deadWeight.hasError('required')"
              >Dead Weight is required</mat-error
            >
            <mat-error *ngIf="form.controls.deadWeight.hasError('greaterThan')"
              >Dead Weight should be more than 0</mat-error
            >
            <mat-error *ngIf="form.controls.deadWeight.hasError('decimalPoint')"
              >Decimal point should be less than 2</mat-error
            >
          </mat-form-field>
        </div>
        <div class="form__box_right">
          <mat-form-field appearance="outline" hideRequiredMarker="true">
            <mat-select
              formControlName="deadWeightUnitName"
              placeholder="Select Unit"
            >
              <mat-option *ngFor="let unit of vm.units" [value]="unit.name">
                {{ unit.name }}
              </mat-option>
            </mat-select>
            <mat-error *ngIf="form.controls.deadWeightUnitName.hasError('required')"
              >Dead Weight Unit is required</mat-error
            >
          </mat-form-field>
        </div>
      </div>
    </div>
  </form>
</lha-page-container>
