import { Route } from '@angular/router';
import { provideEffects } from '@ngrx/effects';
import { provideState } from '@ngrx/store';

import * as assetsEffects from 'libs/services/src/lib/services/maintenance/store/effects/assets.effects';
import * as assetsMobileWellEffects from 'libs/services/src/lib/services/maintenance/store/effects/asset-mobile-well.effects';
import * as operatorsEffects from 'libs/services/src/lib/services/maintenance/store/effects/operators.effects';
import * as siteEffects from 'libs/services/src/lib/services/maintenance/store/effects/site.effects';
import * as districtEffects from 'libs/services/src/lib/services/maintenance/store/effects/district.effects';
import * as areaEffects from 'libs/services/src/lib/services/maintenance/store/effects/area.effects';
import * as blockingActivityEffects from 'libs/services/src/lib/services/maintenance/store/effects/blocking-activity.effects';
import * as distanceEffects from 'libs/services/src/lib/services/maintenance/store/effects/distance.effects';
import * as reportTypeEffects from 'libs/services/src/lib/services/maintenance/store/effects/report-type.effects';
import * as vendorsEffects from 'libs/services/src/lib/services/maintenance/store/effects/vendors.effects';
import * as vendorWarehouseEffects from 'libs/services/src/lib/services/maintenance/store/effects/vendor-warehouse.effects';
import * as usersEffects from 'libs/services/src/lib/services/maintenance/store/effects/users.effects';
import * as cargoesEffects from 'libs/services/src/lib/services/maintenance/store/effects/cargoes.effects';
import * as cargoSizeEffects from 'libs/services/src/lib/services/maintenance/store/effects/cargo-size.effects';
import * as cargoFamilyEffects from 'libs/services/src/lib/services/maintenance/store/effects/cargo-family.effects';
import * as cargoTypeEffects from 'libs/services/src/lib/services/maintenance/store/effects/cargo-type.effects';
import * as cargoDescriptionEffects from 'libs/services/src/lib/services/maintenance/store/effects/cargo-description.effects';
import * as poolsEffects from 'libs/services/src/lib/services/maintenance/store/effects/pool.effects';
import * as cranesEffects from 'libs/services/src/lib/services/maintenance/store/effects/crane.effects';
import * as vehicleEffects from 'libs/services/src/lib/services/maintenance/store/effects/vehicle.effects';
import * as trailerEffects from 'libs/services/src/lib/services/maintenance/store/effects/trailer.effects';
import * as driverEffects from 'libs/services/src/lib/services/maintenance/store/effects/driver.effects';
import * as loadCellEffects from 'libs/services/src/lib/services/maintenance/store/effects/load-cell.effects';
import * as employeeEffects from 'libs/services/src/lib/services/maintenance/store/effects/employee.effects';
import * as squadEffects from 'libs/services/src/lib/services/maintenance/store/effects/squad.effects';
import * as unitsEffects from 'libs/services/src/lib/services/maintenance/store/effects/units.effects';
import * as bulkTypeEffects from 'libs/services/src/lib/services/maintenance/store/effects/bulk-type.effects';
import * as tankTypeEffects from 'libs/services/src/lib/services/maintenance/store/effects/tank-type.effects';
import * as weightCategoryEffects from 'libs/services/src/lib/services/maintenance/store/effects/weight-category.effects';
import * as liftingPauseReasonsEffects from 'libs/services/src/lib/services/maintenance/store/effects/lifting-pause-reasons.effects';
import * as activityEffects from 'libs/services/src/lib/services/maintenance/store/effects/activity-config.effects';
import * as activityCategoriesEffects from 'libs/services/src/lib/services/maintenance/store/effects/activity-category.effects';
import * as blockingActivitiesEffects from 'libs/services/src/lib/services/maintenance/store/effects/blocking-activity.effects';
import * as activityCategoryTypeEffects from 'libs/services/src/lib/services/maintenance/store/effects/activity-category-type.effects';
import * as dangerousGoodsEffects from 'libs/services/src/lib/services/maintenance/store/effects/dangerous-goods.effects';
import * as assetsMDEffects from './store/effects/assets.effects';
import * as locationsMDEffects from './store/effects/locations.effects';
import * as vendorsMDEffects from './store/effects/vendors.effects';
import * as usersMDEffects from './store/effects/users.effects';
import * as cargoMDEffects from './store/effects/cargo.effects';
import * as cranesMDEffects from './store/effects/cranes.effects';
import * as vehiclesMDEffects from './store/effects/vehicles.effects';
import * as trailersMDEffects from './store/effects/trailers.effects';
import * as driversMDEffects from './store/effects/drivers.effects';
import * as loadCellsMDEffects from './store/effects/load-cells.effects';
import * as employeesMDEffects from './store/effects/employees.effects';
import * as squadsMDEffects from './store/effects/squads.effects';
import * as unitsMDEffects from './store/effects/units.effects';
import * as bulkTypesMDEffects from './store/effects/bulk-types.effects';
import * as tankTypesMDEffects from './store/effects/tank-types.effects';
import * as reportTypesMDEffects from './store/effects/report-types.effects';
import * as weightCategoriesMDEffects from './store/effects/weight-categories.effects';
import * as liftingPauseReasonsMDEffects from './store/effects/lifting-pause-reasons.effects';
import * as blockingActivitiesMDEffects from './store/effects/blocking-activities.effects';
import * as dangerousGoodsMDEffects from './store/effects/dangerous-goods.effects';
import * as cargoEventEffects from 'libs/services/src/lib/services/contain/store/effects/cargo-event.effects';
import * as clustersEffects from 'libs/services/src/lib/services/maintenance/store/effects/clusters.effects';

import {
  areaFeature,
  assetMobileWellFeature,
  assetsFeature,
  operatorsFeature,
  siteFeature,
  districtFeature,
  blockingActivityFeature,
  distancesFeature,
  reportTypesFeature,
  vendorsFeature,
  usersFeature,
  cargoesFeature,
  cargoSizeFeature,
  cargoFamilyFeature,
  cargotypeFeature,
  cargoDescriptionsFeature,
  poolsFeature,
  cranesFeature,
  vehiclesFeature,
  trailerFeature,
  driversFeature,
  loadCellFeature,
  employeesFeature,
  squadsFeature,
  unitsFeature,
  bulkTypesFeature,
  tankTypesFeature,
  liftingPauseReasonsFeature,
  weightCategoriesFeature,
  dangerousGoodsFeature,
  activityConfigFeature,
  activityCategoryFeature,
  activityCategoryTypeFeature,
  clustersFeature,
} from 'libs/services/src/lib/services/maintenance/store/features';
import { assetsMDFeature } from './store/features/assets.feature';
import { locationsMDFeature } from './store/features/locations.feature';
import { vendorsMDFeature } from './store/features/vendors.feature';
import { usersMDFeature } from './store/features/users.feature';
import { cargoMDFeature } from './store/features/cargo.feature';
import { cranesMDFeature } from './store/features/cranes.feature';
import { vehiclesMDFeature } from './store/features/vehicles.feature';
import { trailersMDFeature } from './store/features/trailers.feature';
import { driversMDFeature } from './store/features/drivers.feature';
import { loadCellsMDFeature } from './store/features/load-cells.feature';
import { employeesMDFeature } from './store/features/employees.feature';
import { squadsMDFeature } from './store/features/squads.feature';
import { unitsMDFeature } from './store/features/units.feature';
import { tankTypesMDFeature } from './store/features/tank-types.feature';
import { bulkTypesMDFeature } from './store/features/bulk-types.feature';
import { reportTypesMDFeature } from './store/features/report-types.feature';
import { liftingPauseReasonsMDFeature } from './store/features/lifting-pause-reasons.feature';
import { weightCategoriesMDFeature } from './store/features/weight-categories.feature';
import { dangerousGoodsMDFeature } from './store/features/dangerous-goods.feature';
import { blockingActivitiesMDFeature } from './store/features/blocking-activities.feature';
import { cargoEventFeature } from 'libs/services/src/lib/services/contain/store/features/cargo-event.feature';
import { OperatorsPage } from 'libs/components/src/lib/pages/operators/operators.page';
import { DistancesPage } from 'libs/components/src/lib/pages/distances/distances.page';

const cargoesRoutes = () =>
  import('./routings/cargoes.routing').then((m) => m.cargoesRoutes);

const assetsRoutes = () =>
  import('./routings/assets.routing').then((m) => m.assetsRoutes);

export const appRoutes: Route[] = [
  { path: '', redirectTo: 'locations', pathMatch: 'full' },
  {
    path: 'locations',
    loadComponent: () =>
      import('./pages/locations/locations.page').then((p) => p.LocationsPage),
    data: {
      pageName: 'Locations',
    },
    providers: [
      provideEffects([
        locationsMDEffects,
        siteEffects,
        districtEffects,
        areaEffects,
        blockingActivityEffects,
      ]),
      provideState(locationsMDFeature),
      provideState(siteFeature),
      provideState(districtFeature),
      provideState(areaFeature),
      provideState(blockingActivityFeature),
    ],
  },
  {
    path: 'assets',
    loadChildren: assetsRoutes,
    data: {
      pageName: 'Assets',
    },
    providers: [
      provideEffects([
        assetsEffects,
        operatorsEffects,
        assetsMDEffects,
        assetsMobileWellEffects,
        clustersEffects,
      ]),
      provideState(assetsFeature),
      provideState(assetsMDFeature),
      provideState(operatorsFeature),
      provideState(assetMobileWellFeature),
      provideState(clustersFeature),
    ],
  },
  {
    path: 'distances',
    component: DistancesPage,
    data: {
      pageName: 'Distances',
    },
    providers: [
      provideEffects([assetsEffects, distanceEffects]),
      provideState(assetsFeature),
      provideState(distancesFeature),
    ],
  },
  {
    path: 'operators',
    component: OperatorsPage,
    data: {
      pageName: 'Operators',
    },
    providers: [
      provideEffects([operatorsEffects, reportTypeEffects]),
      provideState(operatorsFeature),
      provideState(reportTypesFeature),
    ],
  },
  {
    path: 'vendors',
    loadComponent: () =>
      import('./pages/vendors/vendors.page').then((p) => p.VendorsPage),
    data: {
      pageName: 'Vendors',
    },
    providers: [
      provideEffects([
        vendorsEffects,
        vendorsMDEffects,
        vendorWarehouseEffects,
        districtEffects,
      ]),
      provideState(vendorsFeature),
      provideState(vendorsMDFeature),
      provideState(districtFeature),
    ],
  },
  {
    path: 'users',
    loadComponent: () =>
      import('./pages/users/users.page').then((p) => p.UsersPage),
    data: {
      pageName: 'Users',
    },
    providers: [
      provideEffects([usersMDEffects, usersEffects, operatorsEffects]),
      provideState(usersMDFeature),
      provideState(usersFeature),
      provideState(operatorsFeature),
    ],
  },
  {
    path: 'cargoes',
    loadChildren: cargoesRoutes,
    providers: [
      provideEffects([
        cargoMDEffects,
        cargoesEffects,
        cargoSizeEffects,
        cargoFamilyEffects,
        cargoTypeEffects,
        cargoDescriptionEffects,
        vendorsEffects,
        operatorsEffects,
        poolsEffects,
        cargoEventEffects,
      ]),
      provideState(cargoMDFeature),
      provideState(cargoesFeature),
      provideState(cargoSizeFeature),
      provideState(cargoFamilyFeature),
      provideState(cargotypeFeature),
      provideState(cargoDescriptionsFeature),
      provideState(vendorsFeature),
      provideState(operatorsFeature),
      provideState(poolsFeature),
      provideState(cargoEventFeature),
    ],
  },
  {
    path: 'cranes',
    loadComponent: () =>
      import('./pages/cranes/cranes.page').then((p) => p.CranesPage),
    data: {
      pageName: 'Cranes',
    },
    providers: [
      provideEffects([cranesMDEffects, cranesEffects]),
      provideState(cranesMDFeature),
      provideState(cranesFeature),
    ],
  },
  {
    path: 'vehicles',
    loadComponent: () =>
      import('./pages/vehicles/vehicles.page').then((p) => p.VehiclesPage),
    data: {
      pageName: 'Vehicles',
    },
    providers: [
      provideEffects([vehiclesMDEffects, vehicleEffects]),
      provideState(vehiclesMDFeature),
      provideState(vehiclesFeature),
    ],
  },
  {
    path: 'trailers',
    loadComponent: () =>
      import('./pages/trailers/trailers.page').then((p) => p.TrailersPage),
    data: {
      pageName: 'Trailers',
    },
    providers: [
      provideEffects([trailersMDEffects, trailerEffects]),
      provideState(trailersMDFeature),
      provideState(trailerFeature),
    ],
  },
  {
    path: 'drivers',
    loadComponent: () =>
      import('./pages/drivers/drivers.page').then((p) => p.DriversPage),
    data: {
      pageName: 'Drivers',
    },
    providers: [
      provideEffects([
        driversMDEffects,
        driverEffects,
        vendorsEffects,
        vehicleEffects,
        trailerEffects,
      ]),
      provideState(driversMDFeature),
      provideState(driversFeature),
      provideState(vendorsFeature),
      provideState(vehiclesFeature),
      provideState(trailerFeature),
    ],
  },
  {
    path: 'load-cells',
    loadComponent: () =>
      import('./pages/load-cells/load-cells.page').then((p) => p.LoadCellsPage),
    data: {
      pageName: 'Load cells',
    },
    providers: [
      provideEffects([loadCellEffects, loadCellsMDEffects]),
      provideState(loadCellsMDFeature),
      provideState(loadCellFeature),
    ],
  },
  {
    path: 'employees',
    loadComponent: () =>
      import('./pages/employees/employees.page').then((p) => p.EmployeesPage),
    data: {
      pageName: 'Employees',
    },
    providers: [
      provideEffects([employeesMDEffects, employeeEffects]),
      provideState(employeesFeature),
      provideState(employeesMDFeature),
    ],
  },
  {
    path: 'squads',
    loadComponent: () =>
      import('./pages/squads/squads.page').then((p) => p.SquadsPage),
    data: {
      pageName: 'Squads',
    },
    providers: [
      provideEffects([squadsMDEffects, employeeEffects, squadEffects]),
      provideState(squadsFeature),
      provideState(squadsMDFeature),
      provideState(employeesFeature),
    ],
  },
  {
    path: 'tank-types',
    loadComponent: () =>
      import('./pages/tank-types/tank-types.page').then((p) => p.TankTypesPage),
    data: {
      pageName: 'Tank Types',
    },
    providers: [
      provideEffects([tankTypesMDEffects, unitsEffects, tankTypeEffects]),
      provideState(tankTypesMDFeature),
      provideState(unitsFeature),
      provideState(tankTypesFeature),
    ],
  },
  {
    path: 'bulk-types',
    loadComponent: () =>
      import('./pages/bulk-types/bulk-types.page').then((p) => p.BulkTypesPage),
    data: {
      pageName: 'Bulk Types',
    },
    providers: [
      provideEffects([bulkTypesMDEffects, bulkTypeEffects, unitsEffects]),
      provideState(bulkTypesMDFeature),
      provideState(unitsFeature),
      provideState(bulkTypesFeature),
    ],
  },
  {
    path: 'units',
    loadComponent: () =>
      import('./pages/units/units.page').then((p) => p.UnitsPage),
    data: {
      pageName: 'Units',
    },
    providers: [
      provideEffects([unitsMDEffects, unitsEffects]),
      provideState(unitsMDFeature),
      provideState(unitsFeature),
    ],
  },
  {
    path: 'report-types',
    loadComponent: () =>
      import('./pages/report-types/report-types.page').then(
        (p) => p.ReportTypesPage
      ),
    data: {
      pageName: 'Report Types',
    },
    providers: [
      provideEffects([reportTypesMDEffects, reportTypeEffects]),
      provideState(reportTypesFeature),
      provideState(reportTypesMDFeature),
    ],
  },
  {
    path: 'lifting-pause-reasons',
    loadComponent: () =>
      import('./pages/lifting-pause-reasons/lifting-pause-reasons.page').then(
        (p) => p.LiftingPauseReasonsPage
      ),
    data: {
      pageName: 'Lifting Pause Reasons',
    },
    providers: [
      provideEffects([
        liftingPauseReasonsEffects,
        liftingPauseReasonsMDEffects,
      ]),
      provideState(liftingPauseReasonsFeature),
      provideState(liftingPauseReasonsMDFeature),
    ],
  },
  {
    path: 'weight-categories',
    loadComponent: () =>
      import('./pages/weight-categories/weight-categories.page').then(
        (p) => p.WeightCategoriesPage
      ),
    data: {
      pageName: 'Weight Categories',
    },
    providers: [
      provideEffects([weightCategoryEffects, weightCategoriesMDEffects]),
      provideState(weightCategoriesMDFeature),
      provideState(weightCategoriesFeature),
    ],
  },
  {
    path: 'dangerous-goods',
    loadComponent: () =>
      import('./pages/dangerous-goods/dangerous-goods.page').then(
        (p) => p.DangerousGoodsPage
      ),
    data: {
      pageName: 'Dangerous Goods',
    },
    providers: [
      provideEffects([dangerousGoodsEffects, dangerousGoodsMDEffects]),
      provideState(dangerousGoodsMDFeature),
      provideState(dangerousGoodsFeature),
    ],
  },
  {
    path: 'blocking-activities',
    loadComponent: () =>
      import('./pages/blocking-activities/blocking-activities.page').then(
        (p) => p.BlockingActivitiesPage
      ),
    data: {
      pageName: 'Blocking Activities',
    },
    providers: [
      provideEffects([blockingActivitiesEffects, blockingActivitiesMDEffects]),
      provideState(blockingActivityFeature),
      provideState(blockingActivitiesMDFeature),
    ],
  },
  {
    path: 'activities',
    loadComponent: () =>
      import('libs/components/src/lib/pages/activities/activities.page').then(
        (p) => p.ActivitiesPage
      ),
    data: {
      pageName: 'Activities',
    },
    providers: [
      provideEffects([activityEffects]),
      provideState(activityConfigFeature),
    ],
  },
  {
    path: 'activity-categories',
    loadComponent: () =>
      import('libs/components/src/lib/pages/activity-categories/activity-categories.page').then(
        (p) => p.ActivityCategoriesPage
      ),
    data: {
      pageName: 'Activity Categories',
    },
    providers: [
      provideEffects([
        activityCategoriesEffects,
        activityCategoryTypeEffects,
      ]),
      provideState(activityCategoryFeature),
      provideState(activityCategoryTypeFeature),
    ],
  },
  { path: '**', redirectTo: 'locations' },
];
