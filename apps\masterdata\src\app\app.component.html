<div class="spinner-container" *ngIf="currentUserLoading(); else userLoaded">
  <p-progressSpinner [styleClass]="'spinner-style'"></p-progressSpinner>
</div>
<ng-template #userLoaded>
  <div *ngIf="isLoggedIn()">
    <lha-header [title]="title()"></lha-header>
    <div class="d-flex" style="height: calc(100vh - 57px)">
      <lha-sidebar-navigation
        *ngIf="hasMasterDataPermissions()"
        [navItems]="navItems"
        [showPin]="showPin"
      ></lha-sidebar-navigation>

      <div class="flex-1 overflow-auto">
        <main class="w-100 h-100">
          <ng-container *ngIf="hasMasterDataPermissions(); else noFlowPermissions">
            <router-outlet></router-outlet>
          </ng-container>
          <ng-template #noFlowPermissions>
            <div
              class="warning-message-container d-inline-block fs-20 text-center"
            >
              <div>
                <span class="d-block mb-10 f-bold">
                  You do not have permission to view the Master Data app
                </span>
                <a href="{{ url }}/appswitcher" class="home-url"> Home </a>
              </div>
            </div>
          </ng-template>
        </main>
      </div>
    </div>
  </div>
</ng-template>
<p-confirmDialog class="confirm-dialog"></p-confirmDialog>
<p-toast></p-toast>
