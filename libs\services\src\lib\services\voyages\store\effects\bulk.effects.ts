import { Actions, concatLatestFrom, createEffect, ofType } from '@ngrx/effects';
import { inject } from '@angular/core';
import { catchError, map, mergeMap, of } from 'rxjs';
import { HttpErrorResponse } from '@angular/common/http';
import { BulkTypeService } from '../../../maintenance/bulk-type.service';
import { TankTypeService } from '../../../maintenance/tank-type.service';
import { BulksActions } from '../actions/bulks.actions';
import { BulkType } from '../../../maintenance/interfaces/bulk-type.interface';
import { MatDialog } from '@angular/material/dialog';
import { filter, tap } from 'rxjs/operators';
import { FileService } from '../../../file.service';
import { Store } from '@ngrx/store';
import { bulksFeature } from '../features';
import { BulkService } from '../../bulk.service';
import { Bulk } from '../../interfaces/bulk.interface';
import { ConstantService } from '../../../constant.service';
import { Constant } from 'libs/services/src/lib/services/shared/constant.interface';
import { UtilityService } from '../../../utility.service';
import { ActivatedRoute } from '@angular/router';
import { BulksAddEditComponent } from '../../../../../../../../apps/allocate/src/app/components/voyages/bulks-add-edit/bulks-add-edit.component';
import { TankType } from '../../../maintenance/interfaces/tank-type.interface';
import { DialogOptions } from '../../../config/dialog-options';
import { AssetService } from '../../../maintenance/asset.service';
import { Asset } from '../../../maintenance/interfaces/asset.interface';
import { Operator } from '../../../maintenance/interfaces/operator.interface';
import { BulkQuantities } from '../../interfaces/bulk-quantities.interface';

export const loadVesselTanks = createEffect(
  (
    actions = inject(Actions),
    store = inject(Store),
    utilityService = inject(UtilityService),
    bulkService = inject(BulkService),
    activatedRoute = inject(ActivatedRoute)
  ) => {
    return actions.pipe(
      ofType(
        BulksActions.load_Bulks,
        BulksActions.load_Bulks_Lists,
        BulksActions.update_Bulk_Queries,
        BulksActions.remove_Bulk_Success,
        BulksActions.add_Bulk_Success,
        BulksActions.edit_Bulk_Success
      ),
      filter(
        () =>
          !!utilityService.getParamsFromRoute(activatedRoute.snapshot.root)[
            'id'
          ]
      ),
      concatLatestFrom(() => store.select(bulksFeature.selectQuery)),
      map(([, res]) => {
        return {
          query: res,
          voyageId: utilityService.getParamsFromRoute(
            activatedRoute.snapshot.root
          )['id'],
        };
      }),
      mergeMap((action) =>
        bulkService.loadBulks(action.query, action.voyageId).pipe(
          map((res: Bulk[]) => BulksActions.load_Bulks_Success({ bulks: res })),
          catchError((error: HttpErrorResponse) =>
            of(BulksActions.load_Bulks_Failure({ error }))
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const removeBulk = createEffect(
  (actions = inject(Actions), bulkService = inject(BulkService)) => {
    return actions.pipe(
      ofType(BulksActions.remove_Bulk),
      mergeMap((action) =>
        bulkService.removeBulk(action.id).pipe(
          map((res: Bulk) =>
            BulksActions.remove_Bulk_Success({
              bulk: res,
              successMessage: 'Bulk removed successfully!',
            })
          ),
          catchError((error: HttpErrorResponse) =>
            of(BulksActions.remove_Bulk_Failure({ error: error }))
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const addBulk = createEffect(
  (actions = inject(Actions), bulkService = inject(BulkService)) => {
    return actions.pipe(
      ofType(BulksActions.add_Bulk),
      mergeMap((action) =>
        bulkService.addBulk(action.bulk).pipe(
          map((res: Bulk) =>
            BulksActions.add_Bulk_Success({
              bulk: res,
              successMessage: 'Bulk added successfully!',
            })
          ),
          catchError((error: HttpErrorResponse) =>
            of(BulksActions.add_Bulk_Failure({ error: error }))
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const editBulk = createEffect(
  (actions = inject(Actions), bulkService = inject(BulkService)) => {
    return actions.pipe(
      ofType(BulksActions.edit_Bulk),
      mergeMap((action) =>
        bulkService.editBulk(action.bulkId, action.bulk).pipe(
          map((res: Bulk) =>
            BulksActions.edit_Bulk_Success({
              bulk: res,
              successMessage: 'Bulk edited successfully!',
            })
          ),
          catchError((error: HttpErrorResponse) =>
            of(BulksActions.edit_Bulk_Failure({ error: error }))
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const exportBulkTransactions = createEffect(
  (
    actions = inject(Actions),
    bulkService = inject(BulkService),
    fileService = inject(FileService),
    utilityService = inject(UtilityService),
    activatedRoute = inject(ActivatedRoute)
  ) => {
    return actions.pipe(
      ofType(BulksActions.export_Bulks),
      map(() =>
        utilityService.getParamsFromRoute(activatedRoute.snapshot.root)
      ),
      mergeMap((params) =>
        bulkService.exportBulkTransactions(params['id']).pipe(
          map((res: ArrayBuffer) => {
            fileService.downloadFile(res, 'Bulk Transactions');
            return BulksActions.export_Bulks_Success();
          }),
          catchError((error: HttpErrorResponse) =>
            of(BulksActions.export_Bulks_Failure({ error }))
          )
        )
      )
    );
  },
  {
    // You can omit this part if not needed
    // This indicates that the effect is a pure function and can be tree-shaken during optimization
    functional: true,
  }
);

export const loadTransactionTypeList = createEffect(
  (actions = inject(Actions), constantService = inject(ConstantService)) => {
    return actions.pipe(
      ofType(
        BulksActions.load_Transaction_Type_List,
        BulksActions.load_Bulks_Lists
      ),
      mergeMap(() =>
        constantService.loadTransactionTypes().pipe(
          map((res: Constant[]) =>
            BulksActions.load_Transaction_Type_List_Success({
              transactionTypeList: res,
            })
          ),
          catchError((error: HttpErrorResponse) =>
            of(BulksActions.load_Transaction_Type_List_Failure({ error }))
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const loadAssetList = createEffect(
  (actions = inject(Actions), assetService = inject(AssetService)) => {
    return actions.pipe(
      ofType(BulksActions.load_Asset_List, BulksActions.load_Bulks_Lists),
      mergeMap(() =>
        assetService.loadAssetList().pipe(
          map((res: Asset[]) =>
            BulksActions.load_Asset_List_Success({ assetList: res })
          ),
          catchError((error: HttpErrorResponse) =>
            of(BulksActions.load_Asset_List_Failure({ error }))
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const loadBulkTypeList = createEffect(
  (actions = inject(Actions), bulkTypeService = inject(BulkTypeService)) => {
    return actions.pipe(
      ofType(BulksActions.load_Bulk_Type_List, BulksActions.load_Bulks_Lists),
      mergeMap(() =>
        bulkTypeService.loadBulkTypesList().pipe(
          map((res: BulkType[]) =>
            BulksActions.load_Bulk_Type_List_Success({ bulkTypeList: res })
          ),
          catchError((error: HttpErrorResponse) =>
            of(BulksActions.load_Bulk_Type_List_Failure({ error }))
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const loadTankTypeList = createEffect(
  (actions = inject(Actions), tankTypeService = inject(TankTypeService)) => {
    return actions.pipe(
      ofType(BulksActions.load_Tank_Type_List, BulksActions.load_Bulks_Lists),
      mergeMap(() =>
        tankTypeService.loadTankTypesList().pipe(
          map((res: TankType[]) =>
            BulksActions.load_Tank_Type_List_Success({ tankTypeList: res })
          ),
          catchError((error: HttpErrorResponse) =>
            of(BulksActions.load_Tank_Type_List_Failure({ error }))
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const loadBulkQuantities = createEffect(
  (
    actions = inject(Actions),
    utilityService = inject(UtilityService),
    bulkService = inject(BulkService),
    activatedRoute = inject(ActivatedRoute)
  ) => {
    return actions.pipe(
      ofType(
        BulksActions.load_Bulks_Lists,
        BulksActions.remove_Bulk_Success,
        BulksActions.add_Bulk_Success,
        BulksActions.edit_Bulk_Success
      ),
      map(
        () =>
          utilityService.getParamsFromRoute(activatedRoute.snapshot.root)['id']
      ),
      mergeMap((id) =>
        bulkService.loadBulkQuantities(id).pipe(
          map((res: BulkQuantities[]) =>
            BulksActions.load_Bulk_Quantities_Success({ bulkQuantities: res })
          ),
          catchError((error: HttpErrorResponse) =>
            of(BulksActions.load_Bulk_Quantities_Failure({ error }))
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const loadBulkOperatorList = createEffect(
  (actions = inject(Actions), bulkService = inject(BulkService)) => {
    return actions.pipe(
      ofType(BulksActions.load_Bulks_Lists),
      mergeMap(() =>
        bulkService.loadOperators().pipe(
          map((res: Operator[]) =>
            BulksActions.load_Operators_Success({ operators: res })
          ),
          catchError((error: HttpErrorResponse) =>
            of(BulksActions.load_Operators_Failure({ error }))
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const openBulkDialog = createEffect(
  (
    actions = inject(Actions),
    dialog = inject(MatDialog),
    dialogOptions = inject(DialogOptions)
  ) => {
    return actions.pipe(
      ofType(BulksActions.open_Bulk_Dialog),
      tap((action) => {
        dialog.open(BulksAddEditComponent, {
          ...dialogOptions,
          data: {
            bulk: action.bulk,
          },
        });
      })
    );
  },
  {
    functional: true,
    dispatch: false,
  }
);
