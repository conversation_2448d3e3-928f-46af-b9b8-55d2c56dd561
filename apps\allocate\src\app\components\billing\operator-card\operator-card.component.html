<a
  [routerLink]="['..', 'operator', billingOperator.clientId]"
  class="d-flex flex-direction-column gap-20 gray-border-1px border-radius-10 p-20"
>
  <span class="fs-18 fw-600 red-700">
    {{ billingOperator.clientName }}
  </span>
  <span class="fs-32 fw-600">
    {{ currency }}{{ billingOperator.totalCost | number : '1.2-2' }}
  </span>
  <span
    class="d-flex fs-12 align-items-center gap-8"
    [ngStyle]="{ color: billingOperator.voyageNotValid ? 'red' : 'green' }"
  >
    <em
      class="pi"
      [ngClass]="
        !billingOperator.voyageNotValid ? 'pi-info-circle' : 'pi-check-circle'
      "
    ></em>
    <span>
      {{
        billingOperator.voyageNotValid
          ? 'Issues Detected'
          : 'No Issues Detected'
      }}
    </span>
  </span>
</a>
