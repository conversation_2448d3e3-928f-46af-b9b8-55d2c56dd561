import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { Store } from '@ngrx/store';
import { billingsFeature } from '../../../store/features/billing.features';
import { HeaderNavComponent } from 'libs/components/src/lib/components/header-nav/header-nav.component';

@Component({
  selector: 'lha-billing-header',
  standalone: true,
  imports: [HeaderNavComponent],
  templateUrl: './billing-header.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BillingHeaderComponent {
  private readonly store = inject(Store);
  billing = this.store.selectSignal(billingsFeature.selectBilling);
  categoriesItems = [
    {
      title: 'Overview',
      link: 'overview',
      permissions: [],
    },
    {
      title: 'Operator',
      link: 'operator',
      permissions: [],
    },
    {
      title: 'Vessel',
      link: 'vessel',
      permissions: [],
    },
  ];
}
