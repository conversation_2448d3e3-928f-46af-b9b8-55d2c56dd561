import { Date<PERSON><PERSON><PERSON>, NgIf } from '@angular/common';
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  effect,
  inject,
  OnInit,
  ViewChild,
} from '@angular/core';
import { FormsModule } from '@angular/forms';

import { Store } from '@ngrx/store';

import { DialogModule } from 'primeng/dialog';
import { Table, TableModule } from 'primeng/table';
import { DropdownModule } from 'primeng/dropdown';
import { CalendarModule } from 'primeng/calendar';
import { InputTextModule } from 'primeng/inputtext';

import { assetsAllocateFeature } from '../../../store/features/assets.feature';
import { AssetsAllocateActions } from '../../../store/actions/assets.actions';

import { assetMobileWellFeature } from 'libs/services/src/lib/services/maintenance/store/features';
import { AssetMobileWellActions } from 'libs/services/src/lib/services/maintenance/store/actions/assets-mobile-well.actions';
import { AssetMobileWell } from 'libs/services/src/lib/services/maintenance/interfaces/asset-mobile-well.interface';
import { ConfirmService } from 'libs/components/src/lib/services/confirm.service';
import { Asset } from 'libs/services/src/lib/services/maintenance/interfaces/asset.interface';
import { CustomChipComponent } from 'libs/components/src/lib/components/custom-chip/custom-chip.component';
import { stripTimezoneOffset } from 'libs/services/src/lib/services/functions/convert-date.utils';

@Component({
  standalone: true,
  selector: 'allocate-assets-mobile-well',
  templateUrl: './asset-mobile-well.component.html',
  imports: [
    DatePipe,
    DialogModule,
    TableModule,
    NgIf,
    DropdownModule,
    CalendarModule,
    FormsModule,
    InputTextModule,
    CustomChipComponent,
  ],
  providers: [DatePipe],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AssetsMobileWellComponent implements OnInit {
  @ViewChild('dt') table!: Table;
  private readonly store = inject(Store);
  private readonly confirmService = inject(ConfirmService);
  private readonly cdr = inject(ChangeDetectorRef);
  datePipe = inject(DatePipe);

  isVisible = this.store.selectSignal(
    assetsAllocateFeature.selectIsVisibleMobileWell
  );
  asset = this.store.selectSignal(assetsAllocateFeature.selectAsset);

  wellAssetList = this.store.selectSignal(
    assetMobileWellFeature.selectAssetByType(['WEL'])
  );

  assetMobileWellsSignal = this.store.selectSignal(
    assetMobileWellFeature.selectAssetMobileWells
  );

  assetMobileWells: AssetMobileWell[] = [];
  tableWidth = 752;

  searchTerm = '';
  clonedAssetMobileWell: { [s: string]: AssetMobileWell } = {};

  constructor() {
    effect(() => {
      if (this.assetMobileWellsSignal()) {
        this.assetMobileWells = [...this.assetMobileWellsSignal()];
      }
    });
  }

  ngOnInit() {
    if (this.asset()) {
      this.store.dispatch(
        AssetMobileWellActions.set_Asset_Mobile_Well_Id({
          id: this.asset()!.assetId,
        })
      );
    }
  }

  hideDialog() {
    this.store.dispatch(
      AssetsAllocateActions.change_visibility_mobile_well({
        visible: false,
        asset: null,
      })
    );
  }

  onRowEditCancel(assetMobileWell: AssetMobileWell, index: number) {
    if (assetMobileWell.mobileWellId === 'add') {
      this.assetMobileWells.shift();
    } else {
      this.assetMobileWells[index] =
        this.clonedAssetMobileWell[assetMobileWell.mobileWellId as string];
    }
    delete this.clonedAssetMobileWell[assetMobileWell.mobileWellId as string];
  }

  onRowEditSave(assetMobileWell: AssetMobileWell) {
    if (assetMobileWell.startDateTime && assetMobileWell.wellId) {
      const model = {
        wellId: assetMobileWell.wellId,
        mobileId: assetMobileWell.mobileId,
        startDateTime: assetMobileWell.startDateTime
          ? stripTimezoneOffset(assetMobileWell.startDateTime)
          : null,
        endDateTime: assetMobileWell.endDateTime
          ? stripTimezoneOffset(assetMobileWell.endDateTime)
          : null,
      } as AssetMobileWell;

      if (assetMobileWell.mobileWellId === 'add') {
        const startDateTime = assetMobileWell.startDateTime
          ? this.datePipe.transform(
              assetMobileWell.startDateTime,
              'dd/MM/yyyy HH:mm'
            )
          : '';

        const endDateTime = assetMobileWell.endDateTime
          ? this.datePipe.transform(
              assetMobileWell.endDateTime,
              'dd/MM/yyyy HH:mm'
            )
          : '';
        if (this.assetMobileWells.length === 1) {
          let message = '';
          if (assetMobileWell.endDateTime) {
            message = `
            ${this.asset()?.name} will be at ${this.getAssetNameById(
              assetMobileWell.wellId,
              this.wellAssetList()
            )}
            <br>
            from ${startDateTime} to  ${endDateTime}
            <br>
            You will not be able to edit the well history for this asset during or before this period.
            <br>
            Do you want to continue?
          `;
          } else {
            message = `
            ${this.asset()?.name} will be at ${this.getAssetNameById(
              assetMobileWell.wellId,
              this.wellAssetList()
            )}
            <br>
            from ${startDateTime} to  ${endDateTime}
            <br>
            You cannot add any history that precedes this date.
            <br>
            Do you want to continue?
          `;
          }

          this.confirmService
            .confirm({
              header: 'Confirm',
              message,
              acceptLabel: 'Confirm',
              rejectLabel: 'No',
            })
            .then((value) => {
              if (value) {
                this.store.dispatch(
                  AssetMobileWellActions.add_Asset_Mobile_Well({
                    assetMobileWell: model,
                  })
                );
              } else {
                this.assetMobileWells.shift();
              }
            });
          return;
        }

        const lastMobileWell =
          this.assetMobileWells[this.assetMobileWells.length - 1];

        if (!lastMobileWell.endDateTime) {
          const message = `
            The end date of ${this.asset()?.name} being at${
            lastMobileWell.wellName
          }
            <br>
            from ${startDateTime} does not currently have an end date.
            <br>
            YThe system will automatically set the end date to ${startDateTime}.
            <br>
            Do you want to continue?
          `;
          this.confirmService
            .confirm({
              header: 'Confirmation',
              message,
              acceptLabel: 'Confirm',
              rejectLabel: 'No',
            })
            .then((value) => {
              if (value) {
                this.store.dispatch(
                  AssetMobileWellActions.add_Asset_Mobile_Well({
                    assetMobileWell: model,
                  })
                );
              } else {
                this.assetMobileWells.shift();
              }
            });
          return;
        }

        this.store.dispatch(
          AssetMobileWellActions.add_Asset_Mobile_Well({
            assetMobileWell: model,
          })
        );
      } else {
        this.store.dispatch(
          AssetMobileWellActions.edit_Asset_Mobile_Well({
            id: assetMobileWell.mobileWellId,
            assetMobileWell: model,
          })
        );
      }
      delete this.clonedAssetMobileWell[assetMobileWell.mobileWellId as string];
    }
  }

  remove(assetMobileWell: AssetMobileWell) {
    this.confirmService
      .confirm({
        header: 'Delete',
        message: 'Do you want to remove this Mobile Well Asset?',
        acceptLabel: 'Delete',
        rejectLabel: 'No',
      })
      .then((value) => {
        if (value) {
          this.store.dispatch(
            AssetMobileWellActions.remove_Asset_Mobile_Well({
              id: assetMobileWell.mobileWellId,
            })
          );
        }
      });
  }

  onRowEditInit(assetMobileWell: AssetMobileWell, rowIndex: number) {
    this.clonedAssetMobileWell[assetMobileWell.mobileWellId as string] = {
      ...assetMobileWell,
    };

    this.assetMobileWells[rowIndex] = {
      ...assetMobileWell,
      startDateTime: assetMobileWell.startDateTime
        ? new Date(assetMobileWell.startDateTime)
        : null,
      endDateTime: assetMobileWell.endDateTime
        ? new Date(assetMobileWell.endDateTime)
        : null,
    };
  }

  addNewRow() {
    if (
      this.table &&
      this.table.editingRowKeys &&
      Object.keys(this.table.editingRowKeys).length > 0
    ) {
      return;
    }

    const newRow = {
      mobileWellId: 'add',
      mobileId: this.asset()?.assetId,
      startDateTime: null,
      endDateTime: null,
      wellId: null,
    } as AssetMobileWell;

    this.clonedAssetMobileWell['add'] = newRow;

    this.assetMobileWells.unshift(newRow);
    setTimeout(() => {
      if (this.table) {
        this.table.initRowEdit(newRow);
        this.cdr.markForCheck();
      }
    }, 0);
  }

  private getAssetNameById(id: string, locArr: Asset[]): string {
    return locArr.find((item) => item.assetId === id)?.name ?? '';
  }
}
