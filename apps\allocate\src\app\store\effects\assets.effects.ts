import { inject } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { AssetsAllocateActions } from '../actions/assets.actions';
import { of, switchMap } from 'rxjs';
import { AssetActions } from 'libs/services/src/lib/services/maintenance/store/actions/assets.actions';
import { OperatorActions } from 'libs/services/src/lib/services/maintenance/store/actions/operators.actions';

export const assetsMDInit = createEffect(
  (actions = inject(Actions)) => {
    return actions.pipe(
      ofType(AssetsAllocateActions.init_assets_allocate),
      switchMap(() =>
        of(
          AssetActions.load_Assets(),
          AssetActions.load_Asset_Types(),
          OperatorActions.load_Operators()
        )
      )
    );
  },
  { functional: true }
);
