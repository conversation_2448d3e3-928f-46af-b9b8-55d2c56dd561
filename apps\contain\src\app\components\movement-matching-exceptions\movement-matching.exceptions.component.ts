import { <PERSON><PERSON><PERSON> } from '@angular/common';
import {
  Component,
  DestroyRef,
  inject,
  Input,
  OnInit,
  signal,
  ViewEncapsulation,
} from '@angular/core';
import { CardModule } from 'primeng/card';
import { DividerModule } from 'primeng/divider';
import { CheckboxModule } from 'primeng/checkbox';
import { ButtonModule } from 'primeng/button';
import { TabViewModule } from 'primeng/tabview';
import { TableModule } from 'primeng/table';
import { Store } from '@ngrx/store';
import { DialogModule } from 'primeng/dialog';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { ReactiveFormsModule } from '@angular/forms';
import { DropdownModule } from 'primeng/dropdown';
import { ColumnModel } from 'libs/components/src/lib/models/column.model';
import { currentUserFeature } from 'libs/auth/src/lib/store/current-user/current-user.features';
import { MovementMatchingExceptionsTableFields } from '../../shared/enums/movement-matching-exceptions-table-fields.enum';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { DatePipe } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { HireRequestFilterComponent } from '../hire-request-filter/hire-request-filters.component';
import { movementMatchingFeature } from 'libs/services/src/lib/services/contain/store/features/movement-matching.feature';
import { MovementMatching } from 'libs/services/src/lib/services/contain/interfaces/movement-matching/movement-matching.interface';
import { MovementMatchingActions } from 'libs/services/src/lib/services/contain/store/actions/movement-matching.actions';
import { VoyageDirection } from 'libs/services/src/lib/services/voyages/enums/voyage-direction.enum';
import { MovementMatchingDetailsDialogComponent } from '../movement-matching-details-dialog/movement-matching-details.dialog';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { ConfirmationService } from 'primeng/api';

@Component({
  standalone: true,
  selector: 'contain-movement-matching-exceptions-list',
  encapsulation: ViewEncapsulation.None,
  imports: [
    NgFor,
    CardModule,
    DividerModule,
    CheckboxModule,
    ButtonModule,
    TabViewModule,
    TableModule,
    DialogModule,
    InputTextareaModule,
    DropdownModule,
    ReactiveFormsModule,
    DatePipe,
    FormsModule,
    CommonModule,
    HireRequestFilterComponent,
    MovementMatchingDetailsDialogComponent,
    ConfirmDialogModule,
],
  templateUrl: 'movement-matching.exceptions.component.html',
})
export class MovementMatchingExceptionsListComponent implements OnInit {
  @Input() isRunMovementMatchingPage = false;
  store = inject(Store);
  destroyRef = inject(DestroyRef);
  currentUser = this.store.selectSignal(currentUserFeature.selectUser);
  filterModel = this.store.selectSignal(movementMatchingFeature.selectFilter);
  tableWidth = 3235;
  listColumns = signal<ColumnModel[]>([]);
  detailsDialogVisible = false;
  selectedMovementMatching: MovementMatching | null = null;
  movementMatchingToApprove: MovementMatching | null = null;
  voyageDirection = VoyageDirection;
  private readonly confirmationService = inject(ConfirmationService);
  movementMatchingMatches = this.store.selectSignal(
    movementMatchingFeature.selectMatchingMatches
  );

  rows: MovementMatching[] = [];

  ngOnInit() {
    if (!this.isRunMovementMatchingPage) {
      this.getMovementMatchingExceptionsList();
    }

    this.listColumns.set([
      new ColumnModel(
        MovementMatchingExceptionsTableFields.movementUnit,
        'Movement Unit',
        60
      ),
      new ColumnModel(
        MovementMatchingExceptionsTableFields.direction,
        'Direction',
        60
      ),
      new ColumnModel(
        MovementMatchingExceptionsTableFields.completedDate,
        'Completed Date',
        140
      ),
      new ColumnModel(
        MovementMatchingExceptionsTableFields.sailingDate,
        'Sailing Date',
        140
      ),
      new ColumnModel(
        MovementMatchingExceptionsTableFields.manifestNo,
        'Manifest No',
        60
      ),
      new ColumnModel(
        MovementMatchingExceptionsTableFields.movementVendor,
        'Movement Vendor',
        60
      ),
      new ColumnModel(
        MovementMatchingExceptionsTableFields.movementClient,
        'Movement Client',
        60
      ),
      new ColumnModel(
        MovementMatchingExceptionsTableFields.matchedAt,
        'Matched At',
        140
      ),
      new ColumnModel(
        MovementMatchingExceptionsTableFields.exceptionReason,
        'Exception Reason',
        180
      ),
      new ColumnModel(MovementMatchingExceptionsTableFields.action, '', 80),
    ]);

    this.store
      .select(movementMatchingFeature.selectMatchingExceptions)
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((value) => {
        this.rows = value ?? [];
      });
  }

  getMovementMatchingExceptionsList() {
    this.store.dispatch(
      MovementMatchingActions.load_Movement_Matching_Exceptions_List({
        filterModel: this.filterModel(),
      })
    );
  }

  setDialogVisible(): void {
    this.detailsDialogVisible = !this.detailsDialogVisible;
  }

  openDetailsDialog(
    movementMatching: MovementMatching | null,
    $event: Event
  ): void {
    $event.stopPropagation();
    this.selectedMovementMatching = movementMatching;
    this.detailsDialogVisible = true;
  }

  removeMovementMatching(movementMatchingId: string, $event: Event) {
    $event.stopPropagation();

    const exceptionIds = this.rows.map(row => row.movementMatchingId);
    const matchIds = this.movementMatchingMatches()?.map(match => match.movementMatchingId) || [];
    const allMovementMatchingIds = [...exceptionIds, ...matchIds];

    const enhancedFilter = {
      ...this.filterModel(),
      movementMatchingIds: allMovementMatchingIds,
    };

    this.store.dispatch(
      MovementMatchingActions.delete_Movement_Matching({
        movementMatchingId,
        filterModel: enhancedFilter,
      })
    );
  }

  hireApproveMovementMatching(
    movementMatching: MovementMatching,
    $event: Event
  ) {
    $event.stopPropagation();
    this.confirmationService.confirm({
      header: 'Confirm Approve Movement Matching',
      message: movementMatching.direction === VoyageDirection.Outbound ? 
      `Are you sure you want to approve the movement matching exception for ${movementMatching.movementUnit}? ` + 
      `This action will ${movementMatching.movementAssetId !== movementMatching.hireBillingAssetId ? `update the Billing Asset of the CCU Hire from ${movementMatching.hireBillingAssetName} to 
      ${movementMatching.movementAssetName}` : `not update the Billing Asset of the CCU Hire as it is the same as the Movement Asset`}.` : 'Are you sure you want to approve the movement matching exception?',
      acceptLabel: 'Confirm',
      rejectLabel: 'Cancel',
      acceptButtonStyleClass: 'btn-primary',
      rejectButtonStyleClass: 'btn-tertiary',
      acceptIcon: 'none',
      rejectIcon: 'none',
      accept: () => {
        const exceptionIds = this.rows.map(row => row.movementMatchingId);
        const matchIds = this.movementMatchingMatches()?.map(match => match.movementMatchingId) || [];
        const allMovementMatchingIds = [...exceptionIds, ...matchIds];

        const enhancedFilter = {
          ...this.filterModel(),
          movementMatchingIds: allMovementMatchingIds,
        };

        this.store.dispatch(
          MovementMatchingActions.approve_Movement_Matching({
            movementMatching,
            filterModel: enhancedFilter,
          })
        );
      },
    });
  }

  navigateToFlowVoyage(voyageId: string) {
    window.open(`/flow/voyage/${voyageId}/voyage-detail/cargo`);
  }
}
