<header *ngIf="(vm$ | async) as vm">
  <div class="voyage__block">
    <div class="voyage__box">
      <div class="voyage__title">
        Voyage:
      </div>
      <div class="voyage__text">
        {{vm.activeVoyage?.voyageNumber}}
      </div>
    </div>
    <div class="voyage__box">
      <div class="voyage__title">
        Vessel:
      </div>
      <div class="voyage__text">
        {{vm.activeVoyage?.vesselName}}
      </div>
    </div>
    <div class="voyage__box">
      <div class="voyage__title">
        Voyage type:
      </div>
      <div class="voyage__text">
        {{(vm.activeVoyage?.voyageType ?? '') | propertyName : voyageTypes : 'value' : 'name'}}
      </div>
    </div>
    <div class="voyage__box">
      <div class="voyage__title">
        Initial Port:
      </div>
      <div class="voyage__text">
        {{vm.activeVoyage?.initialAssetName}}
      </div>
    </div>
    <div class="voyage__box">
      <div class="voyage__title">
        Total Mileage:
      </div>
      <div class="voyage__text">
        {{vm.activeVoyage?.totalMileage}}
      </div>
    </div>
  </div>
</header>
