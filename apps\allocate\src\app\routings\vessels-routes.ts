import { Route } from '@angular/router';
import { VesselCreateEditComponent } from '../pages/vessels/vessel-create-edit/vessel-create-edit.component';
import { TanksComponent } from '../components/vessels/tanks/tanks.component';
import { HireStatementsComponent } from '../components/vessels/hire-statements/hire-statements.component';

export const VesselsRoute: Route[] = [
  {
    path: '',
    loadComponent: () =>
      import('../pages/vessels/vessels-list/vessels-list.component').then(
        (c) => c.VesselsListComponent
      ),
  },
  {
    path: 'create',
    component: VesselCreateEditComponent,
  },
  {
    path: ':id',
    loadComponent: () =>
      import('../pages/vessels/vessel-details/vessel-details.component').then(
        (c) => c.VesselDetailsComponent
      ),
    children: [
      { path: '', redirectTo: 'details', pathMatch: 'full' },
      { path: 'details', component: VesselCreateEditComponent },
      { path: 'tanks', component: TanksComponent },
      { path: 'hire-statement', component: HireStatementsComponent },
      {
        path: 'hire-statement/:itemId',
        component: HireStatementsComponent,
      },
    ],
  },
];
