import { <PERSON><PERSON><PERSON>, NgIf } from '@angular/common';
import {
  Component,
  DestroyRef,
  inject,
  Input,
  OnInit,
  signal,
  ViewEncapsulation,
} from '@angular/core';
import { CardModule } from 'primeng/card';
import { DividerModule } from 'primeng/divider';
import { CheckboxModule } from 'primeng/checkbox';
import { ButtonModule } from 'primeng/button';
import { TabViewModule } from 'primeng/tabview';
import { TableModule } from 'primeng/table';
import { Store } from '@ngrx/store';
import { DialogModule } from 'primeng/dialog';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { ReactiveFormsModule } from '@angular/forms';
import { DropdownModule } from 'primeng/dropdown';
import { ColumnModel } from 'libs/components/src/lib/models/column.model';
import { currentUserFeature } from 'libs/auth/src/lib/store/current-user/current-user.features';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { DatePipe } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { HireRequestFilterComponent } from '../hire-request-filter/hire-request-filters.component';
import { movementMatchingFeature } from 'libs/services/src/lib/services/contain/store/features/movement-matching.feature';
import { MovementMatching } from 'libs/services/src/lib/services/contain/interfaces/movement-matching/movement-matching.interface';
import { MovementMatchingActions } from 'libs/services/src/lib/services/contain/store/actions/movement-matching.actions';
import { MovementMatchingMatchesTableFields } from '../../shared/enums/movement-matching-matches-table-fields.enum';
import { MovementMatchingDetailsDialogComponent } from '../movement-matching-details-dialog/movement-matching-details.dialog';
import { VoyageDirection } from 'libs/services/src/lib/services/voyages/enums/voyage-direction.enum';

@Component({
  standalone: true,
  selector: 'contain-movement-matching-matches-list',
  encapsulation: ViewEncapsulation.None,
  imports: [
    NgIf,
    NgFor,
    CardModule,
    DividerModule,
    CheckboxModule,
    ButtonModule,
    TabViewModule,
    TableModule,
    DialogModule,
    InputTextareaModule,
    DropdownModule,
    ReactiveFormsModule,
    DatePipe,
    FormsModule,
    CommonModule,
    HireRequestFilterComponent,
    MovementMatchingDetailsDialogComponent,
  ],
  templateUrl: 'movement-matching.matches.component.html',
})
export class MovementMatchingMatchesListComponent implements OnInit {
  @Input() isRunMovementMatchingPage = false;
  store = inject(Store);
  destroyRef = inject(DestroyRef);
  currentUser = this.store.selectSignal(currentUserFeature.selectUser);
  filterModel = this.store.selectSignal(movementMatchingFeature.selectFilter);
  tableWidth = 3235;
  listColumns = signal<ColumnModel[]>([]);
  detailsDialogVisible = false;
  selectedMovementMatching: MovementMatching | null = null;
  voyageDirection = VoyageDirection;

  rows: MovementMatching[] = [];

  ngOnInit() {
    if (!this.isRunMovementMatchingPage) {
      this.getMovementMatchingMatchesList();
    }

    this.listColumns.set([
      new ColumnModel(
        MovementMatchingMatchesTableFields.hireUnit,
        'Hire Unit',
        80
      ),
      new ColumnModel(
        MovementMatchingMatchesTableFields.movementUnit,
        'Movement Unit',
        60
      ),
      new ColumnModel(
        MovementMatchingMatchesTableFields.hireClient,
        'Hire Client',
        80
      ),
      new ColumnModel(
        MovementMatchingMatchesTableFields.movementClient,
        'Movement Client',
        60
      ),
      new ColumnModel(
        MovementMatchingMatchesTableFields.hireBillingAsset,
        'Hire Billing Asset',
        60
      ),
      new ColumnModel(
        MovementMatchingMatchesTableFields.movementAsset,
        'Movement Asset',
        60
      ),
      new ColumnModel(
        MovementMatchingMatchesTableFields.direction,
        'Direction',
        60
      ),
      new ColumnModel(
        MovementMatchingMatchesTableFields.completedDate,
        'Completed Date',
        140
      ),
      new ColumnModel(
        MovementMatchingMatchesTableFields.sailingDate,
        'Sailing Date',
        140
      ),
      new ColumnModel(
        MovementMatchingMatchesTableFields.manifestNo,
        'Manifest No',
        60
      ),
      new ColumnModel(
        MovementMatchingMatchesTableFields.movementVendor,
        'Vendor',
        60
      ),

      new ColumnModel(
        MovementMatchingMatchesTableFields.matchedAt,
        'Matched At',
        140
      ),
      new ColumnModel(MovementMatchingMatchesTableFields.action, '', 40),
    ]);

    this.store
      .select(movementMatchingFeature.selectMatchingMatches)
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((value) => {
        this.rows = value ?? [];
      });
  }

  getMovementMatchingMatchesList() {
    this.store.dispatch(
      MovementMatchingActions.load_Movement_Matching_Matches_List({
        filterModel: this.filterModel(),
      })
    );
  }

  setDialogVisible(): void {
    this.detailsDialogVisible = !this.detailsDialogVisible;
  }

  openDetailsDialog(movementMatching: MovementMatching | null, $event: Event): void {
    $event.stopPropagation();
    this.selectedMovementMatching = movementMatching;
    this.detailsDialogVisible = true;
  }

  navigateToFlowVoyage(voyageId: string) {
    window.open(`/flow/voyage/${voyageId}/voyage-detail/cargo`);
  }
}
