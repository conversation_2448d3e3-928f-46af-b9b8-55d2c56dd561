import { createActionGroup, emptyProps, props } from '@ngrx/store';
import { Distance } from '../../interfaces/distance.interface';
import { errorProps } from 'libs/components/src/lib/functions/utility.functions';

export const DistanceActions = createActionGroup({
  source: 'Distances',
  events: {
    init_distances: emptyProps(),
    change_visibility_add_edit: props<{
      visible: boolean;
      distance: Distance | null;
    }>(),

    load_All_Distances: emptyProps(),
    load_All_Distances_Success: props<{ distances: Distance[] }>(),
    load_All_Distances_Failure: errorProps(),

    remove_Distance: props<{ id: string }>(),
    remove_Distance_Success: props<{
      distance: Distance;
      successMessage: string;
    }>(),
    remove_Distance_Failure: errorProps(),

    add_Distance: props<{ distance: Distance }>(),
    add_Distance_Success: props<{
      distance: Distance;
      successMessage: string;
    }>(),
    add_Distance_Failure: errorProps(),

    edit_Distance: props<{ distanceId: string; distance: Distance }>(),
    edit_Distance_Success: props<{
      distance: Distance;
      successMessage: string;
    }>(),
    edit_Distance_Failure: errorProps(),

    export_Distances: emptyProps(),
    export_Distances_Success: props<{ successMessage: string }>(),
    export_Distances_Failure: errorProps(),
  },
});
