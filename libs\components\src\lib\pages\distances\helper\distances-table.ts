import { ColumnModel } from 'libs/components/src/lib/models/column.model';
import { DistancesTableFields } from './distances-table-fields.enum';

export function InitializeDistancesTable(): ColumnModel[] {
  const assetsListColumns = [
    new ColumnModel(DistancesTableFields.baseAssetName, 'First Asset', 150, {
      sortable: true,
    }),
    new ColumnModel(DistancesTableFields.toAssetName, 'Second Asset', 150, {
      sortable: true,
    }),
    new ColumnModel(
      DistancesTableFields.distanceInMiles,
      'Distance (miles)',
      150,
      {
        sortable: true,
      }
    ),
    new ColumnModel(DistancesTableFields.createdByName, 'Created By', 150, {
      sortable: true,
    }),
    new ColumnModel(DistancesTableFields.updatedByName, 'Updated By', 150, {
      sortable: true,
    }),
    new ColumnModel(DistancesTableFields.actions, '', 100),
  ];
  return assetsListColumns;
}
