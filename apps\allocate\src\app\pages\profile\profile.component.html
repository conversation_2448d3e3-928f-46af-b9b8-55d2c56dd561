<div class="forms-container">
  <div class="form-container">
    <h2 class="user-details-title">User Details</h2>
    <form [formGroup]="form" (ngSubmit)="saveUser()">
      <div class="form__block">
        <div class="form__box">
          <mat-form-field appearance="outline" hideRequiredMarker="true">
            <mat-label>First Name</mat-label>
            <input matInput formControlName="firstname" />
            <mat-error *ngIf="form.controls.firstname.hasError('required')">
              First name is required.
            </mat-error>
          </mat-form-field>
        </div>
        <div class="form__box">
          <mat-form-field appearance="outline" hideRequiredMarker="true">
            <mat-label>Last Name</mat-label>
            <input matInput formControlName="lastname" />
            <mat-error *ngIf="form.controls.lastname.hasError('required')">
              Last name is required.
            </mat-error>
          </mat-form-field>
        </div>
        <div class="form__box">
          <mat-form-field appearance="outline" hideRequiredMarker="true">
            <mat-label>Email Address</mat-label>
            <input matInput name="email" formControlName="emailAddress" />
            <mat-error *ngIf="form.controls.emailAddress.hasError('required')">
              Email address is required.
            </mat-error>
            <mat-error *ngIf="form.controls.emailAddress.hasError('pattern')">
              Enter a valid email address
            </mat-error>
          </mat-form-field>
        </div>
      </div>
      <button
        mat-raised-button
        color="primary"
        [disabled]="loading$ | async"
        type="submit"
        class="login-button"
      >
        <span [hidden]="loading$ | async">Edit User Details</span>
        <mat-icon class="mat-icon--spinner" *ngIf="loading$ | async"
          ><mat-spinner diameter="25"></mat-spinner
        ></mat-icon>
      </button>
    </form>
  </div>
</div>
