import { DatePipe, NgIf } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  DestroyRef,
  inject,
  OnInit,
} from '@angular/core';
import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

import { Actions, ofType } from '@ngrx/effects';
import { Store } from '@ngrx/store';

import { DialogModule } from 'primeng/dialog';
import { DropdownModule } from 'primeng/dropdown';
import { InputSwitchModule } from 'primeng/inputswitch';
import { InputTextModule } from 'primeng/inputtext';
import { TooltipModule } from 'primeng/tooltip';

import { ActivityCategory } from 'libs/services/src/lib/services/maintenance/interfaces/activity-category.interface';
import { ActivityCategoryActions } from 'libs/services/src/lib/services/maintenance/store/actions/activity-category.actions';
import { ActivityType } from 'libs/services/src/lib/services/maintenance/enums/activity-type.enum';
import { activityCategoryFeature } from 'libs/services/src/lib/services/maintenance/store/features';

@Component({
  selector: 'md-activity-category-add-edit',
  standalone: true,
  templateUrl: './activity-category-add-edit.component.html',
  imports: [
    ReactiveFormsModule,
    NgIf,
    DialogModule,
    DropdownModule,
    InputTextModule,
    InputSwitchModule,
    DatePipe,
    TooltipModule,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ActivityCategoryAddEditComponent implements OnInit {
  private readonly destroyRef = inject(DestroyRef);
  private readonly actions = inject(Actions);
  private readonly store = inject(Store);

  isVisible = this.store.selectSignal(
    activityCategoryFeature.selectIsVisibleAddEdit
  );
  activityCategory = this.store.selectSignal(
    activityCategoryFeature.selectActivityCategory
  );

  form = new FormGroup({
    name: new FormControl<string>('', [Validators.required]),
    activityType: new FormControl<number | null>(null, [Validators.required]),
    isHidden: new FormControl<boolean>(false),
  });

  controls = {
    name: this.form.get('name'),
    activityType: this.form.get('activityType'),
  };

  activityOptions = [
    {
      value: ActivityType.DryBulk,
      name: 'Dry Bulk',
    },
    {
      value: ActivityType.WetBulk,
      name: 'Wet Bulk',
    },
    {
      value: ActivityType.Personnel,
      name: 'Personnel',
    },
    {
      value: ActivityType.Lifting,
      name: 'Lifting',
    },
  ];

  ngOnInit() {
    this.actions
      .pipe(ofType(ActivityCategoryActions.change_visibility_add_edit))
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(({ activityCategory }) => {
        this.form.reset({ isHidden: false });
        this.form.markAsUntouched();
        if (activityCategory != null) {
          this.form.patchValue({
            ...activityCategory,
          });
        }
      });
  }

  hideDialog() {
    this.store.dispatch(
      ActivityCategoryActions.change_visibility_add_edit({
        visible: false,
        activityCategory: null,
      })
    );
  }

  onSubmit() {
    this.form.markAllAsTouched();
    if (this.form.valid) {
      const activityCategory = {
        ...this.form.getRawValue(),
      } as ActivityCategory;

      if (this.activityCategory()) {
        this.store.dispatch(
          ActivityCategoryActions.edit_Activity_Category({
            activityCategoryId: this.activityCategory()!.activityCategoryId,
            activityCategory,
          })
        );
      } else {
        this.store.dispatch(
          ActivityCategoryActions.add_Activity_Category({ activityCategory })
        );
      }
    }
  }
}
