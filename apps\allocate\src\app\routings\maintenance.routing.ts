import { Route } from '@angular/router';

import { provideEffects } from '@ngrx/effects';
import { provideState } from '@ngrx/store';

import * as operatorsEffects from 'libs/services/src/lib/services/maintenance/store/effects/operators.effects';
import * as reportTypeEffects from 'libs/services/src/lib/services/maintenance/store/effects/report-type.effects';
import * as assetsEffects from 'libs/services/src/lib/services/maintenance/store/effects/assets.effects';
import * as distanceEffects from 'libs/services/src/lib/services/maintenance/store/effects/distance.effects';
import * as assetsMobileWellEffects from 'libs/services/src/lib/services/maintenance/store/effects/asset-mobile-well.effects';
import * as activityEffects from 'libs/services/src/lib/services/maintenance/store/effects/activity-config.effects';
import * as activityCategoriesEffects from 'libs/services/src/lib/services/maintenance/store/effects/activity-category.effects';
import * as activityCategoryTypeEffects from 'libs/services/src/lib/services/maintenance/store/effects/activity-category-type.effects';
import * as assetsAllocateEffects from '../store/effects/assets.effects';

import { operatorsFeature } from 'libs/services/src/lib/services/maintenance/store/features/operators.features';
import { reportTypesFeature } from 'libs/services/src/lib/services/maintenance/store/features/report-type.features';
import { assetsFeature } from 'libs/services/src/lib/services/maintenance/store/features/assets.features';
import { distancesFeature } from 'libs/services/src/lib/services/maintenance/store/features/distance.features';
import { assetMobileWellFeature } from 'libs/services/src/lib/services/maintenance/store/features/asset-mobile-well.features';
import { activityCategoryFeature } from 'libs/services/src/lib/services/maintenance/store/features/activity-category.feature';
import { activityCategoryTypeFeature } from 'libs/services/src/lib/services/maintenance/store/features/activity-category-type.feature';
import { activityConfigFeature } from 'libs/services/src/lib/services/maintenance/store/features/activity-config.features';
import { assetsAllocateFeature } from '../store/features/assets.feature';

export const MaintenanceRoutes: Route[] = [
  {
    path: '',
    redirectTo: 'operators',
    pathMatch: 'full',
  },
  {
    path: 'operators',
    loadComponent: () =>
      import('libs/components/src/lib/pages/operators/operators.page').then(
        (p) => p.OperatorsPage
      ),
    providers: [
      provideEffects([operatorsEffects, reportTypeEffects]),
      provideState(operatorsFeature),
      provideState(reportTypesFeature),
    ],
  },
  {
    path: 'distances',
    loadComponent: () =>
      import('libs/components/src/lib/pages/distances/distances.page').then(
        (p) => p.DistancesPage
      ),
    providers: [
      provideEffects([assetsEffects, distanceEffects]),
      provideState(assetsFeature),
      provideState(distancesFeature),
    ],
  },
  {
    path: 'assets',
    loadComponent: () =>
      import('../pages/assets/assets.page').then((p) => p.AssetsPage),
    providers: [
      provideEffects([
        assetsEffects,
        operatorsEffects,
        assetsAllocateEffects,
        assetsMobileWellEffects,
      ]),
      provideState(assetsFeature),
      provideState(assetsAllocateFeature),
      provideState(operatorsFeature),
      provideState(assetMobileWellFeature),
    ],
  },
  {
    path: 'activity',
    loadComponent: () =>
      import('libs/components/src/lib/pages/activities/activities.page').then(
        (p) => p.ActivitiesPage
      ),
    providers: [
      provideEffects([activityEffects]),
      provideState(activityConfigFeature),
    ],
  },
  {
    path: 'activity-categories',
    loadComponent: () =>
      import(
        'libs/components/src/lib/pages/activity-categories/activity-categories.page'
      ).then((p) => p.ActivityCategoriesPage),
    providers: [
      provideEffects([activityCategoriesEffects, activityCategoryTypeEffects]),
      provideState(activityCategoryFeature),
      provideState(activityCategoryTypeFeature),
    ],
  },
];
