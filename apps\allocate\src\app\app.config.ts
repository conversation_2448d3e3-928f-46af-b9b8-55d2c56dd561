import { NgxMatMomentModule } from '@angular-material-components/moment-adapter';
import {
  MAT_MOMENT_DATE_ADAPTER_OPTIONS,
  MomentDateAdapter,
} from '@angular/material-moment-adapter';
import {
  DateAdapter,
  MAT_DATE_FORMATS,
  MAT_DATE_LOCALE,
} from '@angular/material/core';
import { NGX_MAT_DATE_FORMATS } from '@angular-material-components/datetime-picker';

// to part remove after refactoring 

import { provideHttpClient, withInterceptors } from '@angular/common/http';
import {
  APP_INITIALIZER,
  ApplicationConfig,
  importProvidersFrom,
} from '@angular/core';
import { DatePipe } from '@angular/common';
import { provideAnimations } from '@angular/platform-browser/animations';
import { provideRouter, withComponentInputBinding } from '@angular/router';

import { provideRouterStore, routerReducer } from '@ngrx/router-store';
import { Store, provideState, provideStore } from '@ngrx/store';
import { provideEffects } from '@ngrx/effects';

import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { MatSnackBarModule } from '@angular/material/snack-bar';

import { provideAuth0 } from '@auth0/auth0-angular';

import { authInterceptor } from 'libs/auth/src/lib/auth-interceptor';
import { appInitialise } from 'libs/services/src/lib/services/functions/app-initialise';
import { Auth0Environment } from 'libs/env/src/environments/auth0_env';
import { apiRequestInterceptor } from 'libs/services/src/lib/services/interceptors/api-request-interceptor';

import { authFeature } from 'libs/auth/src/lib/store/auth.feature';
import { currentUserFeature } from 'libs/auth/src/lib/store/current-user/current-user.features';
import { clientLocationsFeature } from 'libs/services/src/lib/services/client-locations/store/features';
import {
  locationsFeature,
  usersFeature,
} from 'libs/services/src/lib/services/maintenance/store/features';

import * as authEffects from 'libs/auth/src/lib/store/auth.effects';
import * as currentUserEffects from 'libs/auth/src/lib/store/current-user/current-user.effects';
import * as clientLocationEffects from 'libs/services/src/lib/services/client-locations/store/effects';
import * as appEffects from './store/effects/app.effects';
import * as locationEffects from 'libs/services/src/lib/services/maintenance/store/effects/locations.effects';
import * as lighthouseEffects from 'libs/components/src/lib/store/lighthouse.effects';
import * as lookaheadEffects from 'libs/services/src/lib/services/lookahead/store/effects/lookahead.effect';
import * as settingEffects from 'libs/services/src/lib/services/settings/shared/store/effects';

import { AllocateWebRoutes } from './allocate.routes';
import { settingsFeature } from 'libs/services/src/lib/services/settings/shared/store/features/setting.features';
import { DATE_FORMAT, TIME_FORMAT } from 'libs/components/src/lib/custom-date-adapter';

export const appConfig: ApplicationConfig = {
  providers: [
    DatePipe,
    provideRouter(AllocateWebRoutes, withComponentInputBinding()),
    provideState(authFeature),
    provideState(locationsFeature),
    provideState(currentUserFeature),
    provideState(clientLocationsFeature),
    provideState(usersFeature),
    provideState(settingsFeature),
    provideEffects([
      authEffects,
      currentUserEffects,
      clientLocationEffects,
      locationEffects,
      lighthouseEffects,
      appEffects,
      lookaheadEffects,
      settingEffects,
    ]),
    provideAuth0({
      ...Auth0Environment.auth,
      httpInterceptor: {
        ...Auth0Environment.httpInterceptor,
      },
      cacheLocation: 'localstorage',
      useRefreshTokens: true,
    }),
    provideAnimations(),
    provideStore(
      {},
      {
        runtimeChecks: {
          strictStateImmutability: true,
          strictActionImmutability: true,
          strictStateSerializability: true,
          strictActionWithinNgZone: true,
        },
      }
    ),
    MatDialog,
    importProvidersFrom(NgxMatMomentModule),
    importProvidersFrom(MatDialogModule),
    importProvidersFrom(MatSnackBarModule),
    provideHttpClient(
      withInterceptors([apiRequestInterceptor, authInterceptor])
    ),
    provideRouterStore(),
    provideState('router', routerReducer),
    {
      provide: APP_INITIALIZER,
      useFactory: appInitialise,
      deps: [Store],
      multi: true,
    },
    {
      provide: DateAdapter,
      useClass: MomentDateAdapter,
      deps: [MAT_DATE_LOCALE, MAT_MOMENT_DATE_ADAPTER_OPTIONS],
    },
    {
      provide: MAT_DATE_FORMATS,
      useValue: DATE_FORMAT,
    },
    {
      provide: NGX_MAT_DATE_FORMATS,
      useValue: TIME_FORMAT,
    },
  ],
};
