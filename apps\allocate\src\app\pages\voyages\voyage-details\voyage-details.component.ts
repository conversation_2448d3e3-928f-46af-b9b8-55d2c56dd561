import { Component, computed, inject, OnInit } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { VoyageHeaderComponent } from '../../../components/voyages/voyage-header/voyage-header.component';
import { Store } from '@ngrx/store';
import { VoyagesActions } from 'libs/services/src/lib/services/voyages/store/actions/voyages.actions';
import { PortalDirective } from 'libs/components/src/lib/directives/portal.directive';
import { HeaderNavComponent } from 'libs/components/src/lib/components/header-nav/header-nav.component';
import { NavBar } from 'libs/components/src/lib/interfaces/nav-bar.interface';

@Component({
  selector: 'lha-voyage-details',
  standalone: true,
  imports: [
    RouterOutlet,
    VoyageHeaderComponent,
    PortalDirective,
    HeaderNavComponent,
  ],
  templateUrl: './voyage-details.component.html',
  styleUrls: ['./voyage-details.component.scss'],
})
export class VoyageDetailsComponent implements OnInit {
  store = inject(Store);
  navItems = computed<NavBar[]>(() => {
    return [
      {
        title: 'Activity',
        link: `activity`,
        permissions: [],
      },
      {
        title: 'Bulk Transactions',
        link: `bulks`,
        permissions: [],
      },
      {
        title: 'Tank Management',
        link: 'tank-management',
        permissions: [],
      },
      {
        title: 'Deck Usage',
        link: 'deck-usage',
        permissions: [],
      },
      {
        title: 'Requests',
        link: 'bulk-requests',
        permissions: [],
      },
    ];
  });

  ngOnInit(): void {
    this.loadVoyage();
  }

  loadVoyage(): void {
    this.store.dispatch(VoyagesActions.load_Voyage());
  }
}
