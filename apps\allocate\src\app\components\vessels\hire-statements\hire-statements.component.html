<div class="hire_statement" *ngIf="hireStatementsState()">
  <div class="hire_statement__left">
    <lha-table
      [columns]="columns"
      [loading]="hireStatementsState().loading.list"
      [exportLoading]="hireStatementsState().loading.export"
      [data]="hireStatementsState().hireStatements"
      [canExportTable]="true"
      [pageSize]="query.pageSize"
      [selectableRows]="true"
      propertyId="hireStatementId"
      tableName="hire-statements"
      (queryChange)="onChangeQuery($event)"
      (exportTable)="exportHireStatements()"
    >
      <h3 class="mat-title--warn" tableHeaderLeft>Hire Statement</h3>
      <button
        tableHeaderRightBtn
        mat-raised-button
        (click)="addEditHireStatement()"
        color="primary"
      >
        Create
      </button>
      <ng-template lhaHeaderCellTemplate="dayRate">
        <span>
          Day
          <span class="no-wrap">Rate{{ ' ' + appSettings()?.currency }}</span>
        </span>
      </ng-template>
      <ng-template lhaCellTemplate="vesselId" let-item>
        <a
          [routerLink]="['..', item.hireStatementId]"
          routerLinkActive="active"
          #itemLink="routerLinkActive"
        >
          <mat-checkbox
            color="primary"
            [checked]="itemLink.isActive"
          ></mat-checkbox>
        </a>
      </ng-template>
      <ng-template lhaCellTemplate="isOnHire" let-item>
        <mat-icon
          [ngClass]="item.isOnHire ? 'mat-icon--accept' : 'mat-icon--cancel'"
          >{{ item.isOnHire ? 'check_circle' : 'cancel' }}</mat-icon
        >
      </ng-template>
      <ng-template lhaCellTemplate="dayRate" let-item>
        {{ item.dayRate | number : '1.2-2' }}
      </ng-template>
      <ng-template lhaCellTemplate="deliveryDate" let-item>
        <span class="no-wrap">{{
          item.deliveryDate | date : 'dd/MM/yyyy HH:mm'
        }}</span>
      </ng-template>
      <ng-template lhaCellTemplate="redeliveryDate" let-item>
        <span class="no-wrap">{{
          item.redeliveryDate | date : 'dd/MM/yyyy HH:mm'
        }}</span>
      </ng-template>
      <ng-template lhaCellTemplate="hireStatementId" let-item>
        <button
          mat-icon-button
          color="primary"
          (click)="addEditHireStatement(item)"
        >
          <mat-icon>edit</mat-icon>
        </button>
        <button
          mat-icon-button
          color="warn"
          (click)="removeHireStatement(item)"
        >
          <mat-icon>delete</mat-icon>
        </button>
      </ng-template>
    </lha-table>
  </div>
  <div class="hire_statement__right" *ngIf="hireStatements()">
    <lha-table
      [columns]="columnsHSBulks"
      [loading]="hireStatementsState().loading.bulkList"
      [exportLoading]="hireStatementsState().loading.exportBulks"
      [data]="hireStatementsState().hireStatementBulks"
      [canExportTable]="hireStatements.length > 0"
      [pageSize]="query.pageSize"
      tableName="hire-statements-bulks"
      (exportTable)="exportHireStatementBulks()"
    >
      <h3 class="mat-title--warn" tableHeaderLeft style="margin-bottom: 5px">
        Hire Statement Bulk
      </h3>
      <button
        tableHeaderRightBtn
        mat-raised-button
        (click)="addEditHireStatementBulk()"
        [disabled]="!hireStatementsState().hireStatements.length || isOffHire"
        color="primary"
      >
        Create
      </button>
      <ng-template lhaHeaderCellTemplate="price">
        <span class="no-wrap">Price{{ ' ' + appSettings()?.currency }}</span>
      </ng-template>
      <ng-template lhaCellTemplate="bulkTypeId" let-item>
        <span class="no-wrap">{{ item.bulkType.name }}</span>
      </ng-template>
      <ng-template lhaCellTemplate="dateLoaded" let-item>
        <span class="no-wrap">{{
          item.dateLoaded | date : 'dd/MM/yyyy HH:mm'
        }}</span>
      </ng-template>
      <ng-template lhaCellTemplate="price" let-item>
        {{ item.price | number : '1.2-2' }}
      </ng-template>
      <ng-template lhaCellTemplate="startQuantity" let-item>
        {{ item.startQuantity ?? 'N/A' }}
      </ng-template>
      <ng-template lhaCellTemplate="endQuantity" let-item>
        {{ item.endQuantity ?? 'N/A' }}
      </ng-template>
      <ng-template lhaCellTemplate="hireStatementBulkId" let-item>
        <button
          mat-icon-button
          color="primary"
          [disabled]="isOffHire"
          (click)="addEditHireStatementBulk(item)"
        >
          <mat-icon>edit</mat-icon>
        </button>
        <button
          mat-icon-button
          color="warn"
          [disabled]="isOffHire"
          (click)="removeHireStatementBulk(item)"
        >
          <mat-icon>delete</mat-icon>
        </button>
      </ng-template>
    </lha-table>
  </div>
</div>
