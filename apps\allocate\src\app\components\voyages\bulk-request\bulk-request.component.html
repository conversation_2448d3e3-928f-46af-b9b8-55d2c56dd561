<lha-table
  *ngIf="bulkRequestsState()"
  [columns]="dynamicColumns()"
  [loading]="bulkRequestsState().loading.list"
  [exportLoading]="bulkRequestsState().loading.export"
  [data]="bulkRequestsState().bulkRequests"
  [canExportTable]="true"
  [pageSize]="query.pageSize"
  [tableName]="voyage()?.voyageNumber + '_bulks-requests'"
  (exportTable)="exportBulkRequests()"
>
  <h3 class="mat-title--warn" tableHeaderTitle>Bulk Request</h3>
  <ng-container tableHeaderRightBtn>
    <button mat-icon-button *lhaIsVoyageLocked style="pointer-events: none">
      <mat-icon color="warn">lock</mat-icon>
    </button>
    <button
      mat-raised-button
      lhaIsVoyageLockedDisable
      (click)="addEditBulkRequest()"
      color="primary"
    >
      Create
    </button>
  </ng-container>
  <ng-template lhaCellTemplate="bulkType" let-item>
    {{ item.bulkType.name }}
  </ng-template>
  <ng-template lhaCellTemplate="client" let-item>
    {{ item.client.name }}
  </ng-template>
  <ng-template lhaCellTemplate="billedAsset" let-item>
    {{ item.billedAsset.name }}
  </ng-template>
  <ng-template lhaCellTemplate="bulkRequestId" let-item>
    <button
      mat-icon-button
      color="primary"
      (click)="addEditBulkRequest(item)"
      lhaIsVoyageLockedDisable
    >
      <mat-icon>edit</mat-icon>
    </button>
    <button
      mat-icon-button
      color="warn"
      (click)="removeBulkRequest(item)"
      lhaIsVoyageLockedDisable
    >
      <mat-icon>delete</mat-icon>
    </button>
  </ng-template>
</lha-table>
