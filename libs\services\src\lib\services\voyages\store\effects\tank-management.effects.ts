import { Actions, concatLatestFrom, createEffect, ofType } from '@ngrx/effects';
import { inject } from '@angular/core';
import { catchError, map, mergeMap, of } from 'rxjs';
import { HttpErrorResponse } from '@angular/common/http';
import { TankManagementActions } from '../actions/tank-management.actions';
import { TankManagement } from '../../interfaces/tank-management.interface';
import { Store } from '@ngrx/store';
import { tankManagementsFeature } from '../features';
import { MatDialog } from '@angular/material/dialog';
import { filter, tap } from 'rxjs/operators';
import { FileService } from '../../../file.service';
import { TankManagementAddEditComponent } from '../../../../../../../../apps/allocate/src/app/components/voyages/tank-management-add-edit/tank-management-add-edit.component';
import { TankManagementService } from '../../tank-management.service';
import { ActivatedRoute } from '@angular/router';
import { UtilityService } from '../../../utility.service';
import { Bulk } from '../../interfaces/bulk.interface';
import { DialogOptions } from '../../../config/dialog-options';
import { ConstantService } from '../../../constant.service';
import { Constant } from 'libs/services/src/lib/services/shared/constant.interface';

export const loadTankManagements = createEffect(
  (
    actions = inject(Actions),
    store = inject(Store),
    utilityService = inject(UtilityService),
    tankManagementService = inject(TankManagementService),
    activatedRoute = inject(ActivatedRoute)
  ) => {
    return actions.pipe(
      ofType(
        TankManagementActions.load_Tank_Managements,
        TankManagementActions.load_Tank_Managements_Lists,
        TankManagementActions.update_Tank_Management_Queries,
        TankManagementActions.remove_Tank_Management_Success,
        TankManagementActions.add_Tank_Management_Success,
        TankManagementActions.edit_Tank_Management_Success
      ),
      filter(
        () =>
          !!utilityService.getParamsFromRoute(activatedRoute.snapshot.root)[
            'id'
          ]
      ),
      concatLatestFrom(() => store.select(tankManagementsFeature.selectQuery)),
      map(([, res]) => {
        return {
          query: res,
          voyageId: utilityService.getParamsFromRoute(
            activatedRoute.snapshot.root
          )['id'],
        };
      }),
      mergeMap((action) =>
        tankManagementService
          .loadTankManagementsByVoyageId(action.voyageId, action.query)
          .pipe(
            map((res: TankManagement[]) =>
              TankManagementActions.load_Tank_Managements_Success({
                tankManagements: res,
              })
            ),
            catchError((error: HttpErrorResponse) =>
              of(TankManagementActions.load_Tank_Managements_Failure({ error }))
            )
          )
      )
    );
  },
  {
    functional: true,
  }
);

export const loadBulkTransactions = createEffect(
  (
    actions = inject(Actions),
    store = inject(Store),
    utilityService = inject(UtilityService),
    tankManagementService = inject(TankManagementService),
    activatedRoute = inject(ActivatedRoute)
  ) => {
    return actions.pipe(
      ofType(TankManagementActions.load_Tank_Managements_Lists),
      filter(
        () =>
          !!utilityService.getParamsFromRoute(activatedRoute.snapshot.root)[
            'id'
          ]
      ),
      map(() => {
        return utilityService.getParamsFromRoute(activatedRoute.snapshot.root)[
          'id'
        ];
      }),
      mergeMap((voyageId) =>
        tankManagementService.loadBulkTransactions(voyageId).pipe(
          map((res: Bulk[]) =>
            TankManagementActions.load_Bulk_Transactions_Success({
              bulkTransactions: res,
            })
          ),
          catchError((error: HttpErrorResponse) =>
            of(TankManagementActions.load_Bulk_Transactions_Failure({ error }))
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const loadFluidTypes = createEffect(
  (actions = inject(Actions), constantService = inject(ConstantService)) => {
    return actions.pipe(
      ofType(TankManagementActions.load_Tank_Managements_Lists),
      mergeMap(() =>
        constantService.loadBulkFluidTypes().pipe(
          map((res: Constant[]) =>
            TankManagementActions.load_Bulk_Fluid_Types_Success({
              bulkFluidTypes: res,
            })
          ),
          catchError((error: HttpErrorResponse) =>
            of(TankManagementActions.load_Bulk_Fluid_Types_Failure({ error }))
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const removeTankManagement = createEffect(
  (
    actions = inject(Actions),
    tankManagementService = inject(TankManagementService)
  ) => {
    return actions.pipe(
      ofType(TankManagementActions.remove_Tank_Management),
      mergeMap((action) =>
        tankManagementService.removeTankManagement(action.id).pipe(
          map((res: TankManagement) =>
            TankManagementActions.remove_Tank_Management_Success({
              tankManagement: res,
              successMessage: 'TankManagement remove successfully!',
            })
          ),
          catchError((error: HttpErrorResponse) =>
            of(TankManagementActions.remove_Tank_Management_Failure({ error }))
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const addTankManagement = createEffect(
  (
    actions = inject(Actions),
    tankManagementService = inject(TankManagementService)
  ) => {
    return actions.pipe(
      ofType(TankManagementActions.add_Tank_Management),
      mergeMap((action) =>
        tankManagementService.addTankManagement(action.tankManagement).pipe(
          map((res: TankManagement) =>
            TankManagementActions.add_Tank_Management_Success({
              tankManagement: res,
              successMessage: 'TankManagement added successfully!',
            })
          ),
          catchError((error: HttpErrorResponse) =>
            of(
              TankManagementActions.add_Tank_Management_Failure({
                error: error,
              })
            )
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const editTankManagement = createEffect(
  (
    actions = inject(Actions),
    tankManagementService = inject(TankManagementService)
  ) => {
    return actions.pipe(
      ofType(TankManagementActions.edit_Tank_Management),
      mergeMap((action) =>
        tankManagementService
          .editTankManagement(action.tankManagementId, action.tankManagement)
          .pipe(
            map((res: TankManagement) =>
              TankManagementActions.edit_Tank_Management_Success({
                tankManagement: res,
                successMessage: 'TankManagement edited successfully!',
              })
            ),
            catchError((error: HttpErrorResponse) =>
              of(
                TankManagementActions.edit_Tank_Management_Failure({
                  error: error,
                })
              )
            )
          )
      )
    );
  },
  {
    functional: true,
  }
);

export const exportTankStatuses = createEffect(
  (
    actions = inject(Actions),
    tankManagementService = inject(TankManagementService),
    fileService = inject(FileService),
    utilityService = inject(UtilityService),
    activatedRoute = inject(ActivatedRoute)
  ) => {
    return actions.pipe(
      ofType(TankManagementActions.export_Tank_Statuses),
      map(() =>
        utilityService.getParamsFromRoute(activatedRoute.snapshot.root)
      ),
      mergeMap((params) =>
        tankManagementService.exportTankStatuses(params['id']).pipe(
          map((res: ArrayBuffer) => {
            fileService.downloadFile(res, 'Tank Statuses');
            return TankManagementActions.export_Tank_Statuses_Success();
          }),
          catchError((error: HttpErrorResponse) =>
            of(TankManagementActions.export_Tank_Statuses_Failure({ error }))
          )
        )
      )
    );
  },
  {
    // You can omit this part if not needed
    // This indicates that the effect is a pure function and can be tree-shaken during optimization
    functional: true,
  }
);

export const openTankManagementDialog = createEffect(
  (
    actions = inject(Actions),
    dialog = inject(MatDialog),
    dialogOptions = inject(DialogOptions)
  ) => {
    return actions.pipe(
      ofType(TankManagementActions.open_Tank_Management_Dialog),
      tap((action) => {
        dialog.open(TankManagementAddEditComponent, {
          ...dialogOptions,
          data: {
            tankManagement: action.tankManagement,
          },
        });
      })
    );
  },
  {
    functional: true,
    dispatch: false,
  }
);
