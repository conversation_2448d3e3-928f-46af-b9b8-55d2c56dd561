import { DestroyRef, Directive, inject, OnInit } from '@angular/core';
import { NgIf } from '@angular/common';
import { Store } from '@ngrx/store';
import { voyagesFeature } from 'libs/services/src/lib/services/voyages/store/features';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

@Directive({
  selector: '[lhaIsVoyageUnlocked]',
  standalone: true,
  hostDirectives: [NgIf],
})
export class IsVoyageUnlockedDirective implements OnInit {
  private readonly store = inject(Store);
  private readonly ngIfRef = inject(NgIf);
  private readonly destroyRef = inject(DestroyRef)

  ngOnInit(): void {
    this.store
      .select(voyagesFeature.selectActiveVoyage)
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((res) => {
        this.ngIfRef.ngIf = !!(res && !res.isVoyageLocked);
      });
  }
}
