import { Component, computed, inject, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { TableComponent } from 'libs/components/src/lib/components/table/table.component';
import { Store } from '@ngrx/store';
import { Column } from 'libs/components/src/lib/components/table/column.model';
import {
  AsyncPipe,
  DatePipe,
  DecimalPipe,
  NgStyle,
  NgIf,
} from '@angular/common';
import { CellTemplateDirective } from 'libs/components/src/lib/components/table/cell-template.directive';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { confirmActions } from 'libs/components/src/lib/store/confirm.actions';
import { PropertyNamePipe } from 'libs/components/src/lib/pipes/property-name.pipe';
import {
  bulksFeature,
  voyagesFeature,
} from 'libs/services/src/lib/services/voyages/store/features';
import { bulkInitialState } from 'libs/services/src/lib/services/voyages/store/states/bulk.state';
import { Bulk } from 'libs/services/src/lib/services/voyages/interfaces/bulk.interface';
import { BulksActions } from 'libs/services/src/lib/services/voyages/store/actions/bulks.actions';
import { SearchQuery } from 'libs/components/src/lib/interfaces/search-query.interface';
import { ActivatedRoute } from '@angular/router';
import { TableFilter } from 'libs/components/src/lib/components/table/table-filter.interface';
import { FormControl } from '@angular/forms';
import { IsVoyageLockedDirective } from '../../../shared/directives/is-voyage-locked.directive';
import { HeaderCellTemplateDirective } from 'libs/components/src/lib/components/table/header-cell-template.directive';
import { settingsFeature } from 'libs/services/src/lib/services/settings/shared/store/features';
import { IsVoyageLockedDisableDirective } from '../../../shared/directives/is-voyage-locked-disable.directive';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { Subject } from 'rxjs';
import { map } from 'rxjs/operators';
import { VoyageStatus } from 'libs/components/src/lib/enums/voyage-status.enum';

@Component({
  selector: 'lha-bulk-transaction',
  standalone: true,
  imports: [
    TableComponent,
    AsyncPipe,
    CellTemplateDirective,
    MatButtonModule,
    MatIconModule,
    PropertyNamePipe,
    NgStyle,
    NgIf,
    DatePipe,
    IsVoyageLockedDirective,
    DecimalPipe,
    HeaderCellTemplateDirective,
    IsVoyageLockedDisableDirective,
    MatProgressSpinnerModule,
  ],
  templateUrl: './bulks.component.html',
  styleUrls: ['./bulks.component.scss'],
})
export class BulksComponent implements OnInit, OnDestroy {
  store = inject(Store);
  route = inject(ActivatedRoute);
  bulksState = this.store.selectSignal(bulksFeature.selectBulksState);
  voyage = this.store.selectSignal(voyagesFeature.selectActiveVoyage);
  query: SearchQuery = bulkInitialState.query;
  filters: TableFilter[] = [
    {
      columnName: 'bulkTypeId',
      selected: new FormControl(),
      propertyName: 'name',
      options: this.store.select(bulksFeature.selectBulkTypesList),
      propertyId: 'bulkTypeId',
      placeholder: 'Filter Bulk Type',
    },
  ];
  appSettings$ = this.store.select(settingsFeature.selectAppSettings);
  bulkQuantities$ = this.store
    .select(bulksFeature.selectFilteredBulkQuantities)
    .pipe(
      map((res) => {
        return {
          startQuantity: res.reduce(
            (acc, curr) => acc + curr.startQuantityDisplay,
            0
          ),
          endQuantity: res.reduce(
            (acc, curr) => acc + curr.endQuantityDisplay,
            0
          ),
        };
      })
    );
  bulktype$ = this.store.select(bulksFeature.selectBulkType);
  startQuantity = 0;
  endQuantity = 0;
  unsubscribe: Subject<boolean> = new Subject();
  columns: Column<Bulk>[] = [
    new Column('bulkTransactionNumber', 'Bulk Transaction Number', {
      sortHeader: true,
    }),
    new Column('quantity', 'Quantity', { sortHeader: true }),
    new Column('bulkTypeUnitName', 'Unit', { sortHeader: true }),
    new Column('dayRatePrice', 'Price', { sortHeader: true }),
    new Column('completedDateTime', 'Date', { sortHeader: true }),
    new Column('bulkTypeName', 'Bulk Type', { sortHeader: true }),
    new Column('tankTypeName', 'Tank Type', { sortHeader: true }),
    new Column('transactionTypeDescription', 'Transaction Type', {
      sortHeader: true,
    }),
    new Column('assetName', 'Asset', { sortHeader: true }),
    new Column('delTicket', 'Del. Ticket', { sortHeader: true }),
  ];
  voyageStatus = VoyageStatus;

  dynamicColumns = computed(() => {
    if (this.voyage()?.voyageStatus !== this.voyageStatus.Completed) {
      return [
        ...this.columns,
        new Column('bulkTransactionId', 'Actions', {
          attributeData: [{ name: 'data-exclude', value: 'true' }],
          attributeHeaderData: [{ name: 'data-exclude', value: 'true' }],
        }),
      ];
    }
    return this.columns;
  });

  ngOnInit(): void {
    this.loadLists();
  }

  ngOnDestroy(): void {
    this.store.dispatch(BulksActions.set_Bulk_Type_Id({ id: '' }));
  }

  changeBulkType(value: string): void {
    this.store.dispatch(BulksActions.set_Bulk_Type_Id({ id: value }));
  }

  loadLists(): void {
    this.store.dispatch(BulksActions.load_Bulks_Lists());
  }

  exportBulkTransactions(): void {
    this.store.dispatch(BulksActions.export_Bulks());
  }

  onChangeQuery(query: SearchQuery): void {
    this.query = {
      ...this.query,
      ...query,
    };
    this.store.dispatch(
      BulksActions.update_Bulk_Queries({ query: this.query })
    );
  }

  addEditBulk(bulk: Bulk | null): void {
    this.store.dispatch(BulksActions.open_Bulk_Dialog({ bulk }));
  }

  removeBulk(item: Bulk): void {
    this.store.dispatch(
      confirmActions.request_Confirmation({
        titles: {
          title:
            'Warning, Deleting this Bulk Transaction may affect other parts of Allocate. Do you want to remove this Bulk Transaction?',
          btnConfirm: 'Yes Delete',
        },
        confirm: BulksActions.remove_Bulk({ id: item.bulkTransactionId }),
      })
    );
  }
}
