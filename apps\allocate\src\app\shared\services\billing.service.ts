import { inject, Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { Billing } from '../interfaces/billing.interface';
import { BillingsActions } from '../../store/actions/billing.actions';
import { Store } from '@ngrx/store';
import { BillingHistory } from '../interfaces/billing-history.interface';

@Injectable({
  providedIn: 'root',
})
export class BillingService {
  private readonly http = inject(HttpClient);
  private readonly store = inject(Store);

  getBillingList(): Observable<Billing[]> {
    return this.http.get<Billing[]>('/api/billingperiod/');
  }

  getBillingById(id: string): Observable<Billing> {
    return this.http.get<Billing>(`/api/billingperiod/${id}`);
  }

  lockBillingPeriod(id: string): Observable<Billing> {
    return this.http.post<Billing>(`/api/billingPeriod/${id}`, null);
  }

  unlockBillingPeriod(id: string): Observable<Billing> {
    return this.http.post<Billing>(`/api/billingPeriod/unlock/${id}`, null);
  }

  calcBillingPeriod(
    billingPeriodId: string,
    comments: string
  ): Observable<Billing> {
    return this.http.post<Billing>('/api/calculation/', {
      billingPeriodId,
      comments,
    });
  }

  exportBillingPeriodRawData(id: string): Observable<ArrayBuffer> {
    return this.http.get(`/api/billingPeriod/exportRawData/${id}`, {
      responseType: 'arraybuffer',
    });
  }

  exportBillingPeriods(): Observable<ArrayBuffer> {
    return this.http.get('/api/billingperiod/exportbillingperiods', {
      responseType: 'arraybuffer',
    });
  }

  compareBillingHistory(ids: string[]): Observable<ArrayBuffer> {
    return this.http.get(
      `/api/billingperioddocument/comparefiles/${ids[0]}/${ids[1]}`,
      {
        responseType: 'arraybuffer',
      }
    );
  }

  downloadBillingHistory(id: string): Observable<ArrayBuffer> {
    return this.http.get(`/api/billingperioddocument/download/${id}`, {
      responseType: 'arraybuffer',
    });
  }

  loadBillingHistory(id: string): Observable<BillingHistory[]> {
    return this.http.get<BillingHistory[]>(`/api/billingperioddocument/${id}`);
  }

  toggleBillingPeriodLock(item: Billing | null): void {
    if (!item) {
      return;
    }
    if (item.isLocked) {
      this.store.dispatch(
        BillingsActions.unlock_Billing_Period({ id: item.billingPeriodId })
      );
    } else {
      this.store.dispatch(
        BillingsActions.lock_Billing_Period({ id: item.billingPeriodId })
      );
    }
  }
}
