import { Component, inject, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import {
  MAT_DIALOG_DATA,
  MatDialogModule,
  MatDialogRef,
} from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { AsyncPipe, NgForOf, NgIf } from '@angular/common';
import { LoadingDirective } from 'libs/components/src/lib/directives/loading.directive';
import { OnlyDigitsDirective } from 'libs/components/src/lib/directives/only-digits.directive';
import { UtilityService } from 'libs/services/src/lib/services/utility.service';
import { ActivatedRoute } from '@angular/router';
import { BulkRequest } from 'libs/services/src/lib/services/voyages/interfaces/bulk-request.interface';
import { Store } from '@ngrx/store';
import { Actions, ofType } from '@ngrx/effects';
import { bulkRequestFeature } from 'libs/services/src/lib/services/voyages/store/features';
import { Subject, takeUntil } from 'rxjs';
import { greaterThan } from 'libs/components/src/lib/validators/greaterThan';
import { decimalPoint } from 'libs/components/src/lib/validators/decimal-point';
import { BulkRequestActions } from 'libs/services/src/lib/services/voyages/store/actions/bulk-request.actions';
import { SingleSelectComponent } from 'libs/components/src/lib/components/single-select/single-select.component';
import { CdkDrag, CdkDragHandle } from '@angular/cdk/drag-drop';

@Component({
  selector: 'lha-bulk-request-add-edit',
  standalone: true,
  imports: [
    MatDialogModule,
    MatButtonModule,
    ReactiveFormsModule,
    MatInputModule,
    MatSelectModule,
    NgForOf,
    AsyncPipe,
    LoadingDirective,
    NgIf,
    OnlyDigitsDirective,
    SingleSelectComponent,
    CdkDrag,
    CdkDragHandle,
  ],
  templateUrl: './bulk-request-add-edit.component.html',
  styleUrls: ['./bulk-request-add-edit.component.scss'],
})
export class RequestRequestAddEditComponent implements OnInit, OnDestroy {
  dialogRef = inject(MatDialogRef<RequestRequestAddEditComponent>);
  utilityService = inject(UtilityService);
  activatedRoute = inject(ActivatedRoute);
  data: { bulkRequest: BulkRequest } = inject(MAT_DIALOG_DATA);
  store = inject(Store);
  action = inject(Actions);
  vm$ = this.store.select(bulkRequestFeature.selectBulkRequestState);
  isAdd = true;
  unsubscribe: Subject<boolean> = new Subject();
  voyageId = this.utilityService.getParamsFromRoute(
    this.activatedRoute.snapshot.root
  )['id'];
  bulkRequest!: BulkRequest;
  form = new FormGroup({
    bulkTypeId: new FormControl<string>('', Validators.required),
    billedAssetId: new FormControl<string>('', [Validators.required]),
    quantity: new FormControl<null | number>(null, [
      Validators.required,
      greaterThan(0),
      decimalPoint(3),
    ]),
  });

  ngOnInit(): void {
    this.subCloseDialog();
    this.initAddEdit();
  }

  ngOnDestroy(): void {
    this.unsubscribe.next(true);
    this.unsubscribe.complete();
  }

  private initAddEdit(): void {
    this.isAdd = !this.data.bulkRequest;
    if (!this.isAdd) {
      this.bulkRequest = this.data.bulkRequest;
      this.pathForm(this.bulkRequest);
    }
  }

  private subCloseDialog(): void {
    this.action
      .pipe(
        ofType(
          BulkRequestActions.add_Bulk_Request_Success,
          BulkRequestActions.edit_Bulk_Request_Success
        )
      )
      .pipe(takeUntil(this.unsubscribe))
      .subscribe(() => {
        this.dialogRef.close();
      });
  }

  private pathForm(bulkRequest: BulkRequest): void {
    this.form.patchValue(bulkRequest);
  }

  saveBulkRequest(): void {
    if (this.form.invalid) {
      this.form.markAllAsTouched();
      return;
    }

    const model = {
      ...this.form.value,
      voyageId: this.voyageId,
    } as BulkRequest;

    if (this.isAdd) {
      this.store.dispatch(
        BulkRequestActions.add_Bulk_Request({ bulkRequest: model })
      );
    } else {
      this.store.dispatch(
        BulkRequestActions.edit_Bulk_Request({
          bulkRequest: model,
          id: this.bulkRequest.bulkRequestId,
        })
      );
    }
  }
}
