import { Component, inject, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import {
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { LoadingDirective } from 'libs/components/src/lib/directives/loading.directive';
import { MatButtonModule } from '@angular/material/button';
import {
  MAT_DIALOG_DATA,
  MatDialogModule,
  MatDialogRef,
} from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatOptionModule } from '@angular/material/core';
import { MatSelectModule } from '@angular/material/select';
import { AsyncPipe, NgForOf, NgIf } from '@angular/common';
import { TankManagement } from 'libs/services/src/lib/services/voyages/interfaces/tank-management.interface';
import { ActivatedRoute } from '@angular/router';
import { UtilityService } from 'libs/services/src/lib/services/utility.service';
import { Store } from '@ngrx/store';
import { Actions, ofType } from '@ngrx/effects';
import { tankManagementsFeature } from 'libs/services/src/lib/services/voyages/store/features';
import { Subject, takeUntil } from 'rxjs';
import { TankManagementActions } from 'libs/services/src/lib/services/voyages/store/actions/tank-management.actions';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { NgxMatDatetimePickerModule } from '@angular-material-components/datetime-picker';
import { InitialCapitalizeDirective } from 'libs/components/src/lib/directives/initial-capitalize.directive';
import { SingleSelectComponent } from 'libs/components/src/lib/components/single-select/single-select.component';
import { greaterThan } from 'libs/components/src/lib/validators/greaterThan';
import { decimalPoint } from 'libs/components/src/lib/validators/decimal-point';
import { OnlyDigitsDirective } from 'libs/components/src/lib/directives/only-digits.directive';
import { CdkDrag, CdkDragHandle } from '@angular/cdk/drag-drop';

@Component({
  selector: 'lha-tank-management-add-edit',
  standalone: true,
  imports: [
    FormsModule,
    LoadingDirective,
    MatButtonModule,
    MatDialogModule,
    MatFormFieldModule,
    MatInputModule,
    MatOptionModule,
    MatSelectModule,
    NgForOf,
    ReactiveFormsModule,
    AsyncPipe,
    NgIf,
    MatDatepickerModule,
    MatSlideToggleModule,
    NgxMatDatetimePickerModule,
    InitialCapitalizeDirective,
    SingleSelectComponent,
    OnlyDigitsDirective,
    CdkDrag,
    CdkDragHandle,
  ],
  templateUrl: './tank-management-add-edit.component.html',
  styleUrls: ['./tank-management-add-edit.component.scss'],
})
export class TankManagementAddEditComponent implements OnInit, OnDestroy {
  dialogRef = inject(MatDialogRef<TankManagementAddEditComponent>);
  data: { tankManagement: TankManagement } = inject(MAT_DIALOG_DATA);
  activatedRoute = inject(ActivatedRoute);
  utilityService = inject(UtilityService);
  store = inject(Store);
  action = inject(Actions);
  vm$ = this.store.select(tankManagementsFeature.selectTankManagementsState);
  voyageId = this.utilityService.getParamsFromRoute(
    this.activatedRoute.snapshot.root
  )['id'];
  isAdd = true;
  unsubscribe: Subject<boolean> = new Subject();
  tankManagement!: TankManagement;
  form = new FormGroup({
    bulkTransactionId: new FormControl<string>('', [Validators.required]),
    statusDateTime: new FormControl<Date | null>(null, [Validators.required]),
    dischargeDateTime: new FormControl<Date | null>(null, [
      Validators.required,
    ]),
    specificGravity: new FormControl<number | null>(null, [
      Validators.required,
      Validators.min(0),
    ]),
    quantityOnBoarding: new FormControl<number | null>(null, [
      Validators.required,
      greaterThan(0),
      decimalPoint(3),
    ]),
    isBackload: new FormControl<boolean>(false, []),
    loadDateTime: new FormControl<Date | null>(null, [Validators.required]),
    wellNumber: new FormControl<string>('', []),
    comments: new FormControl<string>('', []),
    quantityTransferred: new FormControl<number | null>(null, [
      Validators.required,
      greaterThan(0),
      decimalPoint(3),
    ]),
    recordingType: new FormControl<string>('', [Validators.required]),
    cleaned: new FormControl<boolean>(false, []),
    status: new FormControl<string>('', []),
  });
  recordingTypeList = [
    {
      name: 'Transactional',
    },
    {
      name: 'Manual',
    },
  ];

  ngOnInit(): void {
    this.subCloseDialog();
    this.initAddEdit();
  }

  ngOnDestroy(): void {
    this.unsubscribe.next(true);
    this.unsubscribe.complete();
  }

  private initAddEdit(): void {
    this.isAdd = !this.data.tankManagement;
    if (!this.isAdd) {
      this.tankManagement = this.data.tankManagement;
      this.pathForm(this.tankManagement);
    }
  }

  private subCloseDialog(): void {
    this.action
      .pipe(
        ofType(
          TankManagementActions.add_Tank_Management_Success,
          TankManagementActions.edit_Tank_Management_Success
        )
      )
      .pipe(takeUntil(this.unsubscribe))
      .subscribe(() => {
        this.dialogRef.close();
      });
  }

  private pathForm(tankManagement: TankManagement): void {
    this.form.patchValue({
      ...tankManagement,
    });
  }

  saveTankManagement(): void {
    if (this.form.invalid) {
      this.form.markAllAsTouched();
      return;
    }

    const model = {
      ...this.form.value,
      voyageId: this.voyageId,
    } as TankManagement;

    if (this.isAdd) {
      this.store.dispatch(
        TankManagementActions.add_Tank_Management({ tankManagement: model })
      );
    } else {
      this.store.dispatch(
        TankManagementActions.edit_Tank_Management({
          tankManagementId: this.tankManagement.tankStatusId,
          tankManagement: model,
        })
      );
    }
  }
}
