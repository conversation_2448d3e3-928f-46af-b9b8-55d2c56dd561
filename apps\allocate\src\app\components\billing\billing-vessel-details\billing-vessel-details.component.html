<p-card>
  <div class="d-flex justify-content-between gap-20 flex-wrap mb-20">
    <lha-billing-voyage-card
      class="flex-1"
      [style.minWidth.px]="250"
      title="No. of Voyages"
      [mainInfo]="billingVesselView()?.numberOfVoyages || 0"
    ></lha-billing-voyage-card>
    <lha-billing-voyage-card
      class="flex-1"
      [style.minWidth.px]="250"
      title="Current Status"
      [mainInfo]="billingVesselView()?.currentStatus || '-'"
      [text]="billingVesselView()?.type || '-'"
    ></lha-billing-voyage-card>
    <lha-billing-voyage-card
      class="flex-1"
      [style.minWidth.px]="250"
      title="Average Charter Rate"
      [mainInfo]="
        (appSettings()?.currency ?? '') +
        (billingVesselView()?.averageCharterRate || 0 | number : '1.2-2') +
        ' per day'
      "
    ></lha-billing-voyage-card>
    <lha-billing-voyage-card
      class="flex-1"
      [style.minWidth.px]="250"
      title="Total Cost"
      [mainInfo]="
        (appSettings()?.currency ?? '') +
        (billingVesselView()?.totalCost || 0 | number : '1.2-2')
      "
    ></lha-billing-voyage-card>
  </div>

  <div class="d-flex justify-content-end gap-8 mb-20">
    <span class="p-input-icon-left">
      <em class="pi pi-search"></em>
      <input
        type="text"
        pInputText
        [(ngModel)]="searchValue"
        placeholder="Search..."
        (input)="table.filterGlobal(searchValue, 'contains')"
      />
    </span>

    <button
      type="button"
      class="btn-export align-items-center d-flex"
      (click)="export()"
    >
      <img src="assets/icons/exel.svg" />
      Export
    </button>

    <button class="btn-secondary" type="button">
      <em class="pi pi-download"></em>
      Reports
    </button>
  </div>

  <p-table
    #table
    [columns]="listColumns"
    [value]="voyages"
    [scrollable]="true"
    scrollHeight="400px"
    [rowsPerPageOptions]="[10, 25, 50]"
    [paginator]="true"
    [rows]="10"
    [filterDelay]="0"
    [loading]="loading().voyageList"
    [globalFilterFields]="[
      tableFields.voyageNumber,
      tableFields.vesselName,
      tableFields.voyageStartDateTime,
      tableFields.voyageEndDateTime,
      tableFields.distanceSailed,
      tableFields.deckPercentageUsedIn,
      tableFields.deckPercentageUsedOut,
      tableFields.voyageStatus
    ]"
  >
    <ng-template pTemplate="header" let-columns>
      <tr>
        <th
          *ngFor="let column of columns"
          [style.min-width.px]="column.width"
          [style.width.%]="(column.width / tableWidth) * 100"
          [pSortableColumn]="column.field"
          [pSortableColumnDisabled]="!column.sortable"
        >
          <span>{{ column.name }}</span>
          <p-sortIcon *ngIf="column.sortable" [field]="column.field" />
        </th>
      </tr>
    </ng-template>
    <ng-template
      let-index="rowIndex"
      pTemplate="body"
      let-row
      let-columns="columns"
    >
      <tr>
        <ng-container *ngFor="let column of columns">
          <ng-container [ngSwitch]="column.field">
            <td *ngSwitchCase="tableFields.voyageNotValid">
              <div class="d-flex justify-content-center">
                <em *ngIf="row.voyageNotValid" class="icon-red-info"></em>
              </div>
            </td>
            <td *ngSwitchCase="tableFields.voyageNumber">
              <a class="link" [routerLink]="['/voyages', row.voyageId]">{{
                row.voyageNumber
              }}</a>
            </td>
            <td *ngSwitchCase="tableFields.voyageStartDateTime">
              {{ row.voyageStartDateTime | date : 'dd/MM/yyyy HH:mm' }}
            </td>
            <td *ngSwitchCase="tableFields.voyageEndDateTime">
              {{ row.voyageEndDateTime | date : 'dd/MM/yyyy HH:mm' }}
            </td>

            <td *ngSwitchCase="tableFields.voyageStatus">
              <div class="d-flex gap-5 flex-wrap">
                <lha-custom-chip
                  [text]="row.isCompleted ? 'Completed' : 'In Progress'"
                >
                </lha-custom-chip>
              </div>
            </td>
            <td *ngSwitchCase="tableFields.actions">
              <button class="btn-icon-only">
                <em
                  class="pi pi-calendar"
                  (click)="openChangePeriodDialog(row)"
                ></em>
              </button>
            </td>

            <td *ngSwitchDefault>{{ row[column.field] }}</td>
          </ng-container>
        </ng-container>
      </tr>
    </ng-template>
    <ng-template pTemplate="emptymessage">
      <tr>
        <td colspan="8" style="text-align: center">No results found</td>
      </tr>
    </ng-template>
  </p-table>
</p-card>

<lha-billing-period/>
