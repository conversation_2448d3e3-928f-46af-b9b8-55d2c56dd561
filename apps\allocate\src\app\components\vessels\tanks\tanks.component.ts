import { ChangeDetectionStrategy, Component, computed, inject, OnInit } from '@angular/core';
import { TableComponent } from 'libs/components/src/lib/components/table/table.component';
import { AsyncPipe, DatePipe, NgIf, NgClass } from '@angular/common';
import { CellTemplateDirective } from 'libs/components/src/lib/components/table/cell-template.directive';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { Store } from '@ngrx/store';
import { vesselTanksFeature } from 'libs/services/src/lib/services/vessels/store/features';
import { SearchQuery } from 'libs/components/src/lib/interfaces/search-query.interface';
import { Column } from 'libs/components/src/lib/components/table/column.model';
import { VesselTank } from 'libs/services/src/lib/services/vessels/interfaces/vessel-tank.interface';
import { VesselTankActions } from 'libs/services/src/lib/services/vessels/store/actions/vessel-tank.actions';
import { confirmActions } from 'libs/components/src/lib/store/confirm.actions';
import { vesselsInitialState } from 'libs/services/src/lib/services/vessels/store/state/vessels.state';

@Component({
  selector: 'lha-tanks',
  standalone: true,
  imports: [
    TableComponent,
    AsyncPipe,
    CellTemplateDirective,
    MatButtonModule,
    MatIconModule,
    NgIf,
    DatePipe,
    NgClass,
  ],
  templateUrl: './tanks.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class TanksComponent implements OnInit {
  store = inject(Store);
  tanksState = this.store.selectSignal(
    vesselTanksFeature.selectVesselTanksState
  );
  query: SearchQuery = vesselsInitialState.query;

  columns: Column<VesselTank>[] = [
    new Column('name', 'Tank Name', { sortHeader: true }),
    new Column('tankTypeName', 'Tank Type', { sortHeader: true }),
    new Column('quantity', 'Quantity', { sortHeader: true }),
    new Column('tankTypeUnitName', 'Tank Unit', { sortHeader: true }),
    new Column('bulkTypeName', 'Bulk Type', { sortHeader: true }),
    new Column('createdByName', 'Created By', { sortHeader: true }),
    new Column('updatedByName', 'Last Updated By', { sortHeader: true }),
    new Column('cleaned', 'Cleaned', { sortHeader: true }),
    new Column('tankStatusChangeDate', 'Tank Status Change Date', {
      sortHeader: true,
    }),
    new Column('dayRatePrice', 'Day Rate Price', { sortHeader: true }),
    new Column('vesselTankId', 'Actions'),
  ];

  ngOnInit(): void {
    this.loadVesselTanksLists();
  }

  loadVesselTanksLists(): void {
    this.store.dispatch(VesselTankActions.load_Vessel_Tanks_Lists());
  }

  exportVesselTanks(): void {
    this.store.dispatch(VesselTankActions.export_Vessel_Tanks());
  }

  onChangeQuery(query: SearchQuery): void {
    this.query = {
      ...this.query,
      ...query,
    };
    this.store.dispatch(
      VesselTankActions.update_Vessel_Tank_Queries({ query: this.query })
    );
  }

  addEditVesselTank(vesselTank: VesselTank | null): void {
    this.store.dispatch(
      VesselTankActions.open_Vessel_Tank_Dialog({ vesselTank })
    );
  }

  removeVesselTank(item: VesselTank): void {
    this.store.dispatch(
      confirmActions.request_Confirmation({
        titles: {
          title:
            'Warning, Deleting this Vessel Tank may affect other parts of Allocate. Do you want to remove this Vessel Tank?',
          btnConfirm: 'Yes Delete',
        },
        confirm: VesselTankActions.remove_Vessel_Tank({
          id: item.vesselTankId,
        }),
      })
    );
  }
}
