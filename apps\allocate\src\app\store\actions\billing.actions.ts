import { createActionGroup, emptyProps, props } from '@ngrx/store';
import { SearchQuery } from 'libs/components/src/lib/interfaces/search-query.interface';
import { errorProps } from 'libs/components/src/lib/functions/utility.functions';
import { BillingHistory } from '../../shared/interfaces/billing-history.interface';
import { Billing } from '../../shared/interfaces/billing.interface';

export const BillingsActions = createActionGroup({
  source: 'Billings',
  events: {
    update_Billing_Queries: props<{ query: SearchQuery }>(),
    init_Billing_Detail: props<{ billingPeriodId: string }>(),
    load_Billings: emptyProps(),
    load_Billings_Success: props<{ billings: Billing[] }>(),
    load_Billings_Failure: errorProps(),

    load_Billing: props<{ billingPeriodId: string }>(),
    load_Billing_Success: props<{ billing: Billing }>(),
    load_Billing_Failure: errorProps(),

    lock_Billing_Period: props<{ id: string }>(),
    lock_Billing_Period_Success: props<{
      billing: Billing;
      successMessage: string;
    }>(),
    lock_Billing_Period_Failure: errorProps(),

    unlock_Billing_Period: props<{ id: string }>(),
    unlock_Billing_Period_Success: props<{
      billing: Billing;
      successMessage: string;
    }>(),
    unlock_Billing_Period_Failure: errorProps(),

    change_visibility_calc_billing_period: props<{ visible: boolean }>(),
    change_visibility_billing_history_period: props<{
      visible: boolean;
      billing: Billing | null;
    }>(),

    calc_Billing_Period: props<{ billingPeriodId: string; message: string }>(),
    calc_Billing_Period_Success: props<{
      successMessage: string;
      billingPeriodId: string;
    }>(),
    calc_Billing_Period_Failure: errorProps(),
    export_Billing: emptyProps(),
    export_Billing_Success: emptyProps(),
    export_Billing_Failure: errorProps(),
    load_Billing_History: props<{ id: string }>(),
    load_Billing_History_Success: props<{
      billingHistory: BillingHistory[];
    }>(),
    load_Billing_History_Failure: errorProps(),
    download_Billing_History: props<{ billingHistory: BillingHistory }>(),
    download_Billing_History_Success: emptyProps(),
    download_Billing_History_Failure: errorProps(),
    compare_Billing_History: props<{ billingHistory: BillingHistory[] }>(),
    compare_Billing_History_Success: emptyProps(),
    compare_Billing_History_Failure: errorProps(),
  },
});
