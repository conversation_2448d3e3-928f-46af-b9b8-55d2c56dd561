import { AssetHistory } from 'libs/services/src/lib/services/maintenance/interfaces/asset-history.interface';
import { Asset } from 'libs/services/src/lib/services/maintenance/interfaces/asset.interface';

export type AssetsMDState = {
  isVisibleAddEdit: boolean;
  isVisibleHistory: boolean;
  isVisibleMobileWell: boolean;
  assetsHistory: AssetHistory[];
  asset: Asset | null;
};

export const initialAssetsMDState: AssetsMDState = {
  isVisibleAddEdit: false,
  isVisibleHistory: false,
  isVisibleMobileWell: false,
  assetsHistory: [],
  asset: null,
};
