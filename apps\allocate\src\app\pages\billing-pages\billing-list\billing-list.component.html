<div class="d-flex justify-content-between mb-40">
  <b class="fs-24">Billings</b>

  <div class="d-flex gap-20">
    <span class="p-input-icon-left">
      <em class="pi pi-search"></em>
      <input
        type="text"
        pInputText
        [(ngModel)]="searchValue"
        placeholder="Search..."
        (input)="table.filterGlobal(searchValue, 'contains')"
      />
    </span>

    <button
      type="button"
      class="btn-export align-items-center d-flex"
      (click)="export()"
    >
      <img src="assets/icons/exel.svg" />
      Export
    </button>
  </div>
</div>

<p-table
  #table
  [columns]="listColumns"
  [value]="billings"
  [scrollable]="true"
  scrollHeight="calc(100vh - 273px)"
  [rowsPerPageOptions]="[10, 25, 50]"
  [paginator]="true"
  [rows]="10"
  [filterDelay]="0"
  [loading]="loading().list"
  [globalFilterFields]="[
    tableFields.billingPeriodMonth,
    tableFields.status,
    tableFields.dateCompleted,
    tableFields.numberOfVoyages,
    tableFields.numberOfVessels,
    tableFields.numberOfOperators,
  ]"
>
  <ng-template pTemplate="header" let-columns>
    <tr>
      <th
        *ngFor="let column of columns"
        [style.min-width.px]="column.width"
        [style.width.%]="(column.width / tableWidth) * 100"
        [pSortableColumn]="column.field"
        [pSortableColumnDisabled]="!column.sortable"
      >
        <span>{{ column.name }}</span>
        <p-sortIcon *ngIf="column.sortable" [field]="column.field" />
      </th>
    </tr>
  </ng-template>
  <ng-template
    let-index="rowIndex"
    pTemplate="body"
    let-row
    let-columns="columns"
  >
    <tr>
      <ng-container *ngFor="let column of columns">
        <ng-container [ngSwitch]="column.field">
          <td *ngSwitchCase="tableFields.recalculate">
            <div class="d-flex justify-content-center">
              <em *ngIf="row.recalculate" class="icon-red-info"></em>
            </div>
          </td>
          <td *ngSwitchCase="tableFields.dateCompleted">
            {{ row.dateCompleted | date : 'dd/MM/yyyy HH:mm' }}
          </td>
          <td *ngSwitchCase="tableFields.billingPeriodMonth">
            <a class="link" [routerLink]="[row.billingPeriodId]">{{
              row.billingPeriodMonth
            }}</a>
          </td>
          
          <td *ngSwitchCase="tableFields.status">
            <div class="d-flex gap-5 flex-wrap">
              <lha-custom-chip
                [size]="150"
                [text]="row.status"
                [toUppercase]="false"
              ></lha-custom-chip>
            </div>
          </td>
          <td *ngSwitchCase="tableFields.actions">
            <div class="d-flex gap-8 flex-wrap">
              <button
                type="button"
                class="btn-icon-only"
                *lhaHasPermission="[
                  userRole.SupportUser,
                  userRole.BillingAdmin
                ]"
                (click)="billingService.toggleBillingPeriodLock(row)"
              >
                <em
                  class="pi"
                  [ngClass]="row.isLocked ? 'pi-lock' : 'pi-lock-open'"
                ></em>
              </button>
              <button
                type="button"
                class="btn-icon-only"
                (click)="openBillingHistory(row)"
              >
                <em class="pi pi-history"></em>
              </button>
            </div>
          </td>
          <td *ngSwitchDefault>{{ row[column.field] }}</td>
        </ng-container>
      </ng-container>
    </tr>
  </ng-template>
  <ng-template pTemplate="emptymessage">
    <tr>
      <td colspan="7" style="text-align: center">No results found</td>
    </tr>
  </ng-template>
</p-table>

<lha-billing-history/>
