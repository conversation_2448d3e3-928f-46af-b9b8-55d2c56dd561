import { ColumnModel } from 'libs/components/src/lib/models/column.model';
import { BillingDetailTableFields } from '../enums/billing-detail-table-fields.enum';

export function InitializeBillingsDetailTable(): ColumnModel[] {
  const columns = [
    new ColumnModel(BillingDetailTableFields.voyageNotValid, '', 80),
    new ColumnModel(
      BillingDetailTableFields.voyageNumber,
      'Voyage Number',
      150,
      {
        sortable: true,
      }
    ),
    new ColumnModel(BillingDetailTableFields.vesselName, 'Vessel', 150, {
      sortable: true,
    }),
    new ColumnModel(
      BillingDetailTableFields.voyageStartDateTime,
      'Start Date',
      150,
      {
        sortable: true,
      }
    ),
    new ColumnModel(
      BillingDetailTableFields.voyageEndDateTime,
      'End Date',
      150,
      { sortable: true }
    ),
    new ColumnModel(
      BillingDetailTableFields.distanceSailed,
      'Miles Sailed',
      150,
      {
        sortable: true,
      }
    ),
    new ColumnModel(
      BillingDetailTableFields.deckPercentageUsedIn,
      'Deck % Used In',
      150,
      { sortable: true }
    ),
    new ColumnModel(
      BillingDetailTableFields.deckPercentageUsedOut,
      'Deck % Used Out',
      150,
      { sortable: true }
    ),
    new ColumnModel(BillingDetailTableFields.voyageStatus, 'Completed', 150, {
      sortable: true,
    }),
  ];
  return columns;
}
