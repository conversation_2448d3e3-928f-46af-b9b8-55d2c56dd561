import { ColumnModel } from 'libs/components/src/lib/models/column.model';
import { BillingTableFields } from '../enums/billing-table-fields.enum';

export function InitializeBillingsTable(): ColumnModel[] {
  const columns = [
    new ColumnModel(BillingTableFields.recalculate, '', 80),
    new ColumnModel(
      BillingTableFields.billingPeriodMonth,
      'Billing Period',
      150,
      {
        sortable: true,
      }
    ),
    new ColumnModel(BillingTableFields.status, 'Status', 170, {
      sortable: true,
    }),
    new ColumnModel(BillingTableFields.dateCompleted, 'Date Complete', 180, {
      sortable: true,
    }),
    new ColumnModel(
      BillingTableFields.numberOfVoyages,
      'No. of Completed Voyages',
      180,
      { sortable: true }
    ),
    new ColumnModel(BillingTableFields.numberOfVessels, 'No. of Vessels', 180, {
      sortable: true,
    }),
    new ColumnModel(
      BillingTableFields.numberOfOperators,
      'No. of Operators',
      180,
      { sortable: true }
    ),
    new ColumnModel(BillingTableFields.actions, '', 100),
  ];
  return columns;
}
