import {
  AfterViewInit,
  DestroyRef,
  Directive,
  ElementRef,
  inject,
  Renderer2,
} from '@angular/core';
import { Store } from '@ngrx/store';
import { voyagesFeature } from 'libs/services/src/lib/services/voyages/store/features';
import { VoyageStatus } from 'libs/components/src/lib/enums/voyage-status.enum';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

@Directive({
  selector: '[lhaIsVoyageLockedDisable]',
  standalone: true,
})
export class IsVoyageLockedDisableDirective
  implements AfterViewInit
{
  private readonly store = inject(Store);
  private readonly elementRef = inject(ElementRef<any>);
  private readonly renderer = inject(Renderer2);
  private readonly destroyRef = inject(DestroyRef)
  voyageStatus = VoyageStatus;

  ngAfterViewInit(): void {
    this.store
      .select(voyagesFeature.selectActiveVoyage)
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((res) => {
        this.renderer.setProperty(
          this.elementRef.nativeElement,
          'disabled',
          (res && res.isVoyageLocked || res?.voyageStatus === this.voyageStatus.Completed)
        );
      });
  }
}
