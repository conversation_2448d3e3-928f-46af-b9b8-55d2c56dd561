import { Component, inject, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Async<PERSON><PERSON><PERSON>, NgForOf, NgIf } from '@angular/common';
import {
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { LoadingDirective } from 'libs/components/src/lib/directives/loading.directive';
import { MatButtonModule } from '@angular/material/button';
import {
  MAT_DIALOG_DATA,
  MatDialogModule,
  MatDialogRef,
} from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatOptionModule } from '@angular/material/core';
import { MatSelectModule } from '@angular/material/select';
import { HireStatement } from 'libs/services/src/lib/services/vessels/interfaces/hire-statement.interface';
import { Store } from '@ngrx/store';
import { Actions, ofType } from '@ngrx/effects';
import { hireStatementFeature } from 'libs/services/src/lib/services/vessels/store/features';
import { Subject, takeUntil } from 'rxjs';
import { HireStatementActions } from 'libs/services/src/lib/services/vessels/store/actions/hire-statement.actions';
import { ActivatedRoute } from '@angular/router';
import { UtilityService } from 'libs/services/src/lib/services/utility.service';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { NgxMatDatetimePickerModule } from '@angular-material-components/datetime-picker';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import {
  timeAfter,
  timeBefore,
  getDefaultTime,
} from 'libs/components/src/lib/functions/utility.functions';
import { decimalPoint } from 'libs/components/src/lib/validators/decimal-point';
import { greaterThan } from 'libs/components/src/lib/validators/greaterThan';
import { OnlyDigitsDirective } from 'libs/components/src/lib/directives/only-digits.directive';
import { SingleSelectComponent } from 'libs/components/src/lib/components/single-select/single-select.component';
import { CdkDrag, CdkDragHandle } from '@angular/cdk/drag-drop';
import { NgxMatDatepickerInputEvent } from '@angular-material-components/datetime-picker/lib/datepicker-input-base';

@Component({
  selector: 'lha-hire-statement-add-edit',
  standalone: true,
  imports: [
    FormsModule,
    LoadingDirective,
    MatButtonModule,
    MatDialogModule,
    MatFormFieldModule,
    MatInputModule,
    MatOptionModule,
    MatSelectModule,
    NgForOf,
    ReactiveFormsModule,
    AsyncPipe,
    NgIf,
    MatDatepickerModule,
    NgxMatDatetimePickerModule,
    MatSlideToggleModule,
    OnlyDigitsDirective,
    SingleSelectComponent,
    CdkDrag,
    CdkDragHandle,
  ],
  templateUrl: './hire-statement-add-edit.component.html',
  styleUrls: ['./hire-statement-add-edit.component.scss'],
})
export class HireStatementAddEditComponent implements OnInit, OnDestroy {
  dialogRef = inject(MatDialogRef<HireStatementAddEditComponent>);
  data: { hireStatement: HireStatement } = inject(MAT_DIALOG_DATA);
  activatedRoute = inject(ActivatedRoute);
  utilityService = inject(UtilityService);
  store = inject(Store);
  action = inject(Actions);
  vm$ = this.store.select(hireStatementFeature.selectHireStatementsState);
  vesselId = this.utilityService.getParamsFromRoute(
    this.activatedRoute.snapshot.root
  )['id'];
  isAdd = true;
  unsubscribe: Subject<boolean> = new Subject();
  hireStatement!: HireStatement;
  form = new FormGroup({
    type: new FormControl<string>('', [Validators.required]),
    isOnHire: new FormControl<boolean>(true, [Validators.required]),
    deliveryDate: new FormControl<Date | null>(null, [Validators.required]),
    deliveryPlace: new FormControl<string>('', [Validators.required]),
    redeliveryDate: new FormControl<Date | null>(null, [Validators.required]),
    redeliveryPlace: new FormControl<string>('', [Validators.required]),
    dayRate: new FormControl<number | null>(null, [
      Validators.required,
      greaterThan(0),
      decimalPoint(2),
    ]),
  });
  typeList = [
    {
      name: 'Spot',
    },
    {
      name: 'Term',
    },
  ];
  isOnHireList = [
    {
      name: 'Yes',
      value: true,
    },
    {
      name: 'No',
      value: false,
    },
  ];
  timeAfter: Date | null = null;
  timeBefore: Date | null = null;
  minDifference = 1;
  defaultTime = getDefaultTime({ sec: 0 });

  ngOnInit(): void {
    this.subCloseDialog();
    this.initAddEdit();
  }

  ngOnDestroy(): void {
    this.unsubscribe.next(true);
    this.unsubscribe.complete();
  }

  private initAddEdit(): void {
    this.isAdd = !this.data.hireStatement;
    if (!this.isAdd) {
      this.hireStatement = this.data.hireStatement;
      this.pathForm(this.hireStatement);

      this.timeBefore = timeBefore(this.hireStatement.deliveryDate, {
        min: this.minDifference,
      });
      this.timeAfter = timeAfter(this.hireStatement.redeliveryDate, {
        min: this.minDifference,
      });

      if (this.hireStatement.redeliveryDate < this.hireStatement.deliveryDate) {
        this.form.controls.redeliveryDate.setValue(null);
      }
    }
  }

  private subCloseDialog(): void {
    this.action
      .pipe(
        ofType(
          HireStatementActions.add_Hire_Statement_Success,
          HireStatementActions.edit_Hire_Statement_Success
        )
      )
      .pipe(takeUntil(this.unsubscribe))
      .subscribe(() => {
        this.dialogRef.close();
      });
  }

  private pathForm(hireStatement: HireStatement): void {
    this.form.patchValue({
      ...hireStatement,
    });
  }

  saveHireStatement(): void {
    if (this.form.invalid) {
      this.form.markAllAsTouched();
      return;
    }

    const model = {
      ...this.form.value,
      vesselId: this.vesselId,
    } as HireStatement;

    if (this.isAdd) {
      this.store.dispatch(
        HireStatementActions.add_Hire_Statement({ hireStatement: model })
      );
    } else {
      this.store.dispatch(
        HireStatementActions.edit_Hire_Statement({
          hireStatementId: this.hireStatement.hireStatementId,
          hireStatement: model,
        })
      );
    }
  }

  changeDeliveryDate(event: NgxMatDatepickerInputEvent<Date>): void {
    this.timeBefore = timeBefore(event.value, { min: this.minDifference });
  }

  changeRedeliveryDate(event: NgxMatDatepickerInputEvent<Date>): void {
    this.timeAfter = timeAfter(event.value, { min: this.minDifference });
  }
}
