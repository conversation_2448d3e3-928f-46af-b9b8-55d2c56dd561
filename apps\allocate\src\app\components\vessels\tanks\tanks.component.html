<lha-table
  *ngIf="tanksState()"
  [columns]="columns"
  [loading]="tanksState().loading.list"
  [exportLoading]="tanksState().loading.export"
  [data]="tanksState().vesselTanks"
  [canExportTable]="true"
  [pageSize]="query.pageSize"
  tableName="vessel-tanks"
  (queryChange)="onChangeQuery($event)"
  (exportTable)="exportVesselTanks()"
>
  <h3 class="mat-title--warn" tableHeaderLeft>Tanks</h3>
  <button
    tableHeaderRightBtn
    mat-raised-button
    (click)="addEditVesselTank(null)"
    color="primary"
  >
    Add
  </button>
  <ng-template lhaCellTemplate="tankStatusChangeDate" let-item>
    {{ item.tankStatusChangeDate | date : 'dd/MM/yyyy HH:mm' }}
  </ng-template>
  <ng-template lhaCellTemplate="createdByName" let-item>
    {{ item.createdByName }} {{ item.createdDate | date : 'dd/MM/yyyy HH:mm' }}
  </ng-template>
  <ng-template lhaCellTemplate="updatedByName" let-item>
    {{ item.updatedByName }} {{ item.updatedDate | date : 'dd/MM/yyyy HH:mm' }}
  </ng-template>
  <ng-template lhaCellTemplate="tankStatusChangeDate" let-item>
    {{ item.tankStatusChangeDate }}
  </ng-template>
  <ng-template lhaCellTemplate="cleaned" let-item>
    <mat-icon
      [ngClass]="item.cleaned ? 'mat-icon--accept' : 'mat-icon--cancel'"
      >{{ item.cleaned ? 'check_circle' : 'cancel' }}</mat-icon
    >
  </ng-template>
  <ng-template lhaCellTemplate="vesselTankId" let-item>
    <button mat-icon-button color="primary" (click)="addEditVesselTank(item)">
      <mat-icon>edit</mat-icon>
    </button>
    <button mat-icon-button color="warn" (click)="removeVesselTank(item)">
      <mat-icon>delete</mat-icon>
    </button>
  </ng-template>
</lha-table>
