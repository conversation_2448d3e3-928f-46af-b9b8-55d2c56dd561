<p-dialog
  [draggable]="true"
  [closable]="true"
  [modal]="true"
  [visible]="isVisible()"
  (visibleChange)="hideDialog()"
  [style]="{ width: '600px' }"
>
  <ng-template pTemplate="header">
    <div class="header">
      {{ activity() ? 'Edit' : 'Add' }} Activity

      <em
        *ngIf="activity()"
        class="pi pi-info-circle"
        [pTooltip]="tooltipContent"
      ></em>
      <ng-template #tooltipContent>
        <div
          class="d-flex flex-direction-column gap-4 fs-12"
          *ngIf="activity()?.createdByName"
        >
          <span>Created By: {{ activity()?.createdByName }}</span>
          <span
            >Created Date:
            {{ activity()?.createdDate | date : 'dd/MM/yyyy HH:mm' }}</span
          >
        </div>

        <div
          class="d-flex flex-direction-column gap-4 fs-12"
          *ngIf="activity()?.updatedByName"
        >
          <span>Last Updated By: {{ activity()?.updatedByName }}</span>
          <span
            >Updated Date:
            {{ activity()?.updatedDate | date : 'dd/MM/yyyy HH:mm' }}</span
          >
        </div>
      </ng-template>
    </div>
  </ng-template>
  <ng-template pTemplate="content">
    <form [formGroup]="form" class="d-flex flex-wrap gap-16">
      <div class="d-flex w-100 justify-content-between gap-8">
        <div class="d-flex w-50 flex-direction-column gap-4">
          <label class="fs-14">Activity Name</label>
          <input pInputText type="text" formControlName="name" />
          <small
            class="validation-control-error"
            *ngIf="controls.name?.invalid && controls.name?.touched"
          >
            Activity Name is required.
          </small>
        </div>
        <div class="d-flex w-50 flex-direction-column gap-4">
          <label class="fs-14">Activity Code</label>
          <input pInputText type="text" formControlName="code" />
          <small
            class="validation-control-error"
            *ngIf="controls.code?.invalid && controls.code?.touched"
          >
            Activity Code is required.
          </small>
        </div>
      </div>
      <div class="d-flex w-100 justify-content-between gap-8">
        <div class="d-flex w-50 flex-direction-column gap-4">
          <label class="fs-14">Activity Type</label>
          <p-dropdown
            [options]="activityTypeList()"
            [filter]="true"
            [showClear]="true"
            formControlName="type"
            optionLabel="description"
            optionValue="value"
            appendTo="body"
            styleClass="new-version"
            panelStyleClass="new-version-panel"
          />
          <small
            class="validation-control-error"
            *ngIf="controls.type?.invalid && controls.type?.touched"
          >
            Activity Type is required.
          </small>
        </div>
        <div class="d-flex w-50 flex-direction-column gap-4">
          <label class="fs-14">Chargeability</label>
          <p-dropdown
            [options]="chargeabilityList()"
            [filter]="true"
            [showClear]="true"
            formControlName="chargeability"
            optionLabel="description"
            optionValue="value"
            appendTo="body"
            styleClass="new-version"
            panelStyleClass="new-version-panel"
          />
          <small
            class="validation-control-error"
            *ngIf="
              controls.chargeability?.invalid && controls.chargeability?.touched
            "
          >
            Chargeability is required.
          </small>
        </div>
      </div>
      <div class="d-flex w-100 justify-content-between gap-8">
        <div class="d-flex w-50 flex-direction-column gap-4">
          <label class="fs-14">Parallel Activities</label>
          <p-multiSelect
            #multiSelectActivities
            inputId="float-voyage-sharer"
            [options]="activities()"
            formControlName="parallelActivityIds"
            optionLabel="name"
            optionValue="activityId"
            [showToggleAll]="!multiSelectActivities.isEmpty()"
            display="comma"
            [showClear]="!!controls.parallelActivityIds?.value?.length"
            appendTo="body"
            styleClass="new-version-multiselect"
            panelStyleClass="new-version-panel"
          ></p-multiSelect>
        </div>
      </div>
    </form>
  </ng-template>
  <ng-template pTemplate="footer">
    <div class="actions">
      <button class="btn-tertiary" type="button" (click)="hideDialog()">
        Cancel
      </button>
      <button class="btn-primary" type="button" (click)="onSubmit()">
        {{ activity() ? 'Save' : 'Add' }}
      </button>
    </div>
  </ng-template>
</p-dialog>
