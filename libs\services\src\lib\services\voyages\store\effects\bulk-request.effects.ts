import { Actions, concatLatestFrom, createEffect, ofType } from '@ngrx/effects';
import { inject } from '@angular/core';
import { catchError, map, mergeMap, of } from 'rxjs';
import { HttpErrorResponse } from '@angular/common/http';
import { BulkRequestActions } from '../actions/bulk-request.actions';
import { BulkType } from '../../../maintenance/interfaces/bulk-type.interface';
import { MatDialog } from '@angular/material/dialog';
import { filter, tap } from 'rxjs/operators';
import { FileService } from '../../../file.service';
import { Store } from '@ngrx/store';
import { bulkRequestFeature } from '../features';
import { BulkRequestService } from '../../bulk-request.service';
import { UtilityService } from '../../../utility.service';
import { ActivatedRoute } from '@angular/router';
import { DialogOptions } from '../../../config/dialog-options';
import { BulkRequest } from '../../interfaces/bulk-request.interface';
import { Asset } from '../../../maintenance/interfaces/asset.interface';
import { RequestRequestAddEditComponent } from '../../../../../../../../apps/allocate/src/app/components/voyages/bulk-request-add-edit/bulk-request-add-edit.component';

export const loadBulkRequestList = createEffect(
  (
    actions = inject(Actions),
    store = inject(Store),
    utilityService = inject(UtilityService),
    bulkRequestService = inject(BulkRequestService),
    activatedRoute = inject(ActivatedRoute)
  ) => {
    return actions.pipe(
      ofType(
        BulkRequestActions.load_Bulk_Request_Lists,
        BulkRequestActions.update_Bulk_Request_Queries,
        BulkRequestActions.remove_Bulk_Request_Success,
        BulkRequestActions.add_Bulk_Request_Success,
        BulkRequestActions.edit_Bulk_Request_Success
      ),
      filter(
        () =>
          !!utilityService.getParamsFromRoute(activatedRoute.snapshot.root)[
            'id'
          ]
      ),
      concatLatestFrom(() => store.select(bulkRequestFeature.selectQuery)),
      map(([, res]) => {
        return {
          query: res,
          voyageId: utilityService.getParamsFromRoute(
            activatedRoute.snapshot.root
          )['id'],
        };
      }),
      mergeMap((action) =>
        bulkRequestService.loadBulkRequests(action.query, action.voyageId).pipe(
          map((res: BulkRequest[]) =>
            BulkRequestActions.load_Bulk_Requests_Success({ bulkRequests: res })
          ),
          catchError((error: HttpErrorResponse) =>
            of(BulkRequestActions.load_Bulk_Requests_Failure({ error }))
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const removeBulkRequest = createEffect(
  (
    actions = inject(Actions),
    bulkRequestService = inject(BulkRequestService)
  ) => {
    return actions.pipe(
      ofType(BulkRequestActions.remove_Bulk_Request),
      mergeMap((action) =>
        bulkRequestService.removeBulkRequest(action.id).pipe(
          map((res: BulkRequest) =>
            BulkRequestActions.remove_Bulk_Request_Success({
              bulkRequest: res,
              successMessage: 'Bulk Request removed successfully!',
            })
          ),
          catchError((error: HttpErrorResponse) =>
            of(BulkRequestActions.remove_Bulk_Request_Failure({ error: error }))
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const addBulkRequest = createEffect(
  (
    actions = inject(Actions),
    bulkRequestService = inject(BulkRequestService)
  ) => {
    return actions.pipe(
      ofType(BulkRequestActions.add_Bulk_Request),
      mergeMap((action) =>
        bulkRequestService.addBulkRequest(action.bulkRequest).pipe(
          map((res: BulkRequest) =>
            BulkRequestActions.add_Bulk_Request_Success({
              bulkRequest: res,
              successMessage: 'Bulk Request added successfully!',
            })
          ),
          catchError((error: HttpErrorResponse) =>
            of(BulkRequestActions.add_Bulk_Request_Failure({ error: error }))
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const editBulkRequest = createEffect(
  (
    actions = inject(Actions),
    bulkRequestService = inject(BulkRequestService)
  ) => {
    return actions.pipe(
      ofType(BulkRequestActions.edit_Bulk_Request),
      mergeMap((action) =>
        bulkRequestService.editBulkRequest(action.id, action.bulkRequest).pipe(
          map((res: BulkRequest) =>
            BulkRequestActions.edit_Bulk_Request_Success({
              bulkRequest: res,
              successMessage: 'Bulk Request edited successfully!',
            })
          ),
          catchError((error: HttpErrorResponse) =>
            of(BulkRequestActions.edit_Bulk_Request_Failure({ error: error }))
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const exportBulkRequests = createEffect(
  (
    actions = inject(Actions),
    bulkRequestService = inject(BulkRequestService),
    fileService = inject(FileService),
    utilityService = inject(UtilityService),
    activatedRoute = inject(ActivatedRoute)
  ) => {
    return actions.pipe(
      ofType(BulkRequestActions.export_Bulk_Requests),
      map(() =>
        utilityService.getParamsFromRoute(activatedRoute.snapshot.root)
      ),
      mergeMap((params) =>
        bulkRequestService.exportBulkRequests(params['id']).pipe(
          map((res: ArrayBuffer) => {
            fileService.downloadFile(res, 'Bulk Requests');
            return BulkRequestActions.export_Bulk_Requests_Success();
          }),
          catchError((error: HttpErrorResponse) =>
            of(BulkRequestActions.export_Bulk_Requests_Failure({ error }))
          )
        )
      )
    );
  },
  {
    // You can omit this part if not needed
    // This indicates that the effect is a pure function and can be tree-shaken during optimization
    functional: true,
  }
);

export const loadBulkRequestTypeList = createEffect(
  (
    actions = inject(Actions),
    bulkRequestService = inject(BulkRequestService)
  ) => {
    return actions.pipe(
      ofType(BulkRequestActions.load_Bulk_Request_Lists),
      mergeMap(() =>
        bulkRequestService.loadBulkTypesList().pipe(
          map((res: BulkType[]) =>
            BulkRequestActions.load_Bulk_Type_List_Success({
              bulkTypeList: res,
            })
          ),
          catchError((error: HttpErrorResponse) =>
            of(BulkRequestActions.load_Bulk_Type_List_Failure({ error }))
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const loadBulkRequestOperatorList = createEffect(
  (
    actions = inject(Actions),
    bulkRequestService = inject(BulkRequestService)
  ) => {
    return actions.pipe(
      ofType(BulkRequestActions.load_Bulk_Request_Lists),
      mergeMap(() =>
        bulkRequestService.loadAssets().pipe(
          map((res: Asset[]) =>
            BulkRequestActions.load_Asset_List_Success({ assets: res })
          ),
          catchError((error: HttpErrorResponse) =>
            of(BulkRequestActions.load_Asset_List_Failure({ error }))
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const openBulkRequestDialog = createEffect(
  (
    actions = inject(Actions),
    dialog = inject(MatDialog),
    dialogOptions = inject(DialogOptions)
  ) => {
    return actions.pipe(
      ofType(BulkRequestActions.open_Bulk_Request_Dialog),
      tap((action) => {
        dialog.open(RequestRequestAddEditComponent, {
          ...dialogOptions,
          data: {
            bulkRequest: action.bulkRequest,
          },
        });
      })
    );
  },
  {
    functional: true,
    dispatch: false,
  }
);
