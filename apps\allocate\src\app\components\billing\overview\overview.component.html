<p-card class="mt-16" *ngIf="billing()">
  <ng-template pTemplate="header">
    <div class="d-flex justify-content-between gap-20">
      <div class="d-flex gap-20 align-items-center flex-wrap">
        <div class="d-flex gap-4">
          <strong>Start Date / Time:</strong>
          <div>
            {{ transformedBilling()?.periodStartDate | date : 'dd-MM-yy HH:mm' }}
          </div>
        </div>
        <div class="d-flex gap-4">
          <strong>Stop Date / Time:</strong>
          <div>
            {{ transformedBilling()?.periodEndDate | date : 'dd-MMMM-yy HH:mm' }}
          </div>
        </div>
      </div>
      <div class="d-flex justify-content-end gap-8 flex-wrap">
        <button
          [ngClass]="
            transformedBilling()?.isLocked ? 'btn-secondary' : 'btn-negative-primary'
          "
          *lhaHasPermission="[userRole.SupportUser, userRole.BillingAdmin]"
          (click)="billingService.toggleBillingPeriodLock(billing())"
          [disabled]="loading().list"
          type="button"
        >
          <em
            class="pi"
            [ngClass]="transformedBilling()?.isLocked ? 'pi-lock' : 'pi-lock-open'"
          ></em>
          {{ transformedBilling()?.isLocked ? 'Unlock' : 'Lock' }}
        </button>

        <button class="btn-secondary" type="button">
          <em class="pi pi-download"></em>
          Reports
        </button>

        <button
          type="button"
          class="btn-secondary"
          (click)="calcBillingPeriod()"
          [disabled]="!!transformedBilling()?.isLocked"
          *lhaHasPermission="[userRole.SupportUser, userRole.BillingAdmin]"
          pBadge
          severity="danger"
          value=" "
          [badgeDisabled]="!billing()?.recalculate"
          badgeSize="small"
        >
          Calculate
        </button>
      </div>
    </div>
  </ng-template>

  <div class="d-flex flex-wrap gap-16">
    <lha-operator-card
      class="w-20"
      [style.minWidth.px]="250"
      *ngFor="let item of billingOperators()"
      [billingOperator]="item"
      [currency]="appSettings()?.currency ?? ''"
    ></lha-operator-card>
    <ng-container *ngIf="!billingOperators()?.length">
      No results found
    </ng-container>
  </div>
</p-card>

<lha-billing-calculation />
