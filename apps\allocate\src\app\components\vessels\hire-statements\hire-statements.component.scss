.hire_statement {
  height: 100%;
  display: flex;
  justify-content: space-between;
  &__left {
    width: 66%;
  }
  &__right {
    width: 33%;
  }
  mat-checkbox {
    pointer-events: none;
  }
  button {
    z-index: 1;
  }
}
@media (max-width: 1100px) {
  .hire_statement {
    flex-direction: column;
    justify-content: flex-start;
    row-gap: 2%;
    &__left {
      width: 100%;
      max-height: 49%;
    }
    &__right {
      width: 100%;
      max-height: 49%;
    }
  }
}
