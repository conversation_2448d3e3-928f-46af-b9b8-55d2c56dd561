import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  DestroyRef,
  inject,
  OnInit,
} from '@angular/core';
import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { DomSanitizer, SafeUrl } from '@angular/platform-browser';
import { NgIf } from '@angular/common';

import { Actions, ofType } from '@ngrx/effects';
import { Store } from '@ngrx/store';

import { ConfirmationService } from 'primeng/api';
import { DialogModule } from 'primeng/dialog';
import { MultiSelectModule } from 'primeng/multiselect';
import { InputTextModule } from 'primeng/inputtext';
import { InputSwitchModule } from 'primeng/inputswitch';

import { OperatorActions } from 'libs/services/src/lib/services/maintenance/store/actions/operators.actions';
import { objectToFormData } from 'libs/components/src/lib/functions/utility.functions';
import { ToastService } from 'libs/components/src/lib/services/toast.service';
import {
  locationsFeature,
  operatorsFeature,
} from 'libs/services/src/lib/services/maintenance/store/features';
import {
  filePatternValidator,
  openFileSelector,
} from 'libs/components/src/lib/functions/file-helpers';
import { CroppingImageDialogComponent } from 'libs/components/src/lib/components/cropping-image-dialog/cropping-image-dialog.component';

@Component({
  standalone: true,
  selector: 'md-operators-add-edit',
  templateUrl: './operators-add-edit.component.html',
  imports: [
    DialogModule,
    MultiSelectModule,
    ReactiveFormsModule,
    InputTextModule,
    InputSwitchModule,
    NgIf,
    CroppingImageDialogComponent,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class OperatorsAddEditComponent implements OnInit {
  private readonly destroyRef = inject(DestroyRef);
  private readonly actions = inject(Actions);
  private readonly store = inject(Store);
  private readonly toastService = inject(ToastService);
  private readonly confirmationService = inject(ConfirmationService);
  private readonly cdr = inject(ChangeDetectorRef);
  private readonly sanitizer = inject(DomSanitizer);

  operator = this.store.selectSignal(operatorsFeature.selectOperator);
  isVisible = this.store.selectSignal(operatorsFeature.selectIsVisibleAddEdit);
  locations = this.store.selectSignal(locationsFeature.selectLocations);

  form = new FormGroup({
    name: new FormControl<string>('', [Validators.required]),
    isActive: new FormControl<boolean>(false),
    locationIds: new FormControl<string[]>([], [Validators.required]),
    customsCompliant: new FormControl<boolean>(false),
    vatNumber: new FormControl<string>(''),
    euNumber: new FormControl<string>(''),
    picture: new FormControl<SafeUrl>(''),
    hasImage: new FormControl<boolean>(false),
  });

  controls = {
    locationIds: this.form.get('locationIds'),
    name: this.form.get('name'),
  };

  logCtrl = this.form.controls.picture;
  visibleCropDialog = false;
  imageChangedEvent: Event | null = null;
  accept = '.jpeg, .jpg, .png';
  photoUrl: SafeUrl = '';

  ngOnInit() {
    this.actions
      .pipe(
        ofType(OperatorActions.change_visibility_add_edit),
        takeUntilDestroyed(this.destroyRef)
      )
      .subscribe(({ operator }) => {
        this.form.reset();
        this.form.markAsUntouched();
        this.form.get('isActive')?.setValue(true);
        if (operator != null) {
          this.form.patchValue({
            ...operator,
            hasImage: !!operator.clientLogoId,
          });
          if (operator.clientLogoId) {
            this.store.dispatch(
              OperatorActions.load_Client_Logo({
                clientLogoId: operator.clientLogoId!,
              })
            );
          }
        }
      });

    this.actions
      .pipe(ofType(OperatorActions.load_Client_Logo_Success))
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(({ clientLogo }) => {
        this.photoUrl = this.sanitizer.bypassSecurityTrustUrl(
          URL.createObjectURL(clientLogo)
        );
      });
  }

  hideDialog() {
    this.photoUrl = '';
    this.store.dispatch(
      OperatorActions.change_visibility_add_edit({
        visible: false,
        operator: null,
      })
    );
  }

  onSubmit() {
    this.form.markAllAsTouched();
    if (this.form.valid) {
      const model = objectToFormData(this.form.value);
      if (this.operator()) {
        this.store.dispatch(
          OperatorActions.edit_Operator({
            operator: model,
            operatorId: this.operator()!.clientId,
          })
        );
      } else {
        this.store.dispatch(
          OperatorActions.add_Operator({
            operator: model,
          })
        );
      }
    }
  }

  async selectFile() {
    this.imageChangedEvent = (await openFileSelector(this.accept)) as Event;
    const file = (this.imageChangedEvent as any).target.files[0];
    const pattern = '(.*?).(jpg|jpeg|png)$';
    if (filePatternValidator(file, pattern) && file.size <= 5 * 1024 * 1024) {
      this.visibleCropDialog = true;
    } else {
      if (!filePatternValidator(file, pattern)) {
        this.toastService.error(
          'Unsupported file format. Please upload a .png, .jpg or .jpeg file.'
        );
      } else {
        this.toastService.error(
          'File size exceeds the 5 MB limit. Please upload a smaller file.'
        );
      }
    }

    this.cdr.markForCheck();
  }

  removePhoto() {
    this.confirmationService.confirm({
      header: 'Delete',
      message:
        'Are you sure you want to remove your photo? We will replace it with default avatar.',
      acceptLabel: 'Remove',
      rejectLabel: 'Cancel',
      acceptButtonStyleClass: 'btn-negative-primary',
      rejectButtonStyleClass: 'btn-tertiary',
      acceptIcon: 'none',
      rejectIcon: 'none',
      accept: () => {
        this.logCtrl.setValue(null);
        this.form.get('hasImage')?.setValue(false);
        this.photoUrl = '';
        this.cdr.markForCheck();
      },
    });
  }

  saveImage(event: { blob: Blob; safeUrl: SafeUrl }) {
    this.toastService.success('Logo updated successfully');
    this.logCtrl.setValue(event.blob);
    this.form.get('hasImage')?.setValue(true);
    this.photoUrl = event.safeUrl as string;
    this.visibleCropDialog = false;
  }
}
